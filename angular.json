{
  "$schema": "./node_modules/@angular/cli/lib/config/schema.json",
  "version": 1,
  "newProjectRoot": "projects",
  "projects": {
    "remarq": {
      "projectType": "application",
      "schematics": {},
      "root": "projects/remarq/src",
      "sourceRoot": "projects/remarq/src",
      "prefix": "app",
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/remarq",
            "index": "projects/remarq/src/index.html",
            "main": "projects/remarq/src/main.ts",
            "polyfills": "projects/remarq/src/polyfills.ts",
            "tsConfig": "projects/remarq/tsconfig.app.json",
            "assets": [
              "projects/remarq/src/favicon.ico",
              "projects/remarq/src/assets",
              "projects/remarq/src/app",
              "projects/remarq/src/manifest.webmanifest",
              "projects/remarq/src/.well-known"
            ],
            "styles": [
              "node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/solid.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/regular.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/brands.scss",
              "projects/remarq/src/assets/bootstrap/bootstrap.scss",
              "node_modules/ng-uikit-pro-standard/assets/scss/mdb.scss",
              "node_modules/animate.css/animate.css",
              "node_modules/line-awesome/dist/line-awesome/css/line-awesome.min.css",
              "projects/remarq/src/app/third-party/dataTables/datatables.min.css",
              "node_modules/slick-carousel/slick/slick.scss",
              "node_modules/slick-carousel/slick/slick-theme.scss",
              {
                "input": "projects/remarq/src/styles.scss",
                "bundleName": "global-styles",
                "inject": false
              },
              {
                "input": "projects/remarq/src/app/components/app/app.component.scss",
                "bundleName": "app.component",
                "inject": false
              },
              {
                "input": "projects/remarq/src/assets/css/mobile.scss",
                "bundleName": "global-mobile",
                "inject": false
              },
              {
                "input": "projects/remarq/src/assets/css/variables.css",
                "bundleName": "global-variables",
                "inject": false
              },
              {
                "input": "projects/remarq/src/assets/sites/tradesales/css/client-theme.scss",
                "bundleName": "tradesales-theme",
                "inject": false
              },
              {
                "input": "projects/remarq/src/assets/sites/tradesales/css/variables.css",
                "bundleName": "tradesales-variables",
                "inject": false
              },
              {
                "input": "projects/remarq/src/assets/sites/motiox/css/client-theme.scss",
                "bundleName": "motiox-theme",
                "inject": false
              },
              {
                "input": "projects/remarq/src/assets/sites/motiox/css/variables.css",
                "bundleName": "motiox-variables",
                "inject": false
              }
            ],
            "scripts": [
              "node_modules/chart.js/dist/chart.umd.js",
              "node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.js",
              "node_modules/easy-pie-chart/dist/easypiechart.js",
              "node_modules/screenfull/dist/screenfull.js",
              "node_modules/hammerjs/hammer.min.js",
              "node_modules/jquery/dist/jquery.js",
              "node_modules/slick-carousel/slick/slick.min.js",
              "node_modules/datatables.net/js/jquery.dataTables.js",
              "node_modules/datatables.net-fixedcolumns/js/dataTables.fixedColumns.min.js"
            ],
            "vendorChunk": true,
            "extractLicenses": false,
            "buildOptimizer": false,
            "sourceMap": true,
            "optimization": false,
            "namedChunks": true,
            "serviceWorker": false
          },
          "configurations": {
            "localhost-dev": {
              "fileReplacements": [
                {
                  "replace": "projects/remarq/src/environments/environment.ts",
                  "with": "projects/remarq/src/environments/environment.remarq.localhost.ts"
                },
                {
                  "replace": "projects/common/shared/aws/aws-exports.js",
                  "with": "projects/common/shared/aws/aws-exports.remarq.development.js"
                }
              ],
              "buildOptimizer": false,
              "optimization": {
                "scripts": false,
                "styles": false,
                "fonts": false
              },
              "sourceMap": true,
              "aot": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "namedChunks": true
            },
            "localhost-live": {
              "fileReplacements": [
                {
                  "replace": "projects/remarq/src/environments/environment.ts",
                  "with": "projects/remarq/src/environments/environment.remarq.localhost.ts"
                },
                {
                  "replace": "projects/common/shared/aws/aws-exports.js",
                  "with": "projects/common/shared/aws/aws-exports.remarq.prod.js"
                }
              ],
              "buildOptimizer": false,
              "optimization": {
                "scripts": false,
                "styles": false,
                "fonts": false
              },
              "sourceMap": true,
              "aot": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "namedChunks": true
            },
            "development": {
              "fileReplacements": [
                {
                  "replace": "projects/remarq/src/environments/environment.ts",
                  "with": "projects/remarq/src/environments/environment.remarq.development.ts"
                },
                {
                  "replace": "projects/common/shared/aws/aws-exports.js",
                  "with": "projects/common/shared/aws/aws-exports.remarq.development.js"
                }
              ],
              "buildOptimizer": false,
              "optimization": {
                "scripts": false,
                "styles": false,
                "fonts": false
              },
              "sourceMap": true,
              "aot": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "namedChunks": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ]
            },
            "production": {
              "fileReplacements": [
                {
                  "replace": "projects/remarq/src/environments/environment.ts",
                  "with": "projects/remarq/src/environments/environment.remarq.prod.ts"
                },
                {
                  "replace": "projects/common/shared/aws/aws-exports.js",
                  "with": "projects/common/shared/aws/aws-exports.remarq.prod.js"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "aot": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ]
            },
            "mobile-development": {
              "fileReplacements": [
                {
                  "replace": "projects/remarq/src/environments/environment.ts",
                  "with": "projects/remarq/src/environments/environment.remarq.development.ts"
                },
                {
                  "replace": "projects/common/shared/aws/aws-exports.js",
                  "with": "projects/common/shared/aws/aws-exports.remarq.mobile.development.js"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "aot": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ]
            },
            "mobile-production": {
              "fileReplacements": [
                {
                  "replace": "projects/remarq/src/environments/environment.ts",
                  "with": "projects/remarq/src/environments/environment.remarq.prod.ts"
                },
                {
                  "replace": "projects/common/shared/aws/aws-exports.js",
                  "with": "projects/common/shared/aws/aws-exports.remarq.mobile.prod.js"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "aot": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ]
            }
          },
          "defaultConfiguration": "production"
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "options": {
            "host": "localhost",
            "port": 44322,
            "buildTarget": "remarq:build"
          },
          "configurations": {
            "production": {
              "buildTarget": "remarq:build:production"
            },
            "development": {
              "buildTarget": "remarq:build:development"
            },
            "sellmyride-development": {
              "buildTarget": "remarq:build:sellmyride-development"
            },
            "localhost-live": {
              "buildTarget": "remarq:build:localhost-live"
            },
            "localhost-dev": {
              "buildTarget": "remarq:build:localhost-dev"
            },
            "en-GB": {
              "buildTarget": "remarq:build:en-GB"
            }
          }
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "buildTarget": "remarq:build"
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "projects/remarq/src/test.ts",
            "polyfills": "projects/remarq/src/polyfills.ts",
            "tsConfig": "projects/remarq/tsconfig.spec.json",
            "karmaConfig": "karma.conf.js",
            "assets": [
              "projects/remarq/src/favicon.ico",
              "projects/remarq/src/assets",
              "projects/remarq/src/app",
              "projects/remarq/src/manifest.webmanifest"
            ],
            "styles": [
              "projects/remarq/src/styles.scss",
              "./node_modules/line-awesome/dist/line-awesome/css/line-awesome.min.css"
            ],
            "scripts": []
          }
        },
        "e2e": {
          "builder": "@angular-devkit/build-angular:protractor",
          "options": {
            "protractorConfig": "e2e/protractor.conf.js",
            "devServerTarget": "remarq:serve"
          },
          "configurations": {
            "production": {
              "devServerTarget": "remarq:serve:production"
            }
          }
        }
      }
    },
    "uinspect": {
      "projectType": "application",
      "root": "projects/uinspect/src",
      "sourceRoot": "projects/uinspect/src",
      "prefix": "app",
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/uinspect",
            "index": "projects/uinspect/src/index.html",
            "main": "projects/uinspect/src/main.ts",
            "polyfills": "projects/uinspect/src/polyfills.ts",
            "tsConfig": "projects/uinspect/tsconfig.app.json",
            "inlineStyleLanguage": "scss",
            "assets": [
              "projects/uinspect/src/favicon.ico",
              "projects/uinspect/src/assets"
            ],
            "styles": [
              "node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/solid.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/regular.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/brands.scss",
              "projects/uinspect/src/assets/bootstrap/bootstrap.scss",
              "node_modules/ng-uikit-pro-standard/assets/scss/mdb.scss",
              "node_modules/animate.css/animate.css",
              "projects/remarq/src/app/third-party/dataTables/datatables.min.css",
              "projects/uinspect/src/styles.scss",
              {
                "input": "projects/uinspect/src/styles.scss",
                "bundleName": "global-styles",
                "inject": false
              },
              {
                "input": "projects/uinspect/src/app/components/non-appraisal/non-appraisal-app.component.scss",
                "bundleName": "non-appraisal-app.component",
                "inject": false
              },
              {
                "input": "projects/uinspect/src/assets/css/variables.css",
                "bundleName": "global-variables",
                "inject": false
              },
              {
                "input": "projects/uinspect/src/assets/css/mobile.scss",
                "bundleName": "global-mobile",
                "inject": false
              },
              {
                "input": "projects/uinspect/src/assets/sites/traadeUInspect/css/client-theme.scss",
                "bundleName": "traadeUInspect-theme",
                "inject": false
              },
              {
                "input": "projects/uinspect/src/assets/sites/traadeUInspect/css/variables.css",
                "bundleName": "traadeUInspect-variables",
                "inject": false
              },
            ],
            "scripts": [
            ]
          },
          "configurations": {
            "production": {
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "700kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "projects/uinspect/src/environments/environment.ts",
                  "with": "projects/uinspect/src/environments/environment.prod.ts"
                }
              ],
              "outputHashing": "all"
            },
            "devserver": {
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "700kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "projects/uinspect/src/environments/environment.ts",
                  "with": "projects/uinspect/src/environments/environment.devserver.ts"
                }
              ],
              "outputHashing": "all"
            },
            "development": {
              "buildOptimizer": false,
              "optimization": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "sourceMap": true,
              "namedChunks": true
            },
            "localhost": {
              "buildOptimizer": false,
              "optimization": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "sourceMap": true,
              "namedChunks": true
            }
          },
          "defaultConfiguration": "production"
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "configurations": {
            "production": {
              "buildTarget": "uinspect:build:production"
            },
            "development": {
              "port": 44322,
              "buildTarget": "uinspect:build:development"
            },
            "devserver": {
              "port": 44322,
              "buildTarget": "uinspect:build:devserver"
            },
            "localhost": {
              "port": 44322,
              "buildTarget": "uinspect:build:localhost"
            }
          },
          "defaultConfiguration": "production"
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "buildTarget": "uinspect:build"
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "projects/uinspect/src/test.ts",
            "polyfills": "projects/uinspect/src/polyfills.ts",
            "tsConfig": "projects/uinspect/tsconfig.spec.json",
            "karmaConfig": "projects/uinspect/karma.conf.js",
            "inlineStyleLanguage": "scss",
            "assets": [
              "projects/uinspect/src/favicon.ico",
              "projects/uinspect/src/assets"
            ],
            "styles": [
              "projects/uinspect/src/styles.scss"
            ],
            "scripts": []
          }
        }
      }
    },
    "crm": {
      "projectType": "application",
      "schematics": {
        "@schematics/angular:component": {
          "style": "scss"
        },
        "@schematics/angular:application": {
          "strict": true
        }
      },
      "root": "projects/crm",
      "sourceRoot": "projects/crm/src",
      "prefix": "app",
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/crm",
            "index": "projects/crm/src/index.html",
            "main": "projects/crm/src/main.ts",
            "polyfills": "projects/crm/src/polyfills.ts",
            "tsConfig": "projects/crm/tsconfig.app.json",
            "inlineStyleLanguage": "scss",
            "assets": [
              "projects/crm/src/favicon.ico",
              "projects/crm/src/assets"
            ],
            "styles": [
              "node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/solid.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/regular.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/brands.scss",
              "projects/crm/src/assets/bootstrap/bootstrap.scss",
              "node_modules/ng-uikit-pro-standard/assets/scss/mdb.scss",
              "node_modules/animate.css/animate.css",
              "node_modules/line-awesome/dist/line-awesome/css/line-awesome.min.css",
              "projects/crm/src/styles.scss",
              {
                "input": "projects/crm/src/styles.scss",
                "bundleName": "global-styles",
                "inject": false
              },
              {
                "input": "projects/crm/src/app/components/app/app.component.scss",
                "bundleName": "app.component",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/css/variables.css",
                "bundleName": "global-variables",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/css/mobile.scss",
                "bundleName": "global-mobile",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/lookersCRM/css/client-theme.scss",
                "bundleName": "lookersCRM-theme",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/lookersCRM/css/variables.css",
                "bundleName": "lookersCRM-variables",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/citygateCRM/css/client-theme.scss",
                "bundleName": "citygateCRM-theme",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/citygateCRM/css/variables.css",
                "bundleName": "citygateCRM-variables",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/traadeCRM/css/client-theme.scss",
                "bundleName": "traadeCRM-theme",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/traadeCRM/css/variables.css",
                "bundleName": "traadeCRM-variables",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/sellmyride/css/client-theme.scss",
                "bundleName": "sellmyride-theme",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/sellmyride/css/variables.css",
                "bundleName": "sellmyride-variables",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/toyotagb/css/client-theme.scss",
                "bundleName": "toyotagb-theme",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/toyotagb/css/variables.css",
                "bundleName": "toyotagb-variables",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/carsure/css/client-theme.scss",
                "bundleName": "carsure-theme",
                "inject": false
              },
              {
                "input": "projects/crm/src/assets/sites/carsure/css/variables.css",
                "bundleName": "carsure-variables",
                "inject": false
              }
            ],
            "scripts": [
              "node_modules/easy-pie-chart/dist/easypiechart.js",
              "node_modules/screenfull/dist/screenfull.js",
              "node_modules/hammerjs/hammer.min.js",
              "node_modules/jquery/dist/jquery.js",
              "node_modules/datatables.net/js/jquery.dataTables.js",
              "node_modules/datatables.net-fixedcolumns/js/dataTables.fixedColumns.min.js",
              "node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.js"
            ]
          },
          "configurations": {
            "production": {
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "projects/crm/src/environments/environment.ts",
                  "with": "projects/crm/src/environments/environment.prod.ts"
                }
              ],
              "outputHashing": "all"
            },
            "devserver": {
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "projects/crm/src/environments/environment.ts",
                  "with": "projects/crm/src/environments/environment.devserver.ts"
                }
              ],
              "outputHashing": "all"
            },
            "development": {
              "buildOptimizer": false,
              "optimization": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "sourceMap": true,
              "namedChunks": true
            },
            "localhost": {
              "buildOptimizer": false,
              "optimization": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "sourceMap": true,
              "namedChunks": true
            }
          },
          "defaultConfiguration": "production"
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "configurations": {
            "production": {
              "buildTarget": "crm:build:production"
            },
            "devserver": {
              "buildTarget": "crm:build:devserver"
            },
            "development": {
              "port": 44321,
              "buildTarget": "crm:build:development"
            },
            "localhost": {
              "port": 44321,
              "buildTarget": "crm:build:localhost"
            }
          },
          "defaultConfiguration": "production"
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "buildTarget": "crm:build"
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "projects/crm/src/test.ts",
            "polyfills": "projects/crm/src/polyfills.ts",
            "tsConfig": "projects/crm/tsconfig.spec.json",
            "karmaConfig": "projects/crm/karma.conf.js",
            "inlineStyleLanguage": "scss",
            "assets": [
              "projects/crm/src/favicon.ico",
              "projects/crm/src/assets"
            ],
            "styles": [
              "projects/crm/src/styles.scss"
            ],
            "scripts": []
          }
        }
      }
    },
    "carbuying": {
      "projectType": "application",
      "schematics": {
        "@schematics/angular:component": {
          "style": "scss"
        },
        "@schematics/angular:application": {
          "strict": true
        }
      },
      "root": "projects/carbuying",
      "sourceRoot": "projects/carbuying/src",
      "prefix": "app",
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/carbuying",
            "index": "projects/carbuying/src/index.html",
            "main": "projects/carbuying/src/main.ts",
            "polyfills": "projects/carbuying/src/polyfills.ts",
            "tsConfig": "projects/carbuying/tsconfig.app.json",
            "inlineStyleLanguage": "scss",
            "assets": [
              "projects/carbuying/src/favicon.ico",
              "projects/carbuying/src/assets"
            ],
            "styles": [
              "node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/solid.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/regular.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/brands.scss",
              "projects/carbuying/src/assets/bootstrap/bootstrap.scss",
              "node_modules/ng-uikit-pro-standard/assets/scss/mdb.scss",
              "node_modules/animate.css/animate.css",
              "node_modules/line-awesome/dist/line-awesome/css/line-awesome.min.css",
              "projects/carbuying/src/styles.scss",
              {
                "input": "projects/carbuying/src/styles.scss",
                "bundleName": "global-styles",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/app/components/app/app.component.scss",
                "bundleName": "app.component",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/css/variables.css",
                "bundleName": "global-variables",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/css/mobile.scss",
                "bundleName": "global-mobile",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/sites/lookersCarBuying/css/client-theme.scss",
                "bundleName": "lookersCarBuying-theme",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/sites/lookersCarBuying/css/variables.css",
                "bundleName": "lookersCarBuying-variables",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/sites/citygateCarBuying/css/client-theme.scss",
                "bundleName": "citygateCarBuying-theme",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/sites/citygateCarBuying/css/variables.css",
                "bundleName": "citygateCarBuying-variables",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/sites/toyotagb/css/client-theme.scss",
                "bundleName": "toyotagb-theme",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/sites/toyotagb/css/variables.css",
                "bundleName": "toyotagb-variables",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/sites/traadecarbuying/css/client-theme.scss",
                "bundleName": "traadecarbuying-theme",
                "inject": false
              },
              {
                "input": "projects/carbuying/src/assets/sites/traadecarbuying/css/variables.css",
                "bundleName": "traadecarbuying-variables",
                "inject": false
              }
            ],
            "scripts": [
              "node_modules/easy-pie-chart/dist/easypiechart.js",
              "node_modules/screenfull/dist/screenfull.js",
              "node_modules/hammerjs/hammer.min.js",
              "node_modules/jquery/dist/jquery.js",
              "node_modules/datatables.net/js/jquery.dataTables.js",
              "node_modules/datatables.net-fixedcolumns/js/dataTables.fixedColumns.min.js",
              "node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.js"
            ]
          },
          "configurations": {
            "production": {
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "projects/carbuying/src/environments/environment.ts",
                  "with": "projects/carbuying/src/environments/environment.prod.ts"
                }
              ],
              "outputHashing": "all"
            },
            "devserver": {
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ],
              "fileReplacements": [
                {
                  "replace": "projects/carbuying/src/environments/environment.ts",
                  "with": "projects/carbuying/src/environments/environment.devserver.ts"
                }
              ],
              "outputHashing": "all"
            },
            "development": {
              "buildOptimizer": false,
              "optimization": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "sourceMap": true,
              "namedChunks": true
            },
            "localhost": {
              "buildOptimizer": false,
              "optimization": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "sourceMap": true,
              "namedChunks": true
            }
          },
          "defaultConfiguration": "production"
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "configurations": {
            "production": {
              "buildTarget": "carbuying:build:production"
            },
            "devserver": {
              "buildTarget": "carbuying:build:devserver"
            },
            "development": {
              "port": 44320,
              "buildTarget": "carbuying:build:development"
            },
            "localhost": {
              "port": 44320,
              "buildTarget": "carbuying:build:localhost"
            }
          },
          "defaultConfiguration": "production"
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "buildTarget": "carbuying:build"
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "projects/carbuying/src/test.ts",
            "polyfills": "projects/carbuying/src/polyfills.ts",
            "tsConfig": "projects/carbuying/tsconfig.spec.json",
            "karmaConfig": "projects/carbuying/karma.conf.js",
            "inlineStyleLanguage": "scss",
            "assets": [
              "projects/carbuying/src/favicon.ico",
              "projects/carbuying/src/assets"
            ],
            "styles": [
              "projects/carbuying/src/styles.scss"
            ],
            "scripts": []
          }
        }
      }
    },
    "sitescan": {
      "projectType": "application",
      "schematics": {},
      "root": "projects/sitescan/src",
      "sourceRoot": "projects/sitescan/src",
      "prefix": "app",
      "architect": {
        "assets": {
          "glob": "**/*",
          "input": "projects/sitescan/src/.well-known",
          "output": ".well-known/"
        },
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/sitescan",
            "index": "projects/sitescan/src/index.html",
            "main": "projects/sitescan/src/main.ts",
            "polyfills": "projects/sitescan/src/polyfills.ts",
            "tsConfig": "projects/sitescan/tsconfig.app.json",
            "assets": [
              "projects/sitescan/src/favicon.ico",
              "projects/sitescan/src/assets",
              "projects/sitescan/src/app",
              "projects/sitescan/src/manifest.webmanifest"
            ],
            "styles": [
              "node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/solid.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/regular.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/brands.scss",
              "projects/remarq/src/assets/bootstrap/bootstrap.scss",
              "node_modules/ng-uikit-pro-standard/assets/scss/mdb.scss",
              "node_modules/animate.css/animate.css",
              "node_modules/line-awesome/dist/line-awesome/css/line-awesome.min.css",
              "projects/remarq/src/app/third-party/dataTables/datatables.min.css",
              "projects/sitescan/src/styles.scss"
            ],
            "scripts": [
              "node_modules/chart.js/dist/chart.umd.js",
              "node_modules/easy-pie-chart/dist/easypiechart.js",
              "node_modules/screenfull/dist/screenfull.js",
              "node_modules/hammerjs/hammer.min.js",
              "node_modules/jquery/dist/jquery.js",
              "node_modules/datatables.net/js/jquery.dataTables.js",
              "node_modules/datatables.net-fixedcolumns/js/dataTables.fixedColumns.min.js",
              "node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.js"
            ],
            "vendorChunk": true,
            "extractLicenses": false,
            "buildOptimizer": false,
            "sourceMap": true,
            "optimization": false,
            "namedChunks": true,
            "serviceWorker": false
          },
          "configurations": {
            "development": {
              "buildOptimizer": false,
              "optimization": {
                "scripts": false,
                "styles": false,
                "fonts": false
              },
              "sourceMap": false,
              "aot": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "namedChunks": true
            },
            "production": {
              "fileReplacements": [
                {
                  "replace": "projects/sitescan/src/environments/environment.ts",
                  "with": "projects/sitescan/src/environments/environment.prod.ts"
                },
                {
                  "replace": "projects/sitescan/src/aws-exports.js",
                  "with": "projects/sitescan/src/aws-exports.prod.js"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "aot": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "10mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ]
            },
            "mobile": {
              "fileReplacements": [
                {
                  "replace": "projects/sitescan/src/environments/environment.ts",
                  "with": "projects/sitescan/src/environments/environment.prod.ts"
                },
                {
                  "replace": "projects/sitescan/src/aws-exports.js",
                  "with": "projects/sitescan/src/aws-exports.mobile.js"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "aot": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ]
            }
          },
          "defaultConfiguration": "production"
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "options": {
            "host": "localhost",
            "port": 44321,
            "buildTarget": "sitescan:build"
          },
          "configurations": {
            "production": {
              "buildTarget": "sitescan:build:production"
            },
            "development": {
              "buildTarget": "sitescan:build:development"
            },
            "localhost-live": {
              "buildTarget": "sitescan:build:localhost-live"
            },
            "localhost-dev": {
              "buildTarget": "sitescan:build:localhost-dev"
            }
          }
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "buildTarget": "sitescan:build"
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "projects/sitescan/src/test.ts",
            "polyfills": "projects/sitescan/src/polyfills.ts",
            "tsConfig": "projects/sitescan/tsconfig.spec.json",
            "karmaConfig": "karma.conf.js",
            "assets": [
              "projects/sitescan/src/favicon.ico",
              "projects/sitescan/src/assets",
              "projects/sitescan/src/app",
              "projects/sitescan/src/manifest.webmanifest"
            ],
            "styles": [
              "projects/sitescan/src/styles.scss",
              "./node_modules/line-awesome/dist/line-awesome/css/line-awesome.min.css"
            ],
            "scripts": []
          }
        },
        "e2e": {
          "builder": "@angular-devkit/build-angular:protractor",
          "options": {
            "protractorConfig": "e2e/protractor.conf.js",
            "devServerTarget": "sitescan:serve"
          },
          "configurations": {
            "production": {
              "devServerTarget": "sitescan:serve:production"
            }
          }
        }
      }
    },
    "inspect-collect": {
      "projectType": "application",
      "schematics": {},
      "root": "projects/inspect-collect/src",
      "sourceRoot": "projects/inspect-collect/src",
      "prefix": "app",
      "architect": {
        "assets": {
          "glob": "**/*",
          "input": "projects/inspect-collect/src/.well-known",
          "output": ".well-known/"
        },
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/inspect-collect",
            "index": "projects/inspect-collect/src/index.html",
            "main": "projects/inspect-collect/src/main.ts",
            "polyfills": "projects/inspect-collect/src/polyfills.ts",
            "tsConfig": "projects/inspect-collect/tsconfig.app.json",
            "assets": [
              "projects/inspect-collect/src/favicon.ico",
              "projects/inspect-collect/src/assets",
              "projects/inspect-collect/src/app",
              "projects/inspect-collect/src/manifest.webmanifest"
            ],
            "styles": [
              "node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/solid.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/regular.scss",
              "node_modules/@fortawesome/fontawesome-free/scss/brands.scss",
              "projects/inspect-collect/src/assets/bootstrap/bootstrap.scss",
              "node_modules/ng-uikit-pro-standard/assets/scss/mdb.scss",
              "node_modules/animate.css/animate.css",
              "node_modules/line-awesome/dist/line-awesome/css/line-awesome.min.css",
              "projects/inspect-collect/src/app/third-party/dataTables/datatables.min.css",
              "projects/inspect-collect/src/styles.scss",
              {
                "input": "projects/inspect-collect/src/styles.scss",
                "bundleName": "global-styles",
                "inject": false
              },
              {
                "input": "projects/inspect-collect/src/app/components/app/app.component.scss",
                "bundleName": "app",
                "inject": false
              },
              {
                "input": "projects/inspect-collect/src/app/components/app/non-app/non-app.component.scss",
                "bundleName": "non-app",
                "inject": false
              },
              {
                "input": "projects/inspect-collect/src/assets/css/mobile.scss",
                "bundleName": "global-mobile",
                "inject": false
              },
              {
                "input": "projects/inspect-collect/src/assets/css/variables.css",
                "bundleName": "global-variables",
                "inject": false
              },
              {
                "input": "projects/inspect-collect/src/assets/sites/tradecollect/css/client-theme.scss",
                "bundleName": "tradecollect-theme",
                "inject": false
              },
              {
                "input": "projects/inspect-collect/src/assets/sites/tradecollect/css/variables.css",
                "bundleName": "tradecollect-variables",
                "inject": false
              },
              {
                "input": "projects/inspect-collect/src/assets/css/variables.css",
                "bundleName": "global-variables",
                "inject": false
              },
              {
                "input": "projects/inspect-collect/src/assets/css/mobile.scss",
                "bundleName": "global-mobile",
                "inject": false
              }
            ],
            "scripts": [
              "node_modules/chart.js/dist/chart.umd.js",
              "node_modules/easy-pie-chart/dist/easypiechart.js",
              "node_modules/screenfull/dist/screenfull.js",
              "node_modules/hammerjs/hammer.min.js",
              "node_modules/jquery/dist/jquery.js",
              "node_modules/datatables.net/js/jquery.dataTables.js",
              "node_modules/datatables.net-fixedcolumns/js/dataTables.fixedColumns.min.js",
              "node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.js"
            ],
            "vendorChunk": true,
            "extractLicenses": false,
            "buildOptimizer": false,
            "sourceMap": true,
            "optimization": false,
            "namedChunks": true,
            "serviceWorker": false
          },
          "configurations": {
            "development": {
              "buildOptimizer": false,
              "optimization": {
                "scripts": false,
                "styles": false,
                "fonts": false
              },
              "sourceMap": false,
              "aot": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "namedChunks": true
            },
            "localhost-dev": {
              "fileReplacements": [
                {
                  "replace": "projects/inspect-collect/src/environments/environment.ts",
                  "with": "projects/inspect-collect/src/environments/environment.inspect-collect.localhost.ts"
                },
                {
                  "replace": "projects/common/shared/aws/aws-exports.js",
                  "with": "projects/common/shared/aws/aws-exports.remarq.development.js"
                }
              ],
              "buildOptimizer": false,
              "optimization": {
                "scripts": false,
                "styles": false,
                "fonts": false
              },
              "sourceMap": true,
              "aot": false,
              "vendorChunk": true,
              "extractLicenses": false,
              "namedChunks": true
            },
            "production": {
              "fileReplacements": [
                {
                  "replace": "projects/inspect-collect/src/environments/environment.ts",
                  "with": "projects/inspect-collect/src/environments/environment.prod.ts"
                },
                {
                  "replace": "projects/inspect-collect/src/aws-exports.js",
                  "with": "projects/inspect-collect/src/aws-exports.prod.js"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "aot": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "10mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ]
            },
            "mobile": {
              "fileReplacements": [
                {
                  "replace": "projects/inspect-collect/src/environments/environment.ts",
                  "with": "projects/inspect-collect/src/environments/environment.prod.ts"
                },
                {
                  "replace": "projects/inspect-collect/src/aws-exports.js",
                  "with": "projects/inspect-collect/src/aws-exports.mobile.js"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "aot": true,
              "budgets": [
                {
                  "type": "initial",
                  "maximumWarning": "5mb",
                  "maximumError": "20mb"
                },
                {
                  "type": "anyComponentStyle",
                  "maximumWarning": "60kb",
                  "maximumError": "200kb"
                }
              ]
            }
          },
          "defaultConfiguration": "production"
        },
        "serve": {
          "builder": "@angular-devkit/build-angular:dev-server",
          "options": {
            "host": "localhost",
            "port": 44321,
            "buildTarget": "inspect-collect:build"
          },
          "configurations": {
            "production": {
              "buildTarget": "inspect-collect:build:production"
            },
            "development": {
              "buildTarget": "inspect-collect:build:development"
            },
            "localhost-live": {
              "buildTarget": "inspect-collect:build:localhost-live"
            },
            "localhost-dev": {
              "buildTarget": "inspect-collect:build:localhost-dev"
            }
          }
        },
        "extract-i18n": {
          "builder": "@angular-devkit/build-angular:extract-i18n",
          "options": {
            "buildTarget": "inspect-collect:build"
          }
        },
        "test": {
          "builder": "@angular-devkit/build-angular:karma",
          "options": {
            "main": "projects/inspect-collect/src/test.ts",
            "polyfills": "projects/inspect-collect/src/polyfills.ts",
            "tsConfig": "projects/inspect-collect/tsconfig.spec.json",
            "karmaConfig": "karma.conf.js",
            "assets": [
              "projects/inspect-collect/src/favicon.ico",
              "projects/inspect-collect/src/assets",
              "projects/inspect-collect/src/app",
              "projects/inspect-collect/src/manifest.webmanifest"
            ],
            "styles": [
              "projects/inspect-collect/src/styles.scss",
              "./node_modules/line-awesome/dist/line-awesome/css/line-awesome.min.css"
            ],
            "scripts": []
          }
        },
        "e2e": {
          "builder": "@angular-devkit/build-angular:protractor",
          "options": {
            "protractorConfig": "e2e/protractor.conf.js",
            "devServerTarget": "inspect-collect:serve"
          },
          "configurations": {
            "production": {
              "devServerTarget": "inspect-collect:serve:production"
            }
          }
        }
      }
    }
  },
  "cli": {
    "analytics": "42813193-750c-4974-a2fd-5ea33588c8a9"
  }
}
