{"name": "bigauction", "version": "0.0.0", "scripts": {"ng": "ng", "start:remarq:localhost-live": "ng serve -o --ssl true --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=remarq --configuration=localhost-live", "start:remarq:localhost-dev": "ng serve -o --ssl true --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=remarq --configuration=localhost-dev", "start:remarq:dev": "ng serve -o --ssl true --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=remarq --configuration=development", "start:remarq:prod": "ng serve -o --ssl true --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=remarq --configuration=production", "start:remarq:sellmyride-development": "ng serve -o --ssl true --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=remarq --configuration=sellmyride-development", "start:remarq-bigauction": "ng serve -o --ssl true --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=remarq-bigauction --configuration=development", "start:uinspect": "ng serve -o --ssl --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=uinspect --configuration=localhost", "start:carbuying": "ng serve -o --ssl --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=carbuying --configuration=localhost", "start:crm": "ng serve -o --ssl --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=crm --configuration=localhost", "start:sitescan:development": "ng serve -o --ssl --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=sitescan --configuration=development", "start:inspect-collect:localhost-dev": "ng serve -o --ssl true --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt --project=inspect-collect --configuration=localhost-dev", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "ci:clean:all": "rimraf ./dist", "ci:clean:remarq": "rimraf ./dist/remarq", "ci:clean:remarq-bigauction": "rimraf ./dist/remarq", "ci:build:remarq": "ng build --project=remarq --configuration=development", "ci:build:remarq:dev": "ng build --project=remarq --configuration=development", "ci:build:remarq:localhost-live": "ng build --project=remarq --configuration=localhost-live", "ci:build:remarq:localhost-dev": "ng build --project=remarq --configuration=localhost-dev", "ci:build:remarq:prod": "ng build --project=remarq --configuration=production", "ci:build:remarq:mobile-development": "ng build --project=remarq --configuration=mobile-development && npm run remarq-mobile-dev-capacitor", "ci:build:remarq:mobile-production": "ng build --project=remarq --configuration=mobile-production && npm run remarq-mobile-prod-capacitor", "ci:clean:uinspect": "rimraf ./dist/uinspect", "ci:build:uinspect:dev": "ng build --project=uinspect --configuration=development", "ci:build:uinspect:devserver": "ng build --project=uinspect --configuration=devserver", "ci:build:uinspect:demo": "ng build --project=uinspect --configuration=demo", "ci:build:uinspect:localhost": "ng build --project=uinspect --configuration=localhost", "ci:build:uinspect:prod": "ng build --project=uinspect --configuration=production", "ci:clean:crm": "rimraf ./dist/crm", "ci:build:crm:dev": "ng build --project=crm --configuration=development", "ci:build:crm:devserver": "ng build --project=crm --configuration=devserver", "ci:build:crm:demo": "ng build --project=crm --configuration=demo", "ci:build:crm:localhost": "ng build --project=crm --configuration=localhost", "ci:build:crm:prod": "ng build --project=crm --configuration=production", "ci:clean:carbuying": "rimraf ./dist/carbuying", "ci:build:carbuying:dev": "ng build --project=carbuying --configuration=development", "ci:build:carbuying:devserver": "ng build --project=carbuying --configuration=devserver", "ci:build:inspect-collect:localhost-dev": "ng build --project=inspect-collect --configuration=localhost-dev", "bigauction-mobile-dev-capacitor": "cd projects/remarq && NODE_ENV=bigauction-dev npx cap copy", "bigauction-mobile-prod-capacitor": "cd projects/remarq && NODE_ENV=bigauction-prod npx cap copy", "remarq-mobile-dev-capacitor": "cd projects/remarq && NODE_ENV=remarq-dev npx cap sync", "remarq-mobile-prod-capacitor": "cd projects/remarq && NODE_ENV=remarq-prod npx cap sync"}, "private": true, "dependencies": {"@angular-magic/ngx-gp-autocomplete": "^2.0.2", "@angular/animations": "^17.3.11", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.11", "@angular/compiler": "^17.3.11", "@angular/core": "^17.3.11", "@angular/forms": "^17.3.11", "@angular/localize": "^17.3.11", "@angular/platform-browser": "^17.3.11", "@angular/platform-browser-dynamic": "^17.3.11", "@angular/router": "^17.3.11", "@angular/service-worker": "^17.3.11", "@angular/youtube-player": "^17.3.10", "@aws-amplify/auth": "^6.7.0", "@aws-amplify/ui-angular": "^5.0.16", "@capacitor-community/http": "^1.4.1", "@capacitor/android": "^4.0.0", "@capacitor/app": "^4.0.0", "@capacitor/camera": "^4.0.0", "@capacitor/core": "^4.0.0", "@capacitor/haptics": "^4.0.0", "@capacitor/ios": "^4.0.0", "@capacitor/keyboard": "^4.0.0", "@capacitor/splash-screen": "^4.0.0", "@capacitor/status-bar": "^4.0.0", "@danielmoncada/angular-datetime-picker": "^16.1.0", "@fortawesome/fontawesome-free": "^5.15.1", "@microsoft/signalr": "^6.0.8", "@microsoft/signalr-protocol-msgpack": "^6.0.8", "@sentry/angular": "^8.28.0", "@stripe/stripe-js": "^3.5.0", "@swimlane/ngx-graph": "^8.3.0", "@types/file-saver": "^2.0.7", "@types/google.maps": "^3.55.10", "@types/jquery": "^3.5.5", "@wizdm/stripe": "^3.1.0", "alert": "^5.0.10", "angular-datatables": "^17.1.0", "angular-responsive-carousel": "^2.0.2", "angularx-qrcode": "^17.0.1", "animate.css": "^4.1.1", "aws-amplify": "^6.3.6", "badge": "^1.0.3", "base64-arraybuffer": "^1.0.2", "chart.js": "^4.4.4", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-datalabels": "^2.2.0", "d3": "^7.9.0", "d3-transition": "^3.0.1", "datatables": "^1.10.18", "datatables.net": "^1.13.11", "datatables.net-dt": "^1.13.11", "datatables.net-fixedcolumns": "^4.3.1", "date-fns": "^2.29.3", "easy-pie-chart": "^2.1.7", "fast-json-patch": "^3.0.0-1", "file-saver": "^2.0.5", "hammerjs": "^2.0.8", "hapi": "^18.1.0", "line-awesome": "^1.3.0", "messages": "^0.1.0", "moment": "^2.29.1", "ng-sortgrid": "^17.0.0", "ng-toggle-button": "^1.5.2", "ng-uikit-pro-standard": "git+https://x-oauth-basic:<EMAIL>/remarqUK/ng-uikit-pro-standard.git", "ng2-charts": "^4.0.0", "ngx-color-picker": "^16.0.0", "ngx-cookie-service": "^17.1.0", "ngx-drag-drop": "^17.0.0", "ngx-google-analytics": "^14.0.1", "ngx-image-cropper": "^8.0.0", "ngx-image-zoom": "^3.0.0", "ngx-json-viewer": "^3.2.1", "ngx-logger": "^5.0.11", "ngx-pagination": "^6.0.2", "ngx-slick-carousel": "^17.0.0", "ngx-stripe": "^17.2.0", "ngx-webcam": "^0.4.1", "node-gyp": "^10.1.0", "raygun4js": "^2.22.1", "rxjs": "~7.6.0", "screenfull": "^3.3.0", "slick-carousel": "^1.8.1", "stream": "0.0.2", "timers": "^0.1.1", "tslib": "^2.0.0", "xml2js": "^0.6.2", "zone.js": "~0.14.7"}, "devDependencies": {"@angular-builders/custom-webpack": "^17.0.2", "@angular-devkit/build-angular": "^17.3.8", "@angular/cli": "^17.3.8", "@angular/compiler-cli": "^17.3.11", "@angular/language-service": "^17.3.11", "@capacitor/cli": "^4.8.2", "@schematics/angular": "^17.3.8", "@types/d3": "^7.4.3", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "cypress": "^13.16.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.5", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~7.0.0", "tslint": "~6.1.0", "typescript": "~5.4.5", "webpack": "^5.92.0"}}