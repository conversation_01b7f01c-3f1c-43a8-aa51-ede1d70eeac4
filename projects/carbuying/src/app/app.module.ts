import {CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {BrowserModule, Meta, Title} from '@angular/platform-browser';
import {IndexRoutingModule} from './modules/index-routing.module';
import {IndexModule} from './modules/index.module';
import {CommonModule, DatePipe} from '@angular/common';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {DataService, URLService, RoleGuardService as AuthGuard} from './services';
import {CookieService} from 'ngx-cookie-service';
import {RouteReuseStrategy} from '@angular/router';
import localeEnGb from '@angular/common/locales/en-GB';
import {registerLocaleData} from '@angular/common';
import {HttpClientModule} from "@angular/common/http";
import {HttpModule} from './modules/http.module';
import {CustomRouteReuseStrategy} from './global/utils';
import {ApiService, LoggerService, ValuationQuoteService} from './global/services';
import {RemarqLoggerModule} from "./global/modules";
import {AppComponent} from './components/app/app.component';
import {LocaleService, ToastModule} from "ng-uikit-pro-standard";
import {AuthGuardServiceInterface, DataServiceInterface, URLServiceInterface} from "./global/services/interfaces";

registerLocaleData(localeEnGb);

@NgModule({
  declarations: [],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpModule,
    HttpClientModule,
    CommonModule,
    RouterModule,
    IndexRoutingModule,
    IndexModule,
    RemarqLoggerModule,
    ToastModule.forRoot(),
  ],
  providers: [
    {provide: DataServiceInterface, useExisting: DataService},
    {provide: URLServiceInterface, useExisting: URLService},
    {provide: AuthGuardServiceInterface, useExisting: AuthGuard},
    AuthGuard,
    CookieService,
    ValuationQuoteService,
    {
      provide: RouteReuseStrategy,
      useClass: CustomRouteReuseStrategy
    },
    {provide: LOCALE_ID, useValue: "en-GB"},
    Title,
    Meta,
    ApiService,
    LoggerService,
    LocaleService,
    DatePipe
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  bootstrap: [AppComponent],
  exports: []
})

export class AppModule {
}
