import {Component, OnInit} from '@angular/core';
import {UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators} from '@angular/forms';
import {LoggerService, ValuationQuoteService, VehicleLookupService} from '../../../../global/services';
import {ValuationRequestDTO, ValuationResultDTO, VehicleLookupInfoDTO} from '../../../../global/interfaces';
import {Router} from '@angular/router';
import {environment} from "../../../../../environments/environment";
import {MainComponent} from '../main.component';

@Component({
  selector: 'app-journey-demo',
  templateUrl: './journey-demo.component.html',
  styleUrls: ['./journey-demo.component.scss']
})
export class JourneyDemoComponent implements OnInit {

  gettingEstimate = false;

  constructor(private fb: UntypedFormBuilder,
              private vehicleLookup: VehicleLookupService,
              private valuationService: ValuationQuoteService,
              private router: Router,
              private logService: LoggerService) {
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  step = 0;
  vrmForm: UntypedFormGroup;
  contactForm: UntypedFormGroup;

  vehicleDetails: VehicleLookupInfoDTO;
  vehicleNotFound = false;
  valuationEstimate: ValuationResultDTO;

  fetchingData = false;
  fetchDataMessage: string = null;
  appraisalLink: string;

  valuationError: boolean;

  ngOnInit(): void {
    // get uinspect format

    this.constructVrmForm();
    this.constructContactForm();
  }

  get cf() {
    return this.contactForm.controls;
  }

  constructVrmForm() {
    this.vrmForm = this.fb.group({
      vrm: new UntypedFormControl("AAL150N", [Validators.required]),
      mileage: new UntypedFormControl("123", [Validators.required])
    });
  }

  constructContactForm() {
    this.contactForm = this.fb.group({
      name: new UntypedFormControl("", [Validators.required]),
      email: new UntypedFormControl("", [Validators.required]),
      mobile: new UntypedFormControl("", [Validators.required]),
    });
  }

  getVehicleDetails() {

    if (!this.vrmForm.valid) {
      return;
    }

    this.gettingEstimate = true;
    const vrm = this.vrmForm.get('vrm').value;

    // get the vehicle details from api lookup
    this.vehicleNotFound = false;
    this.vehicleLookup.getVehicleInfo(vrm).then(result => {
      this.logger.info("VEHICLE LOOKUP: ", result);

      if (!result) {
        this.vehicleNotFound = true;
      } else {
        this.vehicleDetails = result;
        this.step = 1;
      }

    }).catch(err => {
    }).finally(() => {
      this.gettingEstimate = false;
    });
  }

  setFetchData(fetching: boolean, msg: string = null) {
    this.fetchingData = fetching;
    this.fetchDataMessage = msg;
  }

  getEstimate() {
    // initialise a quote (this will create lead crm details)
    // the result contains a link to the UInspect journey for this vehicle
    this.valuationError = false;

    this.contactForm.controls["email"].setErrors(null);

    this.gettingEstimate = true;

    const basePrice = this.getRandomInt(4600, 6200);

    const dto = {
      vrm: this.vrmForm.get('vrm').value,
      name: this.contactForm.get('name').value,
      mileage: this.vrmForm.get('mileage').value,
      email: this.contactForm.get('email').value,
      mobile: this.contactForm.get('mobile').value,
      baseCapPrice: basePrice,
      baseRetailPrice: basePrice + this.getRandomInt(500, 800),
      testValuation: false,
      platformId: MainComponent.globals.platformId,
    } as ValuationRequestDTO;

    this.valuationService.initializeQuote(dto).then(res => {


      if (res.isValid) {

        this.valuationEstimate = res.dto;
        this.logger.log("Valuation: ", this.valuationEstimate);
        this.appraisalLink = `${environment.uInspectUrl}${this.valuationEstimate.externalRef}`;

        this.step = 2;
      }

      this.setFetchData(false);

    }).catch(err => {

      if (err.error.Message.toLowerCase().includes("email")) {
        console.log("Setting error..");
        this.contactForm.controls["email"].setErrors({invalidEmail: "Please enter a valid email address"});
      } else {
        this.valuationError = true;
      }

      this.logger.error("Error occurred getting valuation: ", err);
    }).finally(() => {
      this.gettingEstimate = false;
    });
  }

  wrongVehicleDetails() {
    this.step = 0;
  }

  stepBack() {
    this.step = this.step - 1;
  }

  getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  informAppraisal() {
  }

  startAppraisal() {
    // navigate to appraisalURL
    this.router.navigate([]).then((result) => {
      window.open(this.appraisalLink, '_blank');
    });
  }

  requestCallback() {

    this.valuationService.requestCallback(this.valuationEstimate.leadId).then(res => {

    });
  }
}
