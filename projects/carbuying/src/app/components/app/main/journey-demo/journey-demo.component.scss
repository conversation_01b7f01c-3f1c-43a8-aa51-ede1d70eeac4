.detail-container {
  display: flex;
  align-items: flex-start;
  align-self: center;
  flex-direction: column;
  justify-content: center;
}

.value-button {
  width: 100%;
  margin-top: 0px;
}

.your-details-grid {
 grid-gap: 20px;
}


.trustpilot {
  text-align: center
}

.details-entry-form {

  .md-form { margin-bottom: 30px !important; }

  .md-form .float-label.form-control + label.active {

    background-color: transparent !important;
    width: auto !important;
    top: -20px !important;
    letter-spacing: 0px !important;

  }
}

.entry-form {

  .md-form .float-label.form-control:focus, .md-form .float-label.form-control:not(:placeholder-shown) {
    padding: 0.6rem 12px 0.6rem 12px !important;
  }

  .md-form .float-label.form-control + label {

    font-size: 22px;
    letter-spacing: -1px;
    width: 100%;
    text-align: center;
    padding-left: 0px !important;
    top: 7px !important;
  }



  .md-form .float-label.form-control + label.active {

    background-color: transparent !important;
    width: auto !important;
    top: -20px !important;
    letter-spacing: 0px !important;

  }
}

#mileage {
  font-size: 34px;
  font-weight: 900;
  text-transform: uppercase;
  width: 100%;
  text-align: center;
}

#vrm {

  font-size: 34px;
  font-weight: 900;
  text-transform: uppercase;
  width: 100%;
  text-align: center;
}

.entry-form input.form-control {
  border: 2px solid #777 !important;
}

.open-tool { color: #fff !important }

.back-forwards {
  padding-top: 20px;

  .step-back {
    border: 1px solid #333;
    color: #333;
  }
}

.appraisal-home {
  background-image: url("/assets/sites/toyotagb/images/toyota4.png");
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
}

.title-main {
  font-size: 32px;
  font-weight: bold;
  width: 100%;
  text-align: center;
}

.title-main-2 {
  font-size: 26px;
  font-weight: bold;
}

.bold { font-weight: 800; }

.img-container {
}

img {
  object-fit: contain;
  width: 100%;
  height: 100%;
}

.center-container {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.back-link {
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  background-color: darkblue;
  color: white;
  padding: 2px 5px;
  border-radius: 2px;
  text-align: center;
  display: inline-block;
}

.message-container {
  color: red;
  font-size: 0.875rem
}
