<div style="width: 100%; padding-bottom: 40px;" id="homepage">

  <div *ngIf="step > 0" class="d-flex justify-content-center back-forwards">
    <div style="max-width: 900px; width: 100%;">
      <div class="px-3">
        <div class="btn-sm btn btn-outline-primary step-back" (click)="stepBack()">
          &laquo; Back
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="step == 0" id="enterVRM">
    <div class="title-main mt-4 mb-3">
      <div>Get the best price for your Toyota</div>
    </div>

    <div class="form-container">
      <div class="d-flex flex-wrap justify-content-center">
        <div class="img-container"
             style="flex-basis: 300px; flex-grow: 1; max-width: 600px; padding: 20px; max-height: 300px;">
          <img src="assets/images/1pix.gif" alt="Car Image" class="appraisal-home">
        </div>

        <div class="detail-container" style="width: 280px; padding: 20px;">
          <div class="car-image"></div>
          <div class="entry-form">
            <form [formGroup]="vrmForm" (submit)="getVehicleDetails()">

              <div class="pb-3 md-form">
                <input mdbInput class="form-control float-label" id="vrm"
                       (keyup)="valuationError = false; vehicleNotFound = false" formControlName="vrm">
                <label for="vrm">Enter your reg</label>
              </div>

              <div class="pb-1 md-form">
                <input mdbInput
                       (keyup)="valuationError = false; vehicleNotFound = false"
                       class="form-control float-label" id="mileage" formControlName="mileage">
                <label for="mileage">Enter your mileage</label>
              </div>

              <ng-container *ngIf="valuationError" [ngTemplateOutlet]="valuationErrorTemplate"></ng-container>

              <div class="mt-3">
                <button class="btn btn-primary value-button" type="submit" [disabled]="vrmForm.invalid">
                  <span *ngIf="gettingEstimate"><i class="fa fa-spin fa-spinner"></i> Getting Value</span>
                  <span *ngIf="!gettingEstimate">Get Value</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

  </div>

  <div *ngIf="step == 1" class="d-flex align-items-center justify-content-center">

    <div class="d-flex mt-2 flex-wrap your-details-grid"
         style="flex-direction: row-reverse; width: 100%; max-width: 900px;">

      <div class="flex-grow-1 px-3" style="order: 2; flex-basis: 350px ">

        <div class="title-main-2">
          Your details
        </div>

        <div class="details-entry-form" style="margin-top: 25px;">
          <form [formGroup]="contactForm" (submit)="getEstimate()">
            <div class="md-form">
              <input mdbInput class="form-control float-label" id="name" formControlName="name">
              <label for="name">Name</label>
            </div>

            <div class="md-form">
              <input mdbInput class="form-control float-label" id="email" formControlName="email">
              <label for="email">Email</label>
              <div class="error-message" *ngIf="cf.email.dirty && cf.email.errors">
                <span *ngIf="cf.email.errors.invalidEmail">Please enter a valid email address</span>
              </div>
            </div>

            <div class="md-form">
              <input mdbInput class="form-control float-label" id="mobile" formControlName="mobile">
              <label for="mobile">Mobile</label>
            </div>

            <ng-container *ngIf="valuationError" [ngTemplateOutlet]="valuationErrorTemplate"></ng-container>

            <button class="btn btn-primary btn-block" type="submit" [disabled]="contactForm.invalid"
                    *ngIf="!valuationError">
              <span *ngIf="!gettingEstimate">Get My Valuation</span>
              <span *ngIf="gettingEstimate"><i class="fa fa-spin fa-spinner"></i> Getting Your Valuation</span>
            </button>
          </form>
        </div>
      </div>

      <div class="flex-grow-1 px-3 pt-3" style="flex-basis: 300px">
        <app-vehicle-lookup-result [vehicleInfo]="vehicleDetails"></app-vehicle-lookup-result>
        <div class="pt-2 pb-3">
          <button class="btn btn-primary-outline btn-xs" type="submit" (click)="wrongVehicleDetails()">These are not
            my
            vehicle
            details
          </button>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="step == 2">

    <div class="d-flex" style="justify-content: center">

      <div style="max-width: 600px;">
        <div class="title-main">
          Great News!
        </div>

        <app-journey-quote [quote]="valuationEstimate"></app-journey-quote>

        <div class="text-center">
          <button class="btn btn-primary mt-4 mb-5" (click)="step = step + 1">That looks good, let's continue</button>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="step == 3">

    <div class="d-flex" style="justify-content: center">

      <div style="max-width: 650px;">

        <div class="title-main" style="text-align: left;">Final Step - Take some photos</div>
        <div class="sub-text mt-3">To provide you with the most accurate valuation, and a guaranteed price
          for
          vehicle we
          would
          like you to take some photos of your car.
        </div>

        <div class="widget mt-3 py-2">

          <div class="sub-text mt-2">We have created a special tool to help you upload photos from your mobile device.
          </div>

          <div class="mt-2">
            <ul class="pl-2 ml-2">
              <li><span class="bold">If you are already on a mobile device</span>, open the appraisal tool by clicking
                the button below

                <div class="mt-2"><a [href]="appraisalLink" target="_blank" class="btn btn-sm btn-primary open-tool">Open
                  Appraisal
                  Tool</a></div>
              </li>

            </ul>
          </div>

          <div class="mt-2">
            <ul class="pl-2 ml-2">
              <li><span class="bold">Alternatively, arrange a callback from one of our team</span>

                <div class="mt-2"><span (click)="requestCallback()" class="btn btn-sm btn-primary open-tool">Request Callback</span></div>
              </li>

            </ul>
          </div>

          <div>
            <ul class="pl-2 ml-2" *ngIf="appraisalLink">
              <li>
                <span class="bold">Alternatively</span>, scan the QR code below on using your mobile device to open the
                appraisal tool

                <qrcode [qrdata]="appraisalLink" [width]="256" [errorCorrectionLevel]="'M'"></qrcode>

              </li>
            </ul>
          </div>
        </div>

      </div>
    </div>
  </div>


  <div class="row">
    <div class="col-12">
      <div *ngIf="vehicleNotFound" style="width: 100%; color: red; text-align: center; ">
        Vehicle could not be found - try again with correct details
      </div>
    </div>
  </div>

  <div class="trustpilot mb-4 mt-4">
    <img src="assets/images/trustpilot2.png" style="width: 300px;">
  </div>
</div>

<ng-template #valuationErrorTemplate>
  <div class="row">
    <div class="col-12 message-container mb-2">
      A valuation could not be determined for this vehicle, please call our team on <a type="tel">0191 001991</a> for a
      personal quotation.
    </div>
  </div>
</ng-template>
