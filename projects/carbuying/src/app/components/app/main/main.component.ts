import {AfterViewInit, Component, HostListener, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {DomainData, GlobalConstants} from "../../../global/shared";
import {RoleGuardService, URLService} from "../../../services/index";

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class MainComponent implements OnInit, OnDestroy, AfterViewInit {

  constructor(
    private route: ActivatedRoute,
    private redirect: URLService,
    private router: Router,
    private authService: RoleGuardService
    ) {

    MainComponent.globals = GlobalConstants.getPlatformDetails(GlobalConstants.CompanyProductCode.CRM, window.location.host);
  }

  public static globals: DomainData;

  theme = "abc";
  isMobile = false;
  isDevice = false;

  public wrongVersion = false;
  menuItems: any;


  async ngOnInit() {


  }

  async ngAfterViewInit() {

  }

  @HostListener('window:beforeunload')
  ngOnDestroy() {
  }
}
