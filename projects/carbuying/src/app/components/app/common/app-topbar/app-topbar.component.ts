import {DOCUMENT} from '@angular/common';
import {AfterViewInit, Component, HostListener, Inject, Input, OnDestroy, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {Subscription} from 'rxjs';
import {URLService} from "../../../../services/index";
import {ApiService} from "../../../../global/services";

@Component({
  selector: 'app-topbar',
  templateUrl: './app-topbar.component.html',
  styleUrls: ['./app-topbar.component.scss']
})
export class AppTopbarComponent implements OnInit, OnDestroy {

  @Input() hideSearchBar: boolean;

  public dropDownMenu = [];
  public navbarCollapsed = true;
  public vehicleSearchInput: string;
  public isLoggedIn = false;

  messageEventSub: Subscription;
  folderCounts: any;

  constructor(@Inject(DOCUMENT)
              public document: Document,
              public url: URLService,
              public router: Router,
              private apiClient: ApiService,
  ) {
    this.router = router;
  }

  async ngOnInit() {
  }

  @HostListener('window:beforeunload')
  ngOnDestroy() {
    if (this.messageEventSub) {
      this.messageEventSub.unsubscribe();
    }
  }
}
