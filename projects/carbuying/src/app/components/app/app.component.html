<div id="site-{{ theme }}"> <!-- DONT USE CLASS flex-column -->
  <div [id]="isMobile ? 'mobile' : 'desktop'" [class]="isDevice ? 'device' : 'computer'"
       style="display: flex; flex-direction: column; min-height: 100vh"> <!-- DONT USE CLASS flex-column -->

    <div id="main-content" style="flex-shrink: 1;">
      <app-topbar></app-topbar>
    </div>

    <div class="body-wrapper">
      <router-outlet></router-outlet>
    </div>

    <div style="flex-shrink: 1;">
      <app-footer></app-footer>
    </div>
  </div>
</div>
