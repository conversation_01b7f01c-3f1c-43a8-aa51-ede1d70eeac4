import {NgModule} from '@angular/core';
import {RouterModule} from "@angular/router";
import {HTTP_INTERCEPTORS} from "@angular/common/http";
import {HeaderInterceptor} from "../interceptors/config.interceptor";
import {ApiService} from "../global/services";
import {DataService} from "../services/index";
import {CommonModule} from "@angular/common";
import {AppComponent} from "../components/app/app.component";
import {AppTopbarComponent} from "../components/app/common/app-topbar/app-topbar.component";
import {AppFooterComponent} from "../components/app/common/app-footer/app-footer.component";

@NgModule({
  declarations: [
    AppComponent,
    AppTopbarComponent,
    AppFooterComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HeaderInterceptor,
      multi: true
    },
    ApiService,
    DataService,
  ],
  exports: [
    RouterModule,
  ]
})
export class IndexModule {
}
