import {NgModule} from '@angular/core';
import {MainRoutingModule} from "./main-routing.module";
import {LoadingSpinnerComponent} from '../global/components';
import {RouterModule} from "@angular/router";
import {
  MDBRootModules,
  WavesModule
} from "ng-uikit-pro-standard";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {HttpClientJsonpModule, HttpClientModule} from "@angular/common/http";
import {CommonModule} from "@angular/common";
import {NgsgModule} from "ng-sortgrid";
import {HelpersService, ImageService, VehicleLookupService} from '../global/services';
import {GraphModule} from "@swimlane/ngx-graph";
import {MainComponent} from "../components/app/main/main.component";
import {JourneyDemoComponent} from "../components/app/main/journey-demo/journey-demo.component";
import {
  VehicleLookupResultComponent
} from "../components/app/main/journey-demo/vehicle-lookup-result/vehicle-lookup-result.component";
import {
  VehicleDvlaDetailsComponent
} from "../components/app/main/journey-demo/vehicle-dvla-details/vehicle-dvla-details.component";
import {JourneyQuoteComponent} from "../components/app/main/journey-demo/journey-quote/journey-quote.component";
import {QRCodeModule} from "angularx-qrcode";
import {RoleGuardService as AuthGuard} from "../services/authguard.service";

@NgModule({
  declarations: [
    LoadingSpinnerComponent,
    MainComponent,
    JourneyDemoComponent,
    VehicleLookupResultComponent,
    VehicleDvlaDetailsComponent,
    JourneyQuoteComponent
  ],
  imports: [
    MainRoutingModule,
    RouterModule,
    MDBRootModules,
    WavesModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    CommonModule,
    NgsgModule,
    GraphModule,
    QRCodeModule
  ],
  providers: [
    AuthGuard,
    ImageService,
    HelpersService,
    VehicleLookupService,
  ],
  exports: []
})

export class MainModule {
}
