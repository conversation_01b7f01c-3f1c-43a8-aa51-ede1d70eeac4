import {
  <PERSON>tt<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from "@angular/common/http";
import {Injectable} from "@angular/core";
import {Observable, from} from "rxjs";
import {RoleGuardService} from "../services";

export const InterceptorSkipHeader = "X-Skip-Interceptor";

@Injectable()
export class HeaderInterceptor implements HttpInterceptor {

  constructor(private authguardService: RoleGuardService) {
  }

  intercept(
    request: HttpRequest<any>,
    next: <PERSON>ttp<PERSON>and<PERSON>
  ): Observable<HttpEvent<any>> {

    if (request.headers.has(InterceptorSkipHeader)) {
      const headers = request.headers.delete(InterceptorSkipHeader);
      return next.handle(request.clone({headers}));
    }

    return next.handle(request);

  }
}
