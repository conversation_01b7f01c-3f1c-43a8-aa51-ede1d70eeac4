import {Injectable} from '@angular/core';
import {Router} from "@angular/router";
import {URLHelperService} from '../global/services';

@Injectable({providedIn: 'root'})
export class URLService {

  constructor(private router: Router, private urlHelperService: URLHelperService) {
  }

  redirect(path, params = null, replaceUrl = false) {

    this.router.navigate([path], {replaceUrl, queryParams: params}).then(() => {
    });
  }

  homepage(returnOnly: boolean = false) {
    const path = "";
    if (returnOnly) {
      return path;
    } else {
      this.redirect(path);
    }
  }

  login(returnOnly: boolean = false) {
    const path = "";
    if (returnOnly) {
      return path;
    } else {
      this.redirect(path);
    }
  }

  bidsView(customerId: string) {
    this.redirect("main/my-bids/1/2/", customerId);
  }
}
