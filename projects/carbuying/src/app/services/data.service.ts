import {Injectable, Inject} from '@angular/core';
import {environment} from '../../environments/environment';
import {DOCUMENT} from "@angular/common";
import {DataServiceInterface} from '../global/services/interfaces';
import {DomainData} from '../global/shared';

@Injectable()
export class DataService implements DataServiceInterface {

  public apiUrl: string;
  public messageHubUrl: string;
  public googleMapsAPIKey: string;
  public globals: DomainData;

  constructor(
    @Inject(DOCUMENT) private document,
  ) {

    if (environment.production) {

      this.messageHubUrl = `${environment.messageHubUrl}`;
      this.apiUrl = `${environment.serviceUrl}`;
      this.googleMapsAPIKey = environment.googleMapsAPIKey;

    } else {

      this.messageHubUrl = `${environment.messageHubUrl}`;
      this.apiUrl = `${environment.serviceUrl}`;
      this.googleMapsAPIKey = environment.googleMapsAPIKey;
    }
  }
}
