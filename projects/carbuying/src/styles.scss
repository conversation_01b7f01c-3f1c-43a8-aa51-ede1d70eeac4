/* DO NOT PLACE STYLES HERE, UNLESS YOU WANT THEM GLOBAL TO THE APPRAISAL APP AND THE WEB APP */
/* THERE ARE APPRAISAL-APP.SCSS & NONAPPRAISALAPP.SCSS FILES */

/* NOTE - THIS MUST GO IN STYLES */
.webcam-wrapper, .webcam-wrapper video {
  width: 100% !important;
  height: 100% !important;
}

.md-form {

  position: relative;
  margin-top: 0 !important;
  margin-bottom: 1.2rem !important;

  &.on-background {

    .form-control {
      border: 1px solid #ced4da !important;
    }

    .input-group-text {
      line-height: 32px !important;
    }

    .btn {
      border: 1px solid #ced4da !important;
    }

    input[type=date].float-label.form-control + label,
    label.active {
      background-color: var(--bgColour) !important;
    }
  }

  input[type=date].float-label.form-control + label {

    &.focus {
      color: var(--floatInputFocusLabelColour) !important;
    }

    top: var(--floatLabelTop) !important;
    font-size: var(--floatLabelFontSize) !important;
    transform: none !important;
    color: #757575 !important;
    left: var(--floatLabelLeft) !important;
    background-color: #fff;
    padding-left: 5px !important;
    padding-right: 5px !important;
    margin-left: 0 !important;
    width: auto !important;
  }

  .currencyPrepend {

    left: 36px !important;

  }

  label.active {

    transform: none !important;

  }

  &.non-fixed-height {

    margin-bottom: 0.1rem !important;
    margin-top: 0.9rem !important;

    .error-message {
      margin-left: 5px;
      position: relative;
      top: -8px !important;
      height: 5px !important;
      margin-bottom: 25px;
      font-size: 0.75rem;
      font-weight: 400;
    }
  }


  .form-control {

    /* Prevents the padding from enlarging the input */
    -webkit-box-sizing: border-box !important;
    -moz-box-sizing: border-box !important;
    box-sizing: border-box !important;
  }

  /* Standard Float Label */
  .float-label.form-control {

    padding-left: 10px !important;

    &.ng-invalid.ng-dirty:not(:focus) {
      border-color: var(--errorColour) !important;
    }

    &.ng-invalid.ng-dirty:not(:focus) + label {

      color: var(--errorColour) !important;

    }

    &:focus {
      padding-top: 10px !important;
      border-color: var(--floatInputFocusBorderColour) !important;
    }

    &:focus, &:not(:placeholder-shown) {
      padding: 0.7rem 0 0.5rem 12px !important;
    }

    & + label {
      top: -1px !important;
      padding-left: 12px !important;
      color: var(--placeholderColour) !important;
      font-weight: var(--placeholderWeight) !important;
    }

    & + label.active {
      transform: none !important;
      position: absolute !important;
      font-size: var(--floatLabelFontSize) !important;
      left: var(--floatLabelLeft) !important;
      top: var(--floatLabelTop) !important;
      background-color: #fff;
      padding-left: var(--floatLabelPadding) !important;
      padding-right: var(--floatLabelPadding) !important;
      color: var(--floatLabelColour) !important;
      font-weight: var(--floatLabelWeight) !important;
      z-index: 3;
    }

    & + label.currencyPrepend.active, .currencyPrepend {

      left: 42px !important;
    }

    &:focus + label {
      color: var(--floatInputFocusLabelColour) !important;
      font-weight: var(--floatInputFocusLabelWeight) !important;
    }
  }

  /* For inline float labels search forms -- no space allocated for errors */
  &.inline {
    margin-top: 0.0rem !important;
    margin-bottom: 0.0rem !important;

    .float-label.form-control {

      padding-top: 0.375rem !important;
      padding-bottom: 0.375rem !important;
      margin-bottom: 0;

      & + label {
        top: -4px !important;
      }

      & + label.active {
        position: relative;
        top: var(--floatLabelTop);
        left: var(--floatLabelLeft);
        font-weight: var(--floatLabelWeight);
      }
    }
  }

  /* Narrow, non float inputs */
  &.narrow {
    margin-top: 0.4rem !important;
    margin-bottom: 0.4rem !important;

    .form-control {
      margin-bottom: 0;
    }
  }

  &.prepend-spacer {

    margin-top: 0.4rem !important;
    margin-bottom: 1.7rem !important;

    .form-control {
      margin-bottom: 0;
    }

  }

  select.form-control {
    padding-left: 7px !important;
  }

}
