#mobile {

  .container {
    padding-left: 0px;
    padding-right: 0px;
  }

  .page-top, .indent-on-mobile {

    padding-left: 10px;
    padding-right: 10px;

  }

  .classic-tabs ul.nav {

    padding: 0 10px;
    width: 100%;

  }

  .table th {

    padding-top: 5px;

  }

  .widget {
    border-radius: 0;
    border: 0;

    /* Chunky padding */
    &.widget-padding {
      padding: 1rem 1rem;
    }

    /* Standard padding */
    &.padding {
      padding: 1rem 0.5rem;
    }
  }

  #homepage {

    .header-block {

      .header-line-1 {
        font-size: 4rem;
      }

      .header-line-2 {
        font-size: 3rem;
      }

      .header-line-3 {
        font-size: 2rem;
      }
    }

  }
}
