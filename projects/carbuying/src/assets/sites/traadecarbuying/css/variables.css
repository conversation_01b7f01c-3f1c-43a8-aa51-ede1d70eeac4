:root {

  --font1: "Raleway";
  --font2: "Raleway";
  --defaultFont: var(--font1);
  --widgetBorderRadius: 8px;
  --imageOutlineColour: var(--widgetBorderColour);
  --footerHeight: 118px;
  --headerHeight: 56px;

  /* Palette */
  --colour1: #065cac;
  --colour2: #D21A20;
  --colour3: #ffffff;
  --colour4: #222222;
  --colour5: #ffffff;
  --colour6: #ffffff;
  --colour7: #ccc;
  --colour8: #ffffff;
  --colour9: #007bff;

  --navbarBgColour: var(--colour9);
  --textColour: var(--colour4);
  --navbarTextColour: #fff;
  --bgColour: #f0f1f2;
  --placeholderColour: #bbb;
  --actionColour: var(--colour1);
  --floatLabelLeft: -0px;

  --primaryButtonColour: var(--colour2);
  --secondaryButtonColour: var(--colour1);
  --attentionBoxBackgroundColour: #eaf3f8;
  --textLabelColour: var(--textColour);
  --linkColour: var(--colour1);
  --dropdownBackgroundColour: #fff;
  --dropdownItemTextColour: var(--colour1);
  --dropdownHoverItemBackgroundColour: var(--colour1);
  --dropdownHoverItemTextColour: #fff;
  --sideNavBackgroundColour: #fff;
  --adminSideNav: #fff;
  --footerTextColour: #fff;

  --buyNowColour: var(--colour1);
  --timedSaleColour: #51ADDF;
  --managedSaleColour: var(--colour2);
  --sideNavTextColour: var(--colour4);
  --sideNavHoverTextColour: var(--colour4);
  --primaryButtonColour: var(--colour1);
  --switchColour: var(--colour2);
  --inputBorderColour: var(--colour7);
  --footerBackgroundColour: var(--colour9);

}

.device {
  --headerHeight: 96px;
  --headerPaddingTop: 30px;
}


