#site-iwantyourcar {

  #homepage {

    .header-line-3 {
      color: var(--colour2);

    }

    .btn-login, .btn-sign-up { border: 0 !important }

    #main-content { background-color: var(--colour1) !important; }

  }

  #contact-us {

    .page-top { color: #fff; }

  }


  .svg-color {
    filter: invert(100%) saturate(2476%) brightness(100%) contrast(100%);
  }

  .btn-primary, .bg-primary, .btn.btn-primary {
    border: 0px !important;
  }

  .btn-group .btn.active { border: 1px solid var(--colour1); }
  .btn-group .side-label { border: 1px solid var(--colour4); }

  .md-form .form-control {

    border: 1px solid #ced4da !important;
  }

  .btn-group .btn.active { background-color: var(--colour1) !important ;}

  .btn-secondary { color: var(--secondaryButtonColour); }

  .mobile-bar { background-color: var(--colour1); }

  .classic-tabs .nav li:hover { color: var(--colour3) !important; }

  .side-nav .collapsible .card-body li a { background-color: rgba(0, 0, 0, 0.05) !important; }

  .fa.good { color: #007a5a; }
  .fa.bad { color: #c00; }

  .badger.bg-badge-color { background-color: var(--colour2); }

  .form-control {

    border: 1px solid #ced4da !important;

  }

  .background-input {
    background-color: transparent;
    border-top: 1px solid #ced4da !important;
    border-bottom: 1px solid #ced4da !important;
  }

  .modal-body .form-control {
    background-color: #fff !important;
    border: 1px solid #ccc !important;
  }

  .modal-header {
    background-color: var(--colour2);
    color: #fff;
  }

  .btn-primary {
    background-color: var(--colour3) !important;
    color: #fff !important;
  }

  .search-box {
    background-color: #1A394D !important;
  }

  .search-submit {
    color: #fff;
  }

  .your-advert {
    background-color: rgba(0, 0, 0, 0.4);

  }

  .item-price-container {
    color: #fff;
  }

  .switch.blue-white-switch label input[type=checkbox]:checked + .lever {

    background-color: var(--colour2);

  }

  .summary-inner {
    color: #fff;
  }

  .navbar-brand {
    background-image: url('/assets/sites/iwantyourcar/images/sitelogo.svg');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    height: 35px;
    width: 195px;
    margin-right: 0;
    padding-bottom: 0;
  }

  .undernav {
    background-color: #fff;
  }

  .undernav a.undernav-item, .undernav a.undernav-item:visited {

    font-weight: 500 !important;

  }

  .attention-box {

    color: #fff !important;

    .welcome-text {
      color: #fff !important;
    }

    .sub-text {
      color: #fff !important;
    }
  }

  #homepage {

    .block-1, .block-2 {
      background-color: var(--colour1) !important;
      color: #fff !important;

      .sub-header {
        color: var(--colour1);
      }
    }

    color: var(--colour1) !important;


    .btn-sign-up {
      background-color: var(--colour2) !important;
    }

    .homepage-car {
      width: 100%;
      height: 100%;
      background-image: ("/assets/inchcapetrade/images/jaguar.png");
      background-repeat: no-repeat;
      background-size: contain;

    }
  }
}
