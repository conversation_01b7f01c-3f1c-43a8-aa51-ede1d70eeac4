.mdb-select-arrow, .mdb-select-toggle {

  border-left: 1px solid var(--softInputBorderColour) !important;
  width: 36px !important;

  &::before {
    content: '\f107' !important;
    font-weight: 900;
    font-family: var(--fontAwesome), serif !important;
    display: inline-block;
    color: var(--softInputBorderColour) !important;
    width: 30px;
    height: 30px;
    font-size: 16px;
  }
}

.narrow-select {

  mdb-select:not(.inline) .single, mdb-select:not(.inline) .mdb-select-value, mdb-select-2:not(.inline) .single, mdb-select-2:not(.inline) .mdb-select-value, mdb-date-picker:not(.inline) .single, mdb-date-picker:not(.inline) .mdb-select-value {

    padding-bottom: 0.35rem !important;
    padding-top: 0.375rem !important;

    .mdb-select-arrow {
      height: 38px !important;
      line-height: 38px !important;
      width: 38px !important;
    }
  }

  .single .value {
    font-size: 0.9rem !important;
  }

  label:not(.active) {
    font-weight: 500;
    color: #bbb;
    font-size: 0.9rem;
  }

}


.cursor-pointer {
  cursor: pointer;
}

.btn-xs {
  padding: 0.1rem 0.25rem !important;
  font-size: 0.8rem;
}

.grid-gap-15 {
  grid-gap: 15px;
}

.mdb-select-arrow, .mdb-select-toggle {

  border-left: 1px solid var(--softInputBorderColour) !important;
  width: 36px !important;

  &::before {
    content: '\f107' !important;
    font-weight: 900;
    font-family: var(--fontAwesome), serif !important;
    display: inline-block;
    color: var(--softInputBorderColour) !important;
    width: 30px;
    height: 30px;
    font-size: 16px;
  }
}

/* Multi-select side arrow */
.mdb-select-arrow {

  right: 0 !important;
  top: 0 !important;
  line-height: 30px;

}

mdb-select-dropdown .dropdown-content li > a, mdb-select-dropdown .dropdown-content li > span {

  color: var(--inputTextColour) !important;
  height: 20px;
  line-height: 20px;
  font-weight: 400;

}

mdb-select, mdb-select-2, mdb-date-picker {

  position: relative;

  .md-form {

    .form-control {

      padding: 0.7rem 0 0.5rem 12px !important;

    }

    .datepicker-icon {
      padding: 6px 12px;
      background-color: #eee;
      right: 1px;
      top: 1px;
      line-height: 31px;
      border-left: 1px solid var(--softInputBorderColour);
    }


    label:not(.form-check-label) {
      top: var(--floatLabelTop) !important;
      left: var(--floatLabelLeft) !important;
      font-size: var(--floatLabelFontSize) !important;
      font-weight: var(--floatLabelWeight) !important;
      color: var(--floatLabelColour) !important;
      background-color: var(--floatLabelBackgroundColour) !important;
      padding-left: var(--floatLabelPadding) !important;
      padding-right: var(--floatLabelPadding) !important;
    }
  }

  &.ng-invalid, &.ng-invalid .md-form {

    input:not(:focus) {

      border: 1px solid var(--errorColour) !important;

      & + label {

        color: var(--errorColour) !important;

      }

    }
  }


  &:focus-within {
    label {
      color: var(--floatInputFocusLabelColour) !important;
    }

    .below > .form-control {
      /*
      box-shadow: inset 0 0 0 1px var(--floatInputFocusBorderColour) !important;
      border: none !important;
       */
      box-shadow: none !important;
      border-color: var(--floatInputFocusBorderColour) !important;
    }
  }

  & .mdb-select-label {

    top: 13px;

    &.focused {
      color: var(--floatInputFocusLabelColour) !important;
    }
  }

  & .mdb-select-value.focused {
    box-shadow: inset 0 0 0 1px var(--floatInputFocusBorderColour) !important;
  }


  & .placeholder, .mdb-select-placeholder {

    color: #bbb !important;
    font-weight: 500 !important;

  }

  & > label.active {

    font-size: 0.8rem !important;
    font-weight: 500 !important;
    background-color: #fff !important;
  }

  .single .value, .mdb-select-value {
    padding-left: 12px !important;
    line-height: 1.5rem !important;
    font-size: 1rem !important;
  }

  &.inline {

    .mdb-select-value {
      padding-top: 0.375rem !important;
      padding-bottom: 0.375rem !important;
    }
  }

  &:not(.inline) {

    .single, .mdb-select-value {

      padding-top: 0.7rem !important;
      padding-bottom: 0.5rem !important;
    }

    .mdb-select-arrow {
      height: 45px !important;
      line-height: 45px !important;
      width: 36px !important;
    }
  }

  & *:not(.fas) {
    font-family: var(--font1) !important;
  }

  & > div > div.single {

    padding-top: 0.375rem !important;
    padding-bottom: 0.35rem !important;
    line-height: 1.5rem !important;
    font-size: 1rem !important;

    & > div.value {
      color: var(--inputTextColour) !important;
    }
  }
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
}

.side-nav .nav-item {
  &:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }
  &.selected {
    background-color: rgba(255, 255, 255, 0.25) !important;
  }
}
