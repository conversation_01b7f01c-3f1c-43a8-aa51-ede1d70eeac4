import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ttpInterceptor,
  HttpRequest,
} from "@angular/common/http";
import {Injectable} from "@angular/core";
import {Observable, from} from "rxjs";
import {RoleGuardService} from "../services";
import {mergeMap} from "rxjs/operators";

export const InterceptorSkipHeader = "X-Skip-Interceptor";

@Injectable()
export class HeaderInterceptor implements HttpInterceptor {

  constructor(private authguardService: RoleGuardService) {
  }

  intercept(
    request: HttpRequest<any>,
    next: <PERSON>ttp<PERSON>and<PERSON>
  ): Observable<HttpEvent<any>> {

    if (request.headers.has(InterceptorSkipHeader)) {
      const headers = request.headers.delete(InterceptorSkipHeader);
      return next.handle(request.clone({headers}));
    }

    // send auth token when getting doc categories for linking uinspect section with lead document type
    if (request.url.indexOf("/api/lead-document-categories") > 0) {

      return this.authguardService.getTokenObservable().pipe(
        mergeMap((token) => {
          request = request.clone({
            setHeaders: {
              Authorization: `Bearer ${token}`,
            },
          });

          return next.handle(request);
        })
      );
    }
    else {
      return next.handle(request);
    }
  }

}
