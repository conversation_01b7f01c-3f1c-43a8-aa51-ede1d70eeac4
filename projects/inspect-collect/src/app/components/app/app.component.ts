import {Component, Inject, NgZone, ViewEncapsulation} from "@angular/core";
import {DomainData, GlobalConstants} from "../../global/shared";
import {Router} from "@angular/router";
import {DOCUMENT} from "@angular/common";
import {Meta, Title} from "@angular/platform-browser";
import {WidgetSetting, WidgetSettings} from "../../../settings/widget-settings";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  encapsulation: ViewEncapsulation.None
})

export class AppComponent {

  public static globals: DomainData;
  public static globalWidgets: { [key: string]: WidgetSetting };

  constructor(private router: Router,
              @Inject(DOCUMENT) private document: Document,
              private titleService: Title,
              private metaService: Meta,
              private zone: NgZone,
  ) {

    AppComponent.globals = GlobalConstants.getPlatformDetails(GlobalConstants.CompanyProductCode.InspectCollect, window.location.host);
    AppComponent.globalWidgets = WidgetSettings.Widgets;

    if (AppComponent.globals == null) {
      this.router.navigate(["/assets/html/notConfigured.html"]);
    }
  }
}
