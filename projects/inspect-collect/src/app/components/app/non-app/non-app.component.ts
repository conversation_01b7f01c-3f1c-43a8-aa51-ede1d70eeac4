import {Component, Inject, OnInit, ViewEncapsulation, NgZone} from '@angular/core';
import {Router} from "@angular/router";
import {DOCUMENT} from "@angular/common";
import {Meta, Title} from "@angular/platform-browser";
import {GlobalConstants} from "../../../global/shared";
import {DomainData} from "../../../global/shared/index";

@Component({
  selector: 'app-root',
  templateUrl: './non-app.component.html',
  styleUrls: ['./non-app.component.scss'],
  encapsulation: ViewEncapsulation.None
})

export class NonAppComponent implements OnInit {

  public static globals: DomainData;

  constructor(
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    private titleService: Title,
    private metaService: Meta,
    private zone: NgZone,
    //             private logService: LoggerService
  ) {

    NonAppComponent.globals =
      GlobalConstants.getPlatformDetails(GlobalConstants.CompanyProductCode.InspectCollect, window.location.host);

    if (NonAppComponent.globals == null) {
      console.log("NAVIGATING TO NOT CONFIGURED " + window.location.host);
      this.router.navigate(["/assets/html/notConfigured.html"]);
    }

    this.theme = NonAppComponent.globals.theme;
    this.platformName = NonAppComponent.globals.platformName;
    this.loadCSSForTheme();
    this.setTitle();
    this.setMeta();
    this.setFavicon();
  }

//  logger = this.logService.taggedLogger(this.constructor?.name);

  public showChatWidget = false;
  public theme = "";
  platformName: string;
  isMobile: any;
  isDevice: any;

  async ngOnInit() {
  }

  loadCSSForTheme() {

    const head = this.document.getElementsByTagName('head')[0];

    this.appendStyle(head, 'global-style', "global-styles");
    this.appendStyle(head, 'global-variables', "global-variables");
    this.appendStyle(head, 'global-app-component', "non-app");
    this.appendStyle(head, 'global-mobile-style', "global-mobile");

    this.appendStyle(head, 'client-theme', NonAppComponent.globals.theme + "-theme");
    this.appendStyle(head, 'client-variables', NonAppComponent.globals.theme + "-variables");
  }

  appendStyle(head, id, cssPath) {

    const themeLink = this.document.getElementById(id) as HTMLLinkElement;

    if (themeLink) {
      themeLink.href = cssPath;
    } else {
      const style = this.document.createElement('link');
      style.id = id;
      style.rel = 'stylesheet';
      style.href = `${cssPath}.css`;

      head.appendChild(style);
    }
  }

  setTitle() {
    this.titleService.setTitle(this.platformName + " - Trading Platform");
  }

  setMeta() {
    this.metaService.addTags([
      {name: 'keywords', content: this.platformName + ",Trading,Vehicle Remarketing,Car Auction"},
      {name: 'description', content: this.platformName + " Online Vehicle Remarketing - Trading Platform"}
    ]);
  }

  setFavicon() {
    const faviconId = this.document.getElementById("favicon") as HTMLLinkElement;
    if (faviconId != null) {
      faviconId.href = "/assets/sites/" + this.theme + "/images/favicon.ico";
    }

    const appleTouchIcon = this.document.getElementById("apple-touch-icon") as HTMLLinkElement;
    if (appleTouchIcon != null) {
      appleTouchIcon.href = "/assets/sites/" + this.theme + "/images/apple-touch-icon.png";
    }

    const icon16 = this.document.getElementById("png16") as HTMLLinkElement;
    if (icon16 != null) {
      icon16.href = "/assets/sites/" + this.theme + "/images/favicon-16x16.png";
    }

    const icon32 = this.document.getElementById("png32") as HTMLLinkElement;
    if (icon32 != null) {
      icon32.href = "/assets/sites/" + this.theme + "/images/favicon-32x32.png";
    }
  }
}
