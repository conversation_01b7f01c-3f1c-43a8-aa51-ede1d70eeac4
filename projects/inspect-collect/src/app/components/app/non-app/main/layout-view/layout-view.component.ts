import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewChild} from '@angular/core';
import {ContainerGroupService, LayoutService, URLService, WidgetService} from "../../../../../services";
import {ActivatedRoute} from "@angular/router";
import {ModalDirective} from "ng-uikit-pro-standard";
import {ICContainerDTO, ICLayoutDTO, ICWidgetDTO} from "../../../../../interfaces";
import {ContainerService} from "../../../../../services";
import {EventService} from "../../../../../services";
import {EventTypeEnum} from "../../../../../enums";
import {Subscription} from "rxjs";

@Component({
  templateUrl: './layout-view.component.html',
  styleUrls: ['./layout-view.component.scss'],
})

export class LayoutViewComponent implements OnInit, <PERSON><PERSON><PERSON>roy {

  @ViewChild("addContainerModal") addContainerModal: ModalDirective;
  @ViewChild("editContainerModal") editContainerModal: ModalDirective;
  @ViewChild("editLayoutSettingsModal") editLayoutSettingsModal: ModalDirective;

  containers: ICContainerDTO[] = [];
  layout: ICLayoutDTO;
  layoutId: string;
  currentContainer: ICContainerDTO;
  containerGroupContainers: ICContainerDTO[];
  widgets: ICWidgetDTO[] = [];
  private eventSub: Subscription;

  constructor(
    public url: URLService,
    private eventService: EventService,
    private containerService: ContainerService,
    private layoutService: LayoutService,
    private widgetService: WidgetService,
    private route: ActivatedRoute,
  ) {
    this.layoutId = this.route.snapshot.params.layoutId;
  }

  ngOnInit() {

    this.layoutService.get(this.layoutId).then((x) => {
      this.layout = x.dto as ICLayoutDTO;
      this.loadContainerGroupContainers(this.layout.icContainerGroupId).then(() => {
      });
    });

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {
      this.processEvents(data);
    });
  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  async loadContainerGroupContainers(icContainerGroupId: string) {
    return this.containerService.containerGroupContainers(icContainerGroupId, {component: 'layout-containers'}).then((x) => {
      this.containerGroupContainers = x.results;
    });
  }

  startEditLayout() {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.LayoutEditInit, object: {layoutId: this.layoutId}});
  }

  startCreateContainer() {
    this.eventService.ICEvent.emit({
      eventType: EventTypeEnum.ContainerAddInit,
      object: {icContainerGroupId: this.layout.icContainerGroupId}
    });
  }

  startEditContainer(container: ICContainerDTO) {
    this.eventService.ICEvent.emit({
      eventType: EventTypeEnum.ContainerEditInit,
      object: {containerId: container.id}
    });
  }

  processEvents(data) {

    if (data.eventType === EventTypeEnum.ContainerSaved) {
      this.loadContainerGroupContainers(this.layout.icContainerGroupId).then(() => {
        this.containerService.get(data.object.icContainerId, {component: 'layout-view'}).then((result) => {
          this.currentContainer = result.dto;
        });
      });
    }
  }

  selectContainer(container: ICContainerDTO) {
    this.currentContainer = container;
  }
}
