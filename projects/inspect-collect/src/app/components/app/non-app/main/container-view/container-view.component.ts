import {Component, OnInit, ViewChild} from '@angular/core';
import {
  ContainerService,
  EventService,
  URLService
} from "../../../../../services";
import {ICContainerDTO, ICContainerWidgetDTO} from "../../../../../interfaces";
import {EventTypeEnum} from "../../../../../enums/EventTypeEnum";

@Component({
  selector: 'app-container-view',
  templateUrl: './container-view.component.html',
  styleUrls: ['./container-view.component.scss'],
})

export class ContainerViewComponent implements OnInit {

  container: ICContainerDTO;
  containerWidgets: ICContainerWidgetDTO[] = [];

  constructor(
    public url: URLService,
    private eventService: EventService,
    private containerService: ContainerService,
  ) {

  }

  ngOnInit() {

    this.eventService.ICEvent.subscribe((data) => {
      this.processEvents(data);
    });
  }

  processEvents(data) {

    if (data.eventType === EventTypeEnum.ContainerSelected) {
      this.containerService.get(data.object.id).then((result) => {
        this.container = result.dto;
      });
    }
  }

  startAddWidgetContainer() {

  }
}
