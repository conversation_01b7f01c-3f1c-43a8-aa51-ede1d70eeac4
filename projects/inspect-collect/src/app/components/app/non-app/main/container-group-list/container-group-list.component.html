<div class="d-flex">
  <div class="page-title flex-grow-1">Container Groups</div>
  <div>
    <button class="btn btn-sm btn-primary" (click)="showCreateContainerGroupModal()">Add Container Group</button>
  </div>
</div>

<table class="table table-hover mt-3">
  <thead>
  <tr>
    <td>&nbsp;</td>
    <td>Group Name</td>
    <td>Status</td>
    <td>Added</td>
    <td>Updated</td>
    <td>Actions</td>
  </tr>
  </thead>
  <tbody>
  <tr *ngFor="let containerGroup of containerGroups">
    <td><button class="btn btn-primary btn-sm" (click)="selectContainerGroup(containerGroup)">Select</button></td>
    <td>{{ containerGroup.name }}</td>
    <td>{{ containerGroup.statusId }}</td>
    <td>{{ containerGroup.added | date: "dd/MM/YY HH:mm" }}</td>
    <td>{{ containerGroup.updated | date: "dd/MM/YY HH:mm" }}</td>
    <td>
      <button class="btn btn-sm btn-primary mr-2" (click)="viewContainerGroupLayouts(containerGroup.id)">Layouts
      </button>
      <button class="btn btn-sm btn-primary mr-2" (click)="viewContainerGroupWidgets(containerGroup.id)">Widgets
      </button>
      <button class="btn btn-sm btn-primary mr-2" (click)="editContainerGroup(containerGroup)">Edit</button>
      <button class="btn btn-sm btn-danger" (click)="deleteContainerGroup(containerGroup)">Delete</button>
    </td>
  </tr>
  </tbody>
</table>

<div mdbModal #createContainerGroupModal="mdbModal" class="modal fade" tabindex="-1"
     [config]="{backdrop: false, ignoreBackdropClick: true}"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        Create Container Group (Customer)
      </div>
      <div class="modal-body">
        <input [(ngModel)]="newLayoutName">
        <button class="btn btn-sm btn-primary" (click)="createContainerGroup()">Create</button>
      </div>
    </div>
  </div>
</div>
