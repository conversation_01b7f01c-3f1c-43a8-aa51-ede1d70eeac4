<div mdbModal #editContainerWidgetModal="mdbModal" class="modal" tabindex="-1"
     aria-labelledby="editLayoutSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-side modal-top-right" role="document">
    <div class="modal-content">
      <form [formGroup]="editContainerWidgetForm">
        <div class="modal-header">

          <input type="hidden" formControlName="id">


          <div class="input-group" style="margin-right: 50px">
            <input type="text" class="form-control" id="containerWidgetName" formControlName="name"
                   placeholder="Enter container widget name">
            <div class="input-group-append">
              <div class="input-group-text cursor-pointer">
                <div (click)="saveEditContainerWidget()"><i class="fa fa-spin fa-spinner" *ngIf="savingEdit"></i> Update
                </div>
              </div>
            </div>
          </div>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                  (click)="editContainerWidgetModal.hide()">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div>
            Widget: {{ containerWidget?.icWidget?.name }}
          </div>
          <div class="">
            <div>Content</div>
            <textarea formControlName="content"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <div class="d-flex w-100">
            <div class="flex-grow-1">
              <button type="button" class="btn btn-danger" (click)="deleteContainerWidget()">Delete</button>
            </div>
            <div>
              <button type="button" class="btn btn-secondary mr-2" (click)="hideEditContainerWidget()">Close</button>
              <button type="button" class="btn btn-primary" (click)="saveEditContainerWidget()">Update</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
