import {Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges} from '@angular/core';
import {ContainerService, EventService, URLService} from "../../../../../services";
import {ICContainerDTO, ICWidgetDTO} from "../../../../../interfaces";
import {EventTypeEnum} from "../../../../../enums";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-container-list',
  templateUrl: './container-list.component.html',
  styleUrls: ['./container-list.component.scss'],
})

export class ContainerListComponent implements OnInit, OnDestroy, OnChanges {

  @Input("icContainerGroupId") icContainerGroupId: string;

  containers: ICContainerDTO[] = [];
  private sub: Subscription;

  constructor(
    public url: URLService,
    private eventService: EventService,
    private containerService: ContainerService,
  ) {

  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.icContainerGroupId) {
      this.getContainers(this.icContainerGroupId);
    }
  }

  ngOnInit() {
    this.getContainers(this.icContainerGroupId);
    this.sub = this.eventService.ICEvent.subscribe((data) => {
      this.processEvents(data);
    });
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  getContainers(icContainerGroupId: string) {

    this.containerService.search({
      component: 'container-list',
      filters: {icContainerGroupId}
    }).then((x) => {
      this.containers = x.results.sort(
        (a, b) => new Date(b.added).getTime() - new Date(a.added).getTime());
    });
  }

  addContainer() {
    this.eventService.ICEvent.emit({
      eventType: EventTypeEnum.ContainerAddInit,
      object: {icContainerGroupId: this.icContainerGroupId}
    });
  }

  processEvents(data) {
    if (data.eventType === EventTypeEnum.ContainerSaved) {
      this.getContainers(this.icContainerGroupId);
    }
  }

  editContainer(container: ICContainerDTO) {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.ContainerEditInit, object: {icContainerId: container.id}});
  }
}
