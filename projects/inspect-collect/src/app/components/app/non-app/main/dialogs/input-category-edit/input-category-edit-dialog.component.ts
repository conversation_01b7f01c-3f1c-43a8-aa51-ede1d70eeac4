import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {
  ContainerGroupService,
  EventService,
  InputCategoryService,
  InputService,
  LocalStorageService
} from "../../../../../../services";
import {EventTypeEnum} from "../../../../../../enums";
import {ICContainerGroupDTO, ICInputCategoryDTO} from "../../../../../../interfaces";
import {compare} from "fast-json-patch";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-input-category-edit-dialog',
  templateUrl: './input-category-edit-dialog.component.html',
  styleUrls: ['./input-category-edit-dialog.component.scss'],
})

export class InputCategoryEditDialogComponent implements OnInit, <PERSON><PERSON><PERSON>roy {

  @ViewChild("editInputCategoryModal") editInputCategoryModal: ModalDirective;
  @ViewChild("addInputCategoryModal") addInputCategoryModal: ModalDirective;

  addInputCategoryForm = new FormGroup({
    name: new FormControl(null, [Validators.required]),
    icContainerGroupId: new FormControl(null),
    parentCategoryId: new FormControl(null),
  });

  editInputCategoryForm = new FormGroup({
    id: new FormControl(null, [Validators.required]),
    name: new FormControl(null, [Validators.required]),
    icContainerGroupId: new FormControl(null),
    parentCategoryId: new FormControl(null),
  });

  inputCategory: ICInputCategoryDTO;
  preEditForm: any;
  private eventSub: Subscription;
  inputs: ICInputCategoryDTO[] = [];
  containerGroupOptions: { label: string; value: string }[] = [];
  globalContainerGroup: ICContainerGroupDTO;
  parentCategoryName: string;
  inputCategories: ICInputCategoryDTO[];

  constructor(
    private eventService: EventService,
    private containerGroupService: ContainerGroupService,
    private localStorage: LocalStorageService,
    private inputCategoryService: InputCategoryService) {

  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  ngOnInit(): void {

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {

      if (data.eventType == EventTypeEnum.InputCategoryEditInit) {
        this.startEditInputCategory(data.object).then();
      }
      if (data.eventType == EventTypeEnum.InputCategoryAddInit) {
        this.startAddInputCategory(data.object).then();
      }
    });

    this.loadContainerGroups().then();
  }

  async startAddInputCategory(object: { parentCategory: ICInputCategoryDTO }) {

    // This fetches the globalContainerId if we have it
    this.checkGlobalContainerGroup();
    this.addInputCategoryForm.reset();

    if (object.parentCategory?.id != null) {
      this.addInputCategoryForm.patchValue({parentCategoryId: object.parentCategory.id});
      this.parentCategoryName = object.parentCategory.name;
      // Show Tree Here
    }

    if (this.globalContainerGroup != null) {
      this.addInputCategoryForm.patchValue({icContainerGroupId: this.globalContainerGroup.id});
    }

    this.preEditForm = {};
    this.addInputCategoryModal.show();
  }

  async startEditInputCategory(object) {

    this.editInputCategoryForm.reset();

    // If the object has a input object, use it, otherwise get the input from the input
    if (object?.input != null) {
      this.inputCategory = object.inputCategory;
    } else {
      await this.inputCategoryService.get(object.inputCategory.id).then((x) => {
        this.inputCategory = x.dto as ICInputCategoryDTO;
        this.parentCategoryName = "";

        if (this.inputCategory.parentCategoryId != null) {
          this.inputCategoryService.get(this.inputCategory.parentCategoryId).then((x) => {
            this.parentCategoryName = x.dto.name;
          });
        }
      });
    }

    this.initEditInputCategoryForm(this.inputCategory);
    this.preEditForm = Object.assign({}, this.editInputCategoryForm.value); // Save a copy
    this.editInputCategoryModal.show();
  }

  get ef() {
    return this.editInputCategoryForm.value;
  }

  get af() {
    return this.addInputCategoryForm.value;
  }

  saveAddInput() {

    this.inputCategoryService.create({
      name: this.af.name,
      icContainerGroupId: this.af.icContainerGroupId,
      parentCategoryId: this.af.parentCategoryId
    }).then(() => {
      this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputCategorySaved});
    });

    this.hideAddInput();
  }

  saveEditInput() {

    const inputId = this.ef.id;

    const patch = compare(this.preEditForm, this.ef);

    if (patch.length > 0) {
      this.inputCategoryService.patch(inputId, patch).then(() => {
        this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputCategorySaved});
      });
    }
    this.hideEditInput();
  }

  initEditInputCategoryForm(input: ICInputCategoryDTO) {
    this.editInputCategoryForm.patchValue({
      id: input.id,
      name: input.name,
      parentCategoryId: input.parentCategoryId,
    });
  }

  hideEditInput() {
    this.editInputCategoryModal.hide();
  }

  hideAddInput() {
    this.addInputCategoryModal.hide();
  }


  deleteInput() {
    this.inputCategoryService.delete(this.inputCategory.id).then(() => {
      this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputCategoryDeleted});
      this.editInputCategoryModal.hide();
    });
  }

  loadContainerGroups() {

    return this.containerGroupService.search({component: 'input-edit-dialog'}).then((x) => {
      this.containerGroupOptions = x.results.map((x) => {
        return {label: x.name, value: x.id};
      });
    });
  }

  private checkGlobalContainerGroup() {

    this.globalContainerGroup = this.localStorage.getGlobalContainerGroup();
  }
}

