<div class="d-flex">
  <div class="page-title flex-grow-1">Inputs</div>
  <div>
    <button class="btn btn-sm btn-primary" (click)="addInput()">Add Input</button>
  </div>
</div>

{{ icContainerGroupId }}

<table class="table w-100">
  <thead>
  <tr>
    <th>Name</th>
    <th *ngIf="globalContainerGroup == null">Customer</th>
    <th>DataType</th>
    <th>Category</th>
    <th>Min Items</th>
    <th>Max Items</th>
    <th>Status</th>
    <th>Added</th>
    <th>Updated</th>

  </tr>
  </thead>
  <tbody>
  <tr *ngFor="let input of inputs">
    <td>{{ input.name }}</td>
    <td *ngIf="globalContainerGroup == null">{{ containerGroupName(input.icContainerGroupId) }}</td>
    <td>{{ input.statusId }}</td>
    <td>{{ input.added | date: "dd/MM/yy" }}</td>
    <td>{{ input.updated  | date: "dd/MM/yy" }}</td>
    <td>
      <button class="btn btn-sm btn-primary" (click)="editInput(input)">Edit</button>
      <button class="btn btn-sm btn-danger" (click)="deleteInput(input)">Delete</button>
    </td>
  </tr>
  </tbody>
</table>

