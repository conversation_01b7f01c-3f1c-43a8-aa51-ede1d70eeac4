import {Component, OnInit, ViewEncapsulation, NgZone, Input} from '@angular/core';
import {BaseWidgetComponent} from "../base-widget.component";
import {text} from "d3";

@Component({
  selector: 'widget-text-box',
  templateUrl: './widget-text-box.component.html',
  styleUrls: ['./widget-text-box.component.scss'],
})

export class WidgetTextBoxComponent extends BaseWidgetComponent {

  @Input("content") content: string;

}
