import {Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {ContainerGroupService, InputCategoryService, URLService} from "../../../../../services";
import {ICContainerGroupDTO, ICInputCategoryDTO, ICInputCategoryTreeDTO} from "../../../../../interfaces";
import {ActivatedRoute} from "@angular/router";
import {EventTypeEnum} from "../../../../../enums";
import {EventService} from "../../../../../services";
import {Subscription} from "rxjs";

@Component({
  templateUrl: './input-category-list.component.html',
  styleUrls: ['./input-category-list.component.scss'],
})

export class InputCategoryListComponent implements OnInit, OnDestroy {

  inputCategories: ICInputCategoryDTO[] = [];
  icContainerGroupId: string;
  containerGroups: ICContainerGroupDTO[];
  containerGroupOptions: { label: string; value: string }[];
  private eventSub: Subscription;
  categoryTree: ICInputCategoryTreeDTO[];

  constructor(
    public url: URLService,
    private inputCategoryService: InputCategoryService,
    private eventService: EventService,
    private containerGroupService: ContainerGroupService,
  ) {
  }

  ngOnInit() {

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {
      if (data.eventType === EventTypeEnum.InputCategorySaved) {
        console.log("ICS");
        this.getInputCategories();
      } else if (data.eventType === EventTypeEnum.InputCategoryDeleted) {
        this.getInputCategories();
      }
    });

    this.getInputCategories();
    this.getContainerGroups();
  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  getInputCategories() {

    this.inputCategoryService.search({
      component: 'input-category-list',
      filters: {icContainerGroupId: this.icContainerGroupId}
    }).then((x) => {
      this.inputCategories = x.results;
      this.categoryTree = this.inputCategoryService.buildCategoryTree(this.inputCategories, null, 0);
    });
  }

  getContainerGroups() {

    this.containerGroupService.search({component: 'input-category-list'}).then((x) => {
      this.containerGroups = x.results;
      this.containerGroupOptions = this.containerGroups.map((x) => {
        return {label: x.name, value: x.id};
      });
    });
  }

  addInputCategory(parent: ICInputCategoryDTO) {
    this.eventService.ICEvent.emit({
      eventType: EventTypeEnum.InputCategoryAddInit,
      object: {
        icContainerGroupId: this.icContainerGroupId,
        parentCategory: parent
      }
    });
  }

  deleteInputCategory(inputCategory: ICInputCategoryDTO) {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputCategoryDeleteInit, object: inputCategory});
  }

  containerGroupName(icContainerGroupId: string) {
    return this.containerGroups?.find((x) => x.id === icContainerGroupId)?.name;
  }

  editInputCategory(inputCategory: ICInputCategoryDTO) {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputCategoryEditInit, object: {inputCategory}});
  }

  topLevelCategories() {
    if (this.categoryTree == null) {
      return [];
    }
    return this.categoryTree.filter((x) => x.parentCategoryId == null);
  }
}
