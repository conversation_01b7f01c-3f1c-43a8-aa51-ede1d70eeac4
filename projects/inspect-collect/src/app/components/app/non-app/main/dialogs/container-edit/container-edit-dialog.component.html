<div mdbModal #addContainerModal="mdbModal" class="modal " tabindex="-1"
     aria-labelledby="editLayoutSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addLayoutModalLabel">Add Container</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="addContainerModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="editContainerForm">

          <input type="hidden" formControlName="id">
          <input type="hidden" formControlName="icContainerGroupId">

          <div class="form-group">
            <label for="containerName">Container Name</label>
            <input type="text" class="form-control" id="addContainerName" formControlName="name"
                   placeholder="Enter container name">
          </div>

        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="hideAddContainer()">Close</button>
        <button type="button" class="btn btn-primary" (click)="saveAddContainer()">Save</button>
      </div>
    </div>
  </div>
</div>

<div mdbModal #editContainerModal="mdbModal" class="modal" tabindex="-1"
     aria-labelledby="editLayoutSettingsModalLabel" aria-hidden="true">
  <form [formGroup]="editContainerForm">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">

          <input type="hidden" formControlName="id">
          <input type="hidden" formControlName="icContainerGroupId">

          <div class="input-group" style="margin-right: 50px">
            <input type="text" class="form-control" id="containerName" formControlName="name"
                   placeholder="Enter container name">
            <div class="input-group-append">
              <div class="input-group-text cursor-pointer">
                <div (click)="saveEditContainer()"><i class="fa fa-spin fa-spinner" *ngIf="savingEdit"></i> Update</div>
              </div>
            </div>
          </div>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                  (click)="editContainerModal.hide()">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="d-flex flex-wrap grid-gap-15">
            <div class="layout-container"
                 cdkDropList
                 (cdkDropListDropped)="drop($event)"
                 [class.portrait]="ef.orientation == containerOrientationEnum.Portrait || ef.orientation == null"
                 [class.landscape]="ef.orientation == containerOrientationEnum.Landscape"
            >
              <div
                cdkDrag
                *ngFor="let containerWidget of sortedContainerWidgets(container)" class="edit-mode widget-row" (click)="editContainerWidget(containerWidget)">

                <ng-container [ngTemplateOutlet]="loadWidget" [ngTemplateOutletContext]="{ $implicit: containerWidget }"></ng-container>

              </div>
            </div>
            <div>
              <div class="">

                <div>
                  <div class="btn-group">
                    <label
                      *ngFor="let orientation of orientations"
                      class="btn btn-sm btn-outline-primary"
                      [value]="orientation.value"
                      [ngClass]="{ 'active': ef.orientation == orientation.value }"
                      mdbRadio="{{ orientation.value }}"
                      (click)="setOrientation(orientation.value)"
                      formControlName="orientation">
                      {{ orientation.label }}
                    </label>
                  </div>
                </div>

                <div style="border: 1px solid #aaa; border-radius: 5px; padding: 5px;">
                  <div class="d-flex">
                    <div class="flex-grow-1">Widgets</div>
                    <div>
                      <div class="btn btn-xs btn-primary" (click)="initCreateWidget()">Create Widget</div>
                    </div>
                  </div>
                  <table class="table">
                    <thead>
                    <tr>
                      <th>Widget</th>
                      <th></th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let widget of widgets" (click)="addWidgetToContainer(widget)">
                      <td>
                        {{ widget.name }}
                      </td>
                      <td>
                        <div class=""><i class="fa fa-plus-circle"></i></div>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="hideEditContainer()">Close</button>
          <button type="button" class="btn btn-primary" (click)="saveEditContainer()">Update</button>
        </div>
      </div>
    </div>
  </form>
</div>

<ng-template #loadWidget let-containerWidgetObject>

  <ng-container *ngIf="containerWidgetType(containerWidgetObject) as containerWidget">

    <div *ngIf="containerWidget.icWidget.component == 'text-box'">
      <widget-text-box [content]="containerWidget.content"></widget-text-box>
    </div>

    <div *ngIf="containerWidget.icWidget.component == 'button'">
      <widget-button [content]="containerWidget.content"></widget-button>
    </div>

    <div *ngIf="containerWidget.icWidget.component == 'link'">
      <widget-link [content]="containerWidget.content"></widget-link>
    </div>

  </ng-container>
</ng-template>
