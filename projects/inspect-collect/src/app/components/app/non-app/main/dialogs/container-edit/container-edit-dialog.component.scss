.layout-container {
  background-color: #eee;
  position: relative;
}

.portrait {
  aspect-ratio: 0.5;
  width: 300px;
}

.landscape {
  aspect-ratio: 2;
  height: 300px;
}

.edit-mode {

  border: 1px dashed #888;
  padding: 5px;
  cursor:pointer;

}


.cdk-drag-preview {
  border: none;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
  0 8px 10px 1px rgba(0, 0, 0, 0.14),
  0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .widget-row:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
