import {After<PERSON>iewInit, Component, HostL<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {AuthenticatorService} from "@aws-amplify/ui-angular";
import {UserService} from "../../../../global/services";
import {EventService, LocalStorageService, RoleGuardService, URLService} from "../../../../services";
import {User} from "../../../../global/interfaces";
import {ICContainerGroupDTO} from "../../../../interfaces";
import {EventTypeEnum} from "../../../../enums";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MainComponent implements OnIni<PERSON>, On<PERSON><PERSON>roy, AfterViewInit {

  isMobile = false;
  isDevice = false;
  currentContainerGroup: ICContainerGroupDTO;
  public user: User;
  menuItems: any;
  defaultMenuItems: { itemRoute?: string; itemLabel?: string, subMenu?: any[], roles?: string[] }[] = [];
  private events: Subscription;
  globalContainerGroup: ICContainerGroupDTO = null;

  constructor(
    private route: ActivatedRoute,
    private redirect: URLService,
    private router: Router,
    private urlService: URLService,
    private localStorage: LocalStorageService,
    private userService: UserService,
    private authService: RoleGuardService,
    private eventService: EventService,
    public authenticated: AuthenticatorService,
  ) {

    this.defaultMenuItems = [
      {itemRoute: this.redirect.dashboard(true), itemLabel: "Dashboard"},
      {itemRoute: this.redirect.containerGroups(true), itemLabel: "Container Groups"},
      {itemRoute: this.redirect.layouts(true), itemLabel: "Layouts"},
      {itemRoute: this.redirect.widgets(true), itemLabel: "Widgets"},
      {itemRoute: this.redirect.inputs(true), itemLabel: "Inputs"},
      {itemRoute: this.redirect.inputCategories(true), itemLabel: "Input Categories"},
    ];
  }


  async ngOnInit() {

    await this.userService.loadCurrentUser().then(() => {
      this.user = this.userService.CurrentUser;
    });

    this.globalContainerGroup = this.localStorage.getGlobalContainerGroup();

    this.createNavs(this.router.url);
  }

  async ngAfterViewInit() {

    this.events = this.eventService.ICEvent.subscribe((x) => {

      if (x.eventType === EventTypeEnum.GlobalContainerGroupChange) {
        if (this.globalContainerGroup == null || this.globalContainerGroup.id != x.object?.id) {
          this.localStorage.set('globalContainerGroup', x.object);
          this.globalContainerGroup = x.object;
          window.location.reload();
        }
      }
    });
  }

  @HostListener('window:beforeunload')
  ngOnDestroy() {
    this.events.unsubscribe();
  }

  private createNavs(url: string) {

    this.menuItems = this.defaultMenuItems;

    // filter out menu items based on user roles
    const items = [];
    this.menuItems.flatMap(x => ({menu: x, roles: x.roles})).forEach(x => {
      if (!x.roles || this.authService.HasRole(this.user, x.roles)) {
        items.push(x.menu);
      }
    });

    this.menuItems = items;
  }

  changeContainerGroup() {

    this.eventService.ICEvent.emit({eventType: EventTypeEnum.GlobalContainerGroupChange, object: null});
    this.urlService.containerGroups();
  }
}
