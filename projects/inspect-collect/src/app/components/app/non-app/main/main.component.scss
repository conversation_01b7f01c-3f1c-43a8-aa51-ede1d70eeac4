links {

  .nav-link {
    display: flex !important;
  }

  .nav-label {
    flex-grow: 1;
  }

  .nav-caret {
    flex-shrink: 1;
    color: #aaa;
  }
}

.medium-input {

  .input-group-text {
    padding: 0.1rem 0.5rem !important;
    font-size: 0.875rem !important;
    height: 30px;
  }

  .form-control {
    padding: 0.1rem 0.5rem !important;
    line-height: 1 !important;
    height: 30px;
    font-size: 0.875rem !important;
  }
}


#filterValues {
  position: absolute;
  width: 400px;
  background-color: #fff;
  padding: 10px;
  z-index: 999;
  border-radius: 0 3px 3px 0;
  box-shadow: 0 5px 11px 0 rgba(0, 0, 0, .18), 0 4px 15px 0 rgba(0, 0, 0, .15);

}

#admin-side-nav {

  .side-nav {
    padding-top: 0;
    width: 200px !important;
    margin-top: 0px !important;

    a {
      line-height: 40px !important;
      font-weight: 500;
      font-size: 0.875rem !important;

      h5 {
        color: var(--sideNavTextColour)
      }
    }

    .collapsible .card-body li a {

      height: 40px !important;
      padding-left: 35px !important;
    }

    .collapsible .card .card-header a h5 {

      font-weight: 700;

      &:hover {

        color: var(--colour3) !important;

      }
    }
  }
}

#admin-main-panel {

  padding-top: 0 !important;
  margin-right: 0px !important;
  padding-right: 0px !important;
  padding-left: 0px !important;
  margin-left: 0px !important;
}

@media (min-width: 1200px) {

  #admin-main-panel {

    margin-left: 0rem !important;
    padding-left: 13rem !important;
    margin-right: 0px !important;
  }
}
