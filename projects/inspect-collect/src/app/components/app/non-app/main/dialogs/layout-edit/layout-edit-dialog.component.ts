import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {EventService} from "../../../../../../services";
import {LayoutService} from "../../../../../../services";
import {EventTypeEnum} from "../../../../../../enums";
import {ICContainerDTO, ICLayoutDTO} from "../../../../../../interfaces";
import {compare} from "fast-json-patch";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-layout-edit-dialog',
  templateUrl: './layout-edit-dialog.component.html',
  styleUrls: ['./layout-edit-dialog.component.scss'],
})

export class LayoutEditDialogComponent implements OnInit, OnD<PERSON>roy {

  @ViewChild("editLayoutModal") editLayoutModal: ModalDirective;

  editLayoutForm = new FormGroup({
    id: new FormControl(null, [Validators.required]),
    name: new FormControl(null, [Validators.required]),
    startingContainerId: new FormControl(null, [Validators.required]),
  });

  layout: ICLayoutDTO;
  preEditForm: any;
  containerGroupContainers: ICContainerDTO[] = [];
  private eventSub: Subscription;

  constructor(private eventService: EventService, private layoutService: LayoutService) {

  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  ngOnInit(): void {

    console.log("INIT");

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {

      if (data.eventType === EventTypeEnum.LayoutEditInit) {
        this.startEditLayout(data.object);
      }
      if (data.eventType === EventTypeEnum.LayoutAddInit) {
        this.startAddLayout();
      }
    });
  }

  async startAddLayout() {
    this.editLayoutForm.reset();
    this.preEditForm = {};
    this.editLayoutModal.show();
  }

  async startEditLayout(object) {

    this.editLayoutForm.reset();

    // If the object has a layout object, use it, otherwise get the layout from the layoutId
    if (object?.layout != null) {
      this.layout = object.layout;
    } else {
      await this.layoutService.get(object.layoutId).then((x) => {
        this.layout = x.dto as ICLayoutDTO;
      });
    }

    this.initEditLayoutForm(this.layout);
    this.preEditForm = Object.assign({}, this.editLayoutForm.value); // Save a copy
    this.editLayoutModal.show();
  }

  get f() {
    return this.editLayoutForm.value;
  }

  saveLayout() {

    const layoutId = this.f.id;

    const patch = compare(this.preEditForm, this.f);

    // If it's a patch, otherwise create
    if (layoutId != null) {
      if (patch.length > 0) {
        this.layoutService.patch(layoutId, patch).then(() => {
          this.eventService.ICEvent.emit({eventType: EventTypeEnum.LayoutSaved});
        });
        this.hideEditLayout();
      }
    } else {
      this.layoutService.create({
        name: this.f.name,
        firstContainerId: this.f.startingContainerId,
      }).then(() => {
        this.hideEditLayout();
        this.eventService.ICEvent.emit({eventType: EventTypeEnum.LayoutSaved});
      });
    }

  }

  initEditLayoutForm(layout: ICLayoutDTO) {
    this.editLayoutForm.patchValue({
      id: layout.id,
      name: layout.name,
      startingContainerId: layout.firstContainerId,
    });
  }

  hideEditLayout() {
    this.editLayoutModal.hide();
  }
}

