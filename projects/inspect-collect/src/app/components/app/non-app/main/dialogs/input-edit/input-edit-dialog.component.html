<div mdbModal #addInputModal="mdbModal" class="modal" tabindex="-1"
     aria-labelledby="editInputSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addInputModalLabel">Add Input</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="addInputModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="addInputForm">

          <div class="form-group narrow-select">
            <label for="category">Category Name</label>
            <mdb-select
              [outline]="true" id="category" formControlName="icInputCategoryId"
                        [options]="inputCategoryOptions"></mdb-select>
          </div>

          <div class="form-group">
            <label for="addInputName">Input Name</label>
            <input type="text" class="form-control" id="addInputName" formControlName="name"
                   placeholder="Enter Input name">
          </div>

          <input type="hidden" formControlName="icContainerGroupId" [value]="globalContainerGroup?.id" *ngIf="globalContainerGroup != null">

          <div class="form-group" *ngIf="globalContainerGroup == null">
            <label for="layoutName">Container Group Id</label>
            <mdb-select formControlName="icContainerGroupId" [outline]="true" visibleOptions="3"
                        [options]="containerGroupOptions"></mdb-select>
          </div>

        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="hideAddInput()">Close</button>
        <button type="button" class="btn btn-primary" (click)="saveAddInput()">Save</button>
      </div>
    </div>
  </div>
</div>

<div mdbModal #editInputModal="mdbModal" class="modal" tabindex="-1"
     aria-labelledby="editInputSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editInputModalLabel">Edit Input</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="editInputModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="editInputForm">

          <input type="hidden" formControlName="id">

          <div class="form-group">
            <label for="layoutName">Input Name</label>
            <input type="text" class="form-control" id="layoutName" formControlName="name"
                   placeholder="Enter input name">
          </div>

        </form>
      </div>
      <div class="modal-footer">
        <div class="w-100 d-flex">
          <div class="flex-grow-1">
            <button type="button" class="btn btn-danger mr-2" (click)="deleteInput()">Delete</button>
          </div>
          <div>
            <button type="button" class="btn btn-secondary mr-2" (click)="hideEditInput()">Close</button>
            <button type="button" class="btn btn-primary" (click)="saveEditInput()">Save</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
