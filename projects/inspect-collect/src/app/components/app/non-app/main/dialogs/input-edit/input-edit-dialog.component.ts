import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {
  ContainerGroupService,
  EventService,
  InputCategoryService,
  InputService,
  LocalStorageService
} from "../../../../../../services";
import {EventTypeEnum} from "../../../../../../enums";
import {ICInputCategoryDTO, ICInputCategoryTreeDTO} from "../../../../../../interfaces";
import {compare} from "fast-json-patch";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-input-edit-dialog',
  templateUrl: './input-edit-dialog.component.html',
  styleUrls: ['./input-edit-dialog.component.scss'],
})

export class InputEditDialogComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {

  @ViewChild("editInputModal") editInputModal: ModalDirective;
  @ViewChild("addInputModal") addInputModal: ModalDirective;

  addInputForm = new FormGroup({
    name: new FormControl(null, [Validators.required]),
    icContainerGroupId: new FormControl(null),
    icInputCategoryId: new FormControl(null),
  });

  editInputForm = new FormGroup({
    id: new FormControl(null, [Validators.required]),
    name: new FormControl(null, [Validators.required]),
    icContainerGroupId: new FormControl(null),
    icInputCategoryId: new FormControl(null),
  });

  input: ICInputCategoryDTO;
  preEditForm: any;
  private eventSub: Subscription;
  inputComponentOptions: { label: any; value: string }[] = [];
  inputs: ICInputCategoryDTO[] = [];
  containerGroupOptions: { label: string; value: string }[] = [];
  globalContainerGroup: any;
  inputCategoryOptions: { label: string, value: string }[] = [];
  private inputCategories: ICInputCategoryDTO[];
  private nestedCategoryTree: ICInputCategoryTreeDTO[];
  orderedCategories: ICInputCategoryTreeDTO[];

  constructor(
    private eventService: EventService,
    private localStorage: LocalStorageService,
    private containerGroupService: ContainerGroupService,
    private inputCategoryService: InputCategoryService,
    private inputService: InputService) {

  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  ngOnInit(): void {

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {

      if (data.eventType == EventTypeEnum.InputEditInit) {
        this.startEditInput(data.object).then();
      }
      if (data.eventType == EventTypeEnum.InputAddInit) {
        this.startAddInput().then();
      }
    });

    this.loadContainerGroups().then();
    this.loadInputCategories().then();
    this.globalContainerGroup = this.localStorage.getGlobalContainerGroup();

  }

  async startAddInput() {

    this.addInputForm.reset();
    if (this.globalContainerGroup != null) {
      this.addInputForm.patchValue({icContainerGroupId: this.globalContainerGroup.id});
    }
    this.preEditForm = {};
    this.addInputModal.show();
  }

  async startEditInput(object) {

    this.editInputForm.reset();

    // If the object has a input object, use it, otherwise get the input from the input
    if (object?.input != null) {
      this.input = object.input;
    } else {
      await this.inputService.get(object.icInputId).then((x) => {
        this.input = x.dto as ICInputCategoryDTO;
      });
    }

    this.initEditInputForm(this.input);
    this.preEditForm = Object.assign({}, this.editInputForm.value); // Save a copy
    this.editInputModal.show();
  }

  get ef() {
    return this.editInputForm.value;
  }

  get af() {
    return this.addInputForm.value;
  }

  saveAddInput() {

    this.inputService.create({
      name: this.af.name,
      icContainerGroupId: this.af.icContainerGroupId,
      icInputCategoryId: this.af.icInputCategoryId,
    }).then(() => {
      this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputSaved});
    });

    this.hideAddInput();
  }

  saveEditInput() {

    const inputId = this.ef.id;

    const patch = compare(this.preEditForm, this.ef);

    if (patch.length > 0) {
      this.inputService.patch(inputId, patch).then(() => {
        this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputSaved});
      });
    }
    this.hideEditInput();
  }

  initEditInputForm(input: ICInputCategoryDTO) {
    this.editInputForm.patchValue({
      id: input.id,
      name: input.name,
    });
  }

  hideEditInput() {
    this.editInputModal.hide();
  }

  hideAddInput() {
    this.addInputModal.hide();
  }


  deleteInput() {
    this.inputService.delete(this.input.id).then(() => {
      this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputDeleted});
      this.editInputModal.hide();
    });
  }

  loadContainerGroups() {

    return this.containerGroupService.search({component: 'input-edit-dialog'}).then((x) => {
      this.containerGroupOptions = x.results.map((x) => {
        return {label: x.name, value: x.id};
      });
    });
  }

  private loadInputCategories() {

    return this.inputCategoryService.search({component: 'input-edit-dialog'}).then((x) => {

      this.inputCategories = x.results as ICInputCategoryTreeDTO[];

      this.inputCategoryService.buildCategoryTree(this.inputCategories, null, 0);

      this.orderedCategories = this.inputCategoryService.buildOrderedCategories(this.inputCategories, null, 0);

      this.inputCategoryOptions = this.orderedCategories.map((x: ICInputCategoryTreeDTO) => {
        return {label: x.indentedLabel, value: x.id};
      });

    });
  }
}

