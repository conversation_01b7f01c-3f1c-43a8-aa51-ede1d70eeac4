import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {EventTypeEnum, ICContainerOrientationEnum} from "../../../../../../enums";
import {ICContainerDTO, ICContainerWidgetDTO, ICWidgetDTO} from "../../../../../../interfaces";
import {compare} from "fast-json-patch";
import {ContainerService, EventService, WidgetService} from "../../../../../../services";
import {ContainerWidgetService} from "../../../../../../services";
import {CdkDragDrop, moveItemInArray} from "@angular/cdk/drag-drop";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-container-edit-dialog',
  templateUrl: './container-edit-dialog.component.html',
  styleUrls: ['./container-edit-dialog.component.scss'],
})

export class ContainerEditDialogComponent implements OnInit, OnDestroy {

  @ViewChild("addContainerModal") addContainerModal: ModalDirective;
  @ViewChild("editContainerModal") editContainerModal: ModalDirective;

  orientations = [
    {label: "Portrait", value: 1},
    {label: "Landscape", value: 2},
    {label: "Either", value: 3}
  ];

  addContainerForm = new FormGroup({
    id: new FormControl(null, [Validators.required]),
    name: new FormControl(null, [Validators.required]),
    icContainerGroupId: new FormControl(null, [Validators.required]),
  });

  editContainerForm = new FormGroup({
    id: new FormControl(null, [Validators.required]),
    name: new FormControl(null, [Validators.required]),
    orientation: new FormControl(null, [Validators.required]),
    icContainerGroupId: new FormControl(null, [Validators.required]),
  });

  widgets: ICWidgetDTO[] = [];
  preEditForm: any;
  container: ICContainerDTO;
  savingEdit = false;
  containerOrientationEnum = ICContainerOrientationEnum;
  icContainerId: string;
  private eventSub: Subscription;

  constructor(
    private eventService: EventService,
    private widgetService: WidgetService,
    private containerWidgetService: ContainerWidgetService,
    private containerService: ContainerService) {
  }

  ngOnInit(): void {
    this.eventSub = this.eventService.ICEvent.subscribe((data) => {

      if (data.eventType === EventTypeEnum.ContainerEditInit) {
        this.startEditContainer(data.object).then();
      }
      if (data.eventType === EventTypeEnum.ContainerAddInit) {
        this.startAddContainer(data.object).then();
      }
      if (data.eventType === EventTypeEnum.ContainerWidgetDeleted) {
        this.fetchContainer().then();
      }

      if (data.eventType === EventTypeEnum.ContainerWidgetEditFormChanged) {
        console.log("DATA ", data.object);
        this.updateContainerWidget(data.object);
      }
    });
  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  async startAddContainer(object) {
    this.addContainerForm.reset();
    this.addContainerForm.patchValue({icContainerGroupId: object.icContainerGroupId});
    this.addContainerModal.show();
  }

  containerWidgetType(containerWidget: ICContainerWidgetDTO) {
    return containerWidget;
  }

  async startEditContainer(object) {

    this.editContainerForm.reset();

    this.icContainerId = object.icContainerId;

    this.fetchContainer().then(() => {
      this.initEditContainerForm(this.container);
      this.getWidgets(this.container.icContainerGroupId);
      this.preEditForm = Object.assign({}, this.editContainerForm.value); // Save a copy
      this.editContainerModal.show();
    });
  }

  async fetchContainer() {

    return this.containerService.get(this.icContainerId, {component: 'container-edit-dialog'}).then((x) => {
      this.container = x.dto as ICContainerDTO;
    });
  }

  get ef() {
    return this.editContainerForm.value;
  }

  get af() {
    return this.editContainerForm.value;
  }

  saveAddContainer() {

    this.containerService.create({
      name: this.af.name,
      icContainerGroupId: this.af.icContainerGroupId,
    }).then(() => {
      this.hideAddContainer();
      this.eventService.ICEvent.emit({eventType: EventTypeEnum.ContainerSaved});
    });

  }

  saveEditContainer() {

    const containerId = this.ef.id;
    const patch = compare(this.preEditForm, this.ef);
    this.savingEdit = true;

    if (patch.length > 0) {
      this.containerService.patch(containerId, patch).then((container) => {
        this.eventService.ICEvent.emit({eventType: EventTypeEnum.ContainerSaved, object: {icContainerId: containerId}});
        this.savingEdit = false;
      });
    } else {
      this.savingEdit = false;
    }
  }

  initEditContainerForm(container: ICContainerDTO) {
    this.editContainerForm.patchValue({
      id: container.id,
      icContainerGroupId: container.icContainerGroupId,
      orientation: container.orientation,
      name: container.name,
    });
  }

  hideEditContainer() {
    this.editContainerModal.hide();
  }

  hideAddContainer() {
    this.addContainerModal.hide();
  }

  setOrientation(orientation: number) {
    this.editContainerForm.patchValue({orientation});
  }

  initCreateWidget() {

    const icContainerGroupid = this.ef.icContainerGroupId;

    this.eventService.ICEvent.emit({
      eventType: EventTypeEnum.WidgetAddInit,
      object: {icContainerGroupId: icContainerGroupid}
    });
  }

  getWidgets(containerGroupId: string) {
    this.widgetService.search({
      component: 'container-edit',
      filters: {icContainerGroupId: containerGroupId}
    }).then((x) => {
      this.widgets = x.results;
    });
  }

  addWidgetToContainer(widget: ICWidgetDTO) {

    console.log(this.container.icContainerWidgets.length);

    this.containerWidgetService.create({
      icContainerId: this.icContainerId,
      icWidgetId: widget.id,
      position: this.container.icContainerWidgets.length,
      name: this.ef.name
    }).then(() => {
      this.fetchContainer().then();
    });
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.container.icContainerWidgets, event.previousIndex, event.currentIndex);
    this.updateWidgetPositions();
  }

  updateWidgetPositions() {
    this.container.icContainerWidgets.forEach((x, i) => {
      if (x.position !== i) {
        x.position = i;
        const patch = compare({}, {position: i});
        this.containerWidgetService.patch(x.id, patch).then();
      }
    });
  }

  editContainerWidget(containerWidget: ICContainerWidgetDTO) {

    this.eventService.ICEvent.emit({eventType: EventTypeEnum.ContainerWidgetEditInit, object: containerWidget});
  }

  private updateContainerWidget(object: any) {

    const input = object as ICContainerWidgetDTO;

    if (input != null) {

      let cw = this.container.icContainerWidgets.find((x) => x.id === input.id);

      if (cw != null) {
        cw.content = input.content;
      }
    }
  }

  sortedContainerWidgets(container: ICContainerDTO) {

    if (container == null) {
      return [];
    }

    return container.icContainerWidgets.sort((a, b) => {
      return a.position - b.position;
    });
  }
}

