<div class="d-flex">
  <div class="page-title flex-grow-1">
    Widgets
  </div>
  <div>
    <button class="btn btn-sm btn-primary" (click)="addWidget()">Add Widget</button>
  </div>
</div>

<table class="table table-hover">
  <thead>
  <tr>
    <th>Widget</th>
    <th>Component</th>
    <th>Inputs</th>
    <th>Status</th>
    <th>Added</th>
    <th>Updated</th>

  </tr>
  </thead>
  <tbody>
  <tr *ngFor="let widget of widgets" (click)="editWidget(widget)" class="cursor-pointer">
    <td>{{ widget.name }}
      <div class="desc">{{ widget.description }}</div>
    </td>
    <td>{{ widget.component }}</td>
    <td>{{ widget.icWidgetInputs }}</td>
    <td>{{ statusName[widget.statusId] }}</td>
    <td>{{ widget.added | date: "dd/MM/YY HH:mm"}}</td>
    <td>{{ widget.updated | date: "dd/MM/YY HH:mm"}}</td>
  </tr>
  </tbody>
</table>
