import {Component, OnInit, ViewEncapsulation, NgZone, Input} from '@angular/core';
import {BaseWidgetComponent} from "../base-widget.component";
import {text} from "d3";

@Component({
  selector: 'widget-link',
  templateUrl: './widget-link.component.html',
  styleUrls: ['./widget-link.component.scss'],
})

export class WidgetLinkComponent extends BaseWidgetComponent {

  @Input("content") content: string;

}
