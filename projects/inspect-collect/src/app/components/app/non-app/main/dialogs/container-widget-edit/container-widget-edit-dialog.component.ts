import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {EventTypeEnum, ICContainerOrientationEnum} from "../../../../../../enums";
import {ICContainerDTO, ICContainerWidgetDTO, ICWidgetDTO} from "../../../../../../interfaces";
import {compare} from "fast-json-patch";
import {ContainerWidgetService, EventService, WidgetService} from "../../../../../../services";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-container-widget-edit-dialog',
  templateUrl: './container-widget-edit-dialog.component.html',
  styleUrls: ['./container-widget-edit-dialog.component.scss'],
})

export class ContainerWidgetEditDialogComponent implements OnInit, OnD<PERSON>roy {

  @ViewChild("editContainerWidgetModal") editContainerWidgetModal: ModalDirective;

  editContainerWidgetForm = new FormGroup({
    id: new FormControl(null, [Validators.required]),
    name: new FormControl(null, [Validators.required]),
    content: new FormControl(null),
  });

  containerWidget: ICContainerWidgetDTO;
  savingEdit = false;
  preEditForm: {} = {};
  private eventSub: Subscription;
  private formEventSub: Subscription;

  constructor(
    private eventService: EventService,
    private widgetService: WidgetService,
    private containerWidgetService: ContainerWidgetService
  ) {

  }

  ngOnInit(): void {
    this.formEventSub = this.editContainerWidgetForm.valueChanges.subscribe(() => {
      this.eventService.ICEvent.emit({
        eventType: EventTypeEnum.ContainerWidgetEditFormChanged,
        object: this.editContainerWidgetForm.value
      });
    });

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {

      if (data.eventType === EventTypeEnum.ContainerWidgetEditInit) {
        this.startEditContainerWidget(data.object as ICContainerWidgetDTO).then();
      }
    });
  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
    this.formEventSub.unsubscribe();
  }

  async startEditContainerWidget(object: ICContainerWidgetDTO) {

    this.containerWidget = object;

    this.editContainerWidgetForm.reset();

    this.initEditContainerWidgetForm(this.containerWidget);
    this.preEditForm = Object.assign({}, this.editContainerWidgetForm.value); // Save a copy
    this.editContainerWidgetModal.show();
  }

  get ef() {
    return this.editContainerWidgetForm.value;
  }

  saveEditContainerWidget() {

    const containerWidgetId = this.ef.id;
    const patch = compare(this.preEditForm, this.ef);
    this.savingEdit = true;

    if (patch.length > 0) {
      this.containerWidgetService.patch(containerWidgetId, patch).then((container) => {
        this.eventService.ICEvent.emit({
          eventType: EventTypeEnum.ContainerSaved,
          object: {icContainerWidgetId: containerWidgetId}
        });
        this.savingEdit = false;
      });
    } else {
      this.savingEdit = false;
    }
  }

  initEditContainerWidgetForm(containerWidget: ICContainerWidgetDTO) {
    this.editContainerWidgetForm.patchValue({
      id: containerWidget.id,
      name: containerWidget.name,
      content: containerWidget.content
    });
  }

  hideEditContainerWidget() {
    this.editContainerWidgetModal.hide();
  }

  deleteContainerWidget() {

    this.containerWidgetService.delete(this.containerWidget.id).then(() => {
      this.eventService.ICEvent.emit({eventType: EventTypeEnum.ContainerWidgetDeleted});
      this.editContainerWidgetModal.hide();
    });
  }
}

