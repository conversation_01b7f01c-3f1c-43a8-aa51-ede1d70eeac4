<div class="d-flex">
  <div class="page-title flex-grow-1">Layout: {{ layout?.name }}</div>
  <div><button class="btn btn-sm btn-primary" (click)="startEditLayout()">Edit</button></div>
</div>

<div>
  <PERSON>roller
  <div style="height: 300px; width: 100%; background-color: #eee;">
    <ng-container [ngTemplateOutlet]="addContainerStrip"></ng-container>
    <div *ngFor="let container of containers" style="display: inline-block;">
      <ng-container [ngTemplateOutlet]="containerPreview"></ng-container>
      <ng-container [ngTemplateOutlet]="addContainerStrip"></ng-container>
    </div>
  </div>
</div>

<div class="d-flex mt-3">
  <div class="flex-grow-1">
    <app-container-view></app-container-view>
  </div>
  <div class="flex-grow-1">
    <app-container-list [icContainerGroupId]="layout?.icContainerGroupId"></app-container-list>
  </div>
</div>

<ng-template #containerPreview>
  <div
    style="width: 100px; border: 2px solid #000; height: 100%; display: inline-block; margin-left: 5px; margin-right: 5px;"></div>
</ng-template>

<ng-template #addContainerStrip>
  <div style="display: inline-block; position: relative; height: 100%; width: 20px; cursor: pointer;"
       (click)="startCreateContainer()">
    <div style="position: absolute; top: 0; left: 18px; ">
      <div style="background-color: orange; width: 4px; height: 290px;"></div>
    </div>
    <div style="position: absolute; bottom: 0; left: 10px; ">
      <span
        style="background-color: orange; display: inline-block; width: 20px; height: 20px; border-radius: 50%; text-align: center; line-height: 20px; color: #fff">+</span>
    </div>
  </div>
</ng-template>
