import {Component, OnInit, ViewChild} from '@angular/core';
import {ContainerGroupService, EventService, URLService} from "../../../../../services";
import {ModalDirective} from "ng-uikit-pro-standard";
import {ICContainerGroupDTO, ICLayoutDTO} from "../../../../../interfaces";
import {EventTypeEnum} from "../../../../../enums";

@Component({
  templateUrl: './container-group-list.component.html',
  styleUrls: ['./container-group-list.component.scss'],
})

export class ContainerGroupListComponent implements OnInit {

  @ViewChild('createContainerGroupModal') createContainerGroupModal: ModalDirective;

  containerGroups: ICContainerGroupDTO[] = [];
  newLayoutName: string;

  constructor(
    public url: URLService,
    private eventService: EventService,
    private containerGroupService: ContainerGroupService
  ) {

  }

  ngOnInit() {

    this.getContainerGroups();
  }

  getContainerGroups() {

    this.containerGroupService.search({component: 'container-group-list'}).then((x) => {

      this.containerGroups = x.results;

    });
  }

  showCreateContainerGroupModal() {

    this.createContainerGroupModal.show();
  }

  editContainerGroup(containerGroup: ICContainerGroupDTO) {

  }

  deleteContainerGroup(containerGroup: ICContainerGroupDTO) {
    this.containerGroupService.delete(containerGroup.id).then(() => {
      this.getContainerGroups();
    });
  }

  createContainerGroup() {

    this.containerGroupService.create({name: this.newLayoutName})
      .then((x) => {
        this.getContainerGroups();
      })
      .finally(() => {
        this.createContainerGroupModal.hide();
      });

  }

  viewContainerGroupLayouts(id: string) {
    this.url.containerGroupLayouts(id);
  }

  viewContainerGroupWidgets(id: string) {
    this.url.containerGroupWidgets(id);
  }

  selectContainerGroup(containerGroup: ICContainerGroupDTO) {

    console.log("CG ", containerGroup);

    this.eventService.ICEvent.emit({eventType: EventTypeEnum.GlobalContainerGroupChange, object: containerGroup});

  }
}
