<div mdbModal #addInputCategoryModal="mdbModal" class="modal" tabindex="-1"
     aria-labelledby="editInputSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addInputModalLabel">Input Category</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="addInputCategoryModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="addInputCategoryForm">

          <input type="hidden" formControlName="parentCategoryId">

          <div class="form-group" *ngIf="af.parentCategoryId">
            <label for="addInputName">Parent Category</label>
            <div><input disabled class="form-control" value="{{ parentCategoryName }}"></div>
          </div>

          <div class="form-group">
            <label for="addInputName">Category Name</label>
            <input type="text" class="form-control" id="addInputName" formControlName="name"
                   placeholder="Enter Category Name">
          </div>

          <div class="form-group">
            <label for="layoutName">Container Group Id</label>
            <mdb-select formControlName="icContainerGroupId"
                        [disabled]="globalContainerGroup?.id != null"
                        [outline]="true" visibleOptions="3"
                        [options]="containerGroupOptions"></mdb-select>
          </div>

        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="hideAddInput()">Close</button>
        <button type="button" class="btn btn-primary" (click)="saveAddInput()">Save</button>
      </div>
    </div>
  </div>
</div>

<div mdbModal #editInputCategoryModal="mdbModal" class="modal" tabindex="-1"
     aria-labelledby="editInputSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editInputModalLabel">Input Category</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="editInputCategoryModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="editInputCategoryForm">

          <input type="hidden" formControlName="id">

          <div class="form-group" *ngIf="ef.parentCategoryId">
            <label for="addInputName">Parent Category</label>
            <div><input disabled class="form-control" value="{{ parentCategoryName }}"></div>
          </div>

          <div class="form-group">
            <label for="layoutName">Category Name</label>
            <input type="text" class="form-control" id="layoutName" formControlName="name"
                   placeholder="Enter input name">
          </div>

        </form>
      </div>
      <div class="modal-footer">
        <div class="w-100 d-flex">
          <div class="flex-grow-1">
            <button type="button" class="btn btn-danger mr-2" (click)="deleteInput()">Delete</button>
          </div>
          <div>
            <button type="button" class="btn btn-secondary mr-2" (click)="hideEditInput()">Close</button>
            <button type="button" class="btn btn-primary" (click)="saveEditInput()">Save</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
