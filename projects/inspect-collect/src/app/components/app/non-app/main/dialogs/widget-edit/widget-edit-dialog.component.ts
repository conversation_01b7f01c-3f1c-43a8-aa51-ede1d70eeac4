import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewChild} from '@angular/core';
import {ModalDirective} from "ng-uikit-pro-standard";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {EventService, WidgetService} from "../../../../../../services";
import {EventTypeEnum} from "../../../../../../enums";
import {ICContainerDTO, ICWidgetDTO} from "../../../../../../interfaces";
import {compare} from "fast-json-patch";
import {Subscription} from "rxjs";
import {AppComponent} from "../../../../app.component";

@Component({
  selector: 'app-widget-edit-dialog',
  templateUrl: './widget-edit-dialog.component.html',
  styleUrls: ['./widget-edit-dialog.component.scss'],
})

export class WidgetEditDialogComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {

  @ViewChild("editWidgetModal") editWidgetModal: ModalDirective;
  @ViewChild("addWidgetModal") addWidgetModal: ModalDirective;

  addWidgetForm = new FormGroup({
    name: new FormControl(null, [Validators.required]),
    component: new FormControl(null),
    containerGroupId: new FormControl(null),
  });

  editWidgetForm = new FormGroup({
    id: new FormControl(null, [Validators.required]),
    name: new FormControl(null, [Validators.required]),
    containerGroupId: new FormControl(null),
    component: new FormControl(null),
  });

  widget: ICWidgetDTO;
  preEditForm: any;
  private eventSub: Subscription;
  widgetComponentOptions: { label: any; value: string }[] = [];
  widgets: ICWidgetDTO[] = [];

  constructor(private eventService: EventService, private widgetService: WidgetService) {

    const globalWidgets = AppComponent.globalWidgets;

    // Get object keys from environment.widgetComponents
    const keys = Object.keys(globalWidgets);

    this.widgetComponentOptions = keys.map((key) => {
      return {value: key, label: key + " : " + globalWidgets[key].component};
    });
  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  ngOnInit(): void {

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {

      if (data.eventType == EventTypeEnum.WidgetEditInit) {
        this.startEditWidget(data.object).then();
      }
      if (data.eventType == EventTypeEnum.WidgetAddInit) {
        this.startAddWidget().then();
      }
    });
  }

  async startAddWidget() {

    this.addWidgetForm.reset();
    this.preEditForm = {};
    this.addWidgetModal.show();
  }

  async startEditWidget(object) {

    this.editWidgetForm.reset();

    // If the object has a widget object, use it, otherwise get the widget from the widget
    if (object?.widget != null) {
      this.widget = object.widget;
    } else {
      await this.widgetService.get(object.icWidgetId).then((x) => {
        this.widget = x.dto as ICWidgetDTO;
      });
    }

    this.initEditWidgetForm(this.widget);
    this.preEditForm = Object.assign({}, this.editWidgetForm.value); // Save a copy
    this.editWidgetModal.show();
  }

  get ef() {
    return this.editWidgetForm.value;
  }

  get af() {
    return this.addWidgetForm.value;
  }

  saveAddWidget() {

    this.widgetService.create({
      name: this.af.name,
      component: this.af.component,
      containerGroupId: this.af.containerGroupId,
    }).then(() => {
      this.eventService.ICEvent.emit({eventType: EventTypeEnum.WidgetSaved});
    });

    this.hideAddWidget();
  }

  saveEditWidget() {

    const widgetId = this.ef.id;

    const patch = compare(this.preEditForm, this.ef);

    if (patch.length > 0) {
      this.widgetService.patch(widgetId, patch).then(() => {
        this.eventService.ICEvent.emit({eventType: EventTypeEnum.WidgetSaved});
      });
    }
    this.hideEditWidget();
  }

  initEditWidgetForm(widget: ICWidgetDTO) {
    this.editWidgetForm.patchValue({
      id: widget.id,
      name: widget.name,
      component: widget.component,
    });
  }

  hideEditWidget() {
    this.editWidgetModal.hide();
  }

  hideAddWidget() {
    this.addWidgetModal.hide();
  }


  deleteWidget() {
    this.widgetService.delete(this.widget.id).then(() => {
      this.eventService.ICEvent.emit({eventType: EventTypeEnum.WidgetDeleted});
      this.editWidgetModal.hide();
    });
  }
}

