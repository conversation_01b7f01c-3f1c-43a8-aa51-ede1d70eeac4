Container: {{ container?.id }} {{ container?.name }}

<ng-container [ngTemplateOutlet]="addWidgetStrip"></ng-container>
<div *ngFor="let widget of containerWidgets">
  <ng-container [ngTemplateOutlet]="addWidgetStrip"></ng-container>
</div>

<ng-template #addWidgetStrip>
  <div style="display: inline-block; position: relative; height: 20px; width: 100%; cursor: pointer;" (click)="startAddWidgetContainer()">
    <div style="position: absolute; top: 11px; left: 0px; width: 100%;">
      <div style="background-color: orange; width: 100%; height: 2px;"></div>
    </div>
    <div style="position: absolute; top: 0; right: 0; ">
      <span
        style="background-color: orange; display: inline-block; width: 15px; height: 15px; border-radius: 50%; text-align: center; line-height: 15px; color: #fff">+</span>
    </div>
  </div>
</ng-template>



