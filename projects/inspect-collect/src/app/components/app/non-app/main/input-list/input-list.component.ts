import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from '@angular/core';
import {ContainerGroupService, InputService, LocalStorageService, URLService} from "../../../../../services";
import {ICContainerGroupDTO, ICInputCategoryDTO, ICInputDTO} from "../../../../../interfaces";
import {ActivatedRoute} from "@angular/router";
import {EventTypeEnum} from "../../../../../enums";
import {EventService} from "../../../../../services";
import {Subscription} from "rxjs";

@Component({
  templateUrl: './input-list.component.html',
  styleUrls: ['./input-list.component.scss'],
})

export class InputListComponent implements OnInit, OnDestroy {

  inputs: ICInputDTO[] = [];
  icContainerGroupId: string;
  containerGroups: ICContainerGroupDTO[];
  containerGroupOptions: { label: string; value: string }[];
  globalContainerGroup: any;
  private eventSub: Subscription;

  constructor(
    public url: URLService,
    private route: ActivatedRoute,
    private inputService: InputService,
    private localStorage: LocalStorageService,
    private eventService: EventService,
    private containerGroupService: ContainerGroupService,
  ) {

  }

  ngOnInit() {

    this.getInputs();
    this.getContainerGroups();
    this.globalContainerGroup = this.localStorage.getGlobalContainerGroup();

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {
      if (data.eventType === EventTypeEnum.InputSaved) {
        this.getInputs();
      } else if (data.eventType === EventTypeEnum.InputDeleted) {
        this.getInputs();
      }
    });
  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  getInputs() {

    this.inputService.search({
      component: 'input-list',
      filters: {icContainerGroupId: this.icContainerGroupId}
    }).then((x) => {
      this.inputs = x.results;
    });
  }

  getContainerGroups() {

    this.containerGroupService.search({component: 'layout-list'}).then((x) => {
      this.containerGroups = x.results;
      this.containerGroupOptions = this.containerGroups.map((x) => {
        return {label: x.name, value: x.id};
      });
    });
  }

  addInput() {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.InputAddInit});
  }

  deleteInput(input: ICInputCategoryDTO) {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.LayoutDeleteInit, object: input});
  }

  containerGroupName(icContainerGroupId: string) {
    return this.containerGroups?.find((x) => x.id === icContainerGroupId)?.name;
  }

  editInput(input: ICInputCategoryDTO) {

  }
}
