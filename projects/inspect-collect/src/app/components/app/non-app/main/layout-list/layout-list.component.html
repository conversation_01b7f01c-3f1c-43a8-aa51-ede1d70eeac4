<div class="d-flex">

  <div class="flex-grow-1 page-title">
    Layouts
  </div>

  <div>
    <button class="btn btn-sm btn-primary" (click)="addLayout()">Add Layout</button>
  </div>
</div>

<table class="table table-hover">
  <thead>
  <tr>
    <th>Name</th>
    <th *ngIf="globalContainerGroup == null">Customer</th>
    <th>Status</th>
    <th>Added</th>
    <th>Updated</th>
    <th>&nbsp;</th>

  </tr>
  </thead>
  <tbody>
  <tr *ngFor="let layout of layouts">
    <td>{{ layout.name }}</td>
    <td *ngIf="globalContainerGroup == null">{{ containerGroupName(layout.icContainerGroupId) }}</td>
    <td>{{ statusName[layout.statusId] }}</td>
    <td>{{ layout.added | date: "dd/MM/yy" }}</td>
    <td>{{ layout.updated  | date: "dd/MM/yy" }}</td>
    <td>
      <button class="btn btn-sm btn-primary mr-2" (click)="url.viewLayout(layout.id)">Layout</button>
      <button class="btn btn-sm btn-primary mr-2" (click)="editLayout(layout)">Edit</button>
      <button class="btn btn-sm btn-danger" (click)="deleteLayout(layout)">Delete</button>
    </td>
  </tr>
  </tbody>
</table>

