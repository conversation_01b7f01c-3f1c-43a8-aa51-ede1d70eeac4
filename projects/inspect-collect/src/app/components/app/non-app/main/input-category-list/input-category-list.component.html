<div class="d-flex" style="border-bottom: 1px solid #eee; margin-bottom: 10px;">
  <div class="page-title flex-grow-1">Input Categories</div>
  <div>
    <button class="btn btn-sm btn-primary" (click)="addInputCategory(null)">Add Top Category</button>
  </div>
</div>

<div *ngFor="let category of topLevelCategories()">

  <ng-container [ngTemplateOutlet]="children" [ngTemplateOutletContext]="{$implicit: category }"></ng-container>

</div>

<ng-template #children let-category>
  <div style="margin-left: 40px; border-left:3px solid #000; position: relative;">
    <img src="/assets/images/icons/svg/nested-icon.svg" class="nested-icon"
         style="position: absolute; top: 9px; left: -7px;">
    <div style="display: inline-block; padding-left: 11px; padding-top: 10px;">
      <div class="d-flex">
        <div class="flex-shrink-1" (click)="editInputCategory(category)">
          <div class="category-name">{{ category.name || "N/A" }}
          </div>
        </div>
        <div>
          <div (click)="addInputCategory(category)"
               style="
           background-color: var(--primaryButtonColour);
           text-align: center;
           color: #fff;
           height: 14px; width: 14px;
           line-height: 14px;
           cursor: pointer;
           border-radius: 7px;
           vertical-align: middle;
           display: inline-block; margin-left: 10px;">
            +
          </div>
        </div>
      </div>
      <div *ngFor="let child of category.children">
        <ng-container [ngTemplateOutlet]="children" [ngTemplateOutletContext]="{ $implicit: child }"></ng-container>
      </div>
    </div>
  </div>
</ng-template>
