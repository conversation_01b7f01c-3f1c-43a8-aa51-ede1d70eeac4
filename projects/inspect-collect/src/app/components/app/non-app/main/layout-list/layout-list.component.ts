import {<PERSON>mponent, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild} from '@angular/core';
import {ContainerGroupService, LayoutService, URLService} from "../../../../../services";
import {ICContainerGroupDTO, ICLayoutDTO} from "../../../../../interfaces";
import {ActivatedRoute} from "@angular/router";
import {EventTypeEnum} from "../../../../../enums";
import {EventService} from "../../../../../services";
import {StatusEnum} from '../../../../../global/enums';
import {HelpersService} from '../../../../../global/services';
import {LocalStorageService} from "../../../../../services";
import {Subscription} from "rxjs";

@Component({
  templateUrl: './layout-list.component.html',
  styleUrls: ['./layout-list.component.scss'],
})

export class LayoutListComponent implements OnInit, OnDestroy {

  layouts: ICLayoutDTO[] = [];
  containerGroups: ICContainerGroupDTO[] = [];
  containerGroupOptions: { label: string, value: string }[] = [];
  icContainerGroupId: string;
  globalContainerGroup?: ICContainerGroupDTO;
  statusOptions: { label: string; value: number }[] = [];
  statusName: {} = {};
  private eventSub: Subscription;

  constructor(
    public url: URLService,
    private localStorage: LocalStorageService,
    private layoutService: LayoutService,
    private helpersService: HelpersService,
    private route: ActivatedRoute,
    private eventService: EventService,
    private containerGroupService: ContainerGroupService,
  ) {

    this.icContainerGroupId = this.route.snapshot.params.icContainerGroupId || null;
  }

  ngOnInit() {

    this.eventSub = this.eventService.ICEvent.subscribe((data) => {
      if (data.eventType === EventTypeEnum.LayoutSaved) {
        this.getLayouts();
      } else if (data.eventType === EventTypeEnum.LayoutDeleted) {
        this.getLayouts();
      }
    });

    this.getLayouts();
    this.getContainerGroups();
    this.globalContainerGroup = this.localStorage.getGlobalContainerGroup();
    this.initStatus();
  }

  ngOnDestroy() {
    this.eventSub.unsubscribe();
  }

  initStatus() {

    const statuses = this.helpersService.getNamesAndValues(StatusEnum);

    this.statusOptions = statuses.map(status => {
      this.statusName[status.value] = status.name;
      return {label: status.name, value: status.value};
    });
  }

  getLayouts() {

    this.layoutService.search({
      component: 'layout-list',
      filters: {icContainerGroupId: this.icContainerGroupId}
    }).then((x) => {
      this.layouts = x.results;
    });
  }

  getContainerGroups() {

    this.containerGroupService.search({component: 'layout-list'}).then((x) => {
      this.containerGroups = x.results;
      this.containerGroupOptions = this.containerGroups.map((x) => {
        return {label: x.name, value: x.id};
      });
    });
  }

  addLayout() {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.LayoutAddInit});
  }

  deleteLayout(layout: ICLayoutDTO) {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.LayoutDeleteInit, object: layout});
  }

  containerGroupName(icContainerGroupId: string) {
    return this.containerGroups?.find((x) => x.id === icContainerGroupId)?.name;
  }

  editLayout(layout: ICLayoutDTO) {

  }
}
