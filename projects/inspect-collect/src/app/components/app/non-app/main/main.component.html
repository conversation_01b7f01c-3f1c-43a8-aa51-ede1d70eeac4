<header id="admin-side-nav">
  <mdb-side-nav #sidenav class="" [fixed]="true" [sidenavBreakpoint]="1200">
    <div *ngIf="globalContainerGroup != null"
         style="border-bottom: 1px solid rgba(255,255,255, 0.5); padding: 10px 10px 10px 20px; font-size: 13px; background-color: rgba(255,255,255,0.1); color: cornsilk">
      {{ globalContainerGroup.name }}
      <div class="text-right mt-1"><button (click)="changeContainerGroup()" class="btn btn-xs" style="background-color: #000; color: rgba(255,255,255,0.5)">Change</button></div>
    </div>
    <app-side-menu [menuItems]="menuItems"></app-side-menu>
  </mdb-side-nav>

</header>

<!--/.Double navigation-->

<!--Main Layout-->
<main id="admin-main-panel">
  <div class="container-fluid mt-2">

    <div>
      <a (click)="sidenav.toggle()" class="d-block d-xl-none button-collapse hidden-nav-button-collapse mx-0 mb-2">
        <button class="btn btn-sm btn-primary">
          <mdb-icon fas icon="bars"></mdb-icon>
        </button>
      </a>
    </div>

    <router-outlet></router-outlet>
  </div>
</main>

<app-layout-edit-dialog></app-layout-edit-dialog>
<app-container-edit-dialog></app-container-edit-dialog>
<app-widget-edit-dialog></app-widget-edit-dialog>
<app-container-widget-edit-dialog></app-container-widget-edit-dialog>
<app-input-edit-dialog></app-input-edit-dialog>
<app-input-category-edit-dialog></app-input-category-edit-dialog>



