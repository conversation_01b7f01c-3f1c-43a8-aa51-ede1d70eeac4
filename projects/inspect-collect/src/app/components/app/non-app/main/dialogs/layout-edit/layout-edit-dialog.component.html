<div mdbModal #editLayoutModal="mdbModal" class="modal" tabindex="-1"
     aria-labelledby="editLayoutSettingsModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editLayoutModalLabel">Edit Layout</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="editLayoutModal.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="editLayoutForm">

          <input type="hidden" formControlName="id">

          <div class="form-group">
            <label for="layoutName">Layout Name</label>
            <input type="text" class="form-control" id="layoutName" formControlName="name"
                   placeholder="Enter layout name">
          </div>

          <div class="form-group">
            <label for="startingContainerId">Starting Container</label>
            <mdb-select-2 formControlName="startingContainerId" id="startingContainerId">
              <mdb-option *ngFor="let container of containerGroupContainers" value="container.id">{{ container.name }}
              </mdb-option>
            </mdb-select-2>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="hideEditLayout()">Close</button>
        <button type="button" class="btn btn-primary" (click)="saveLayout()">Save</button>
      </div>
    </div>
  </div>
</div>
