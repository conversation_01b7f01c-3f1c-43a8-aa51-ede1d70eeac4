import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {EventService, URLService, WidgetService} from "../../../../../services";
import {ICLayoutDTO, ICWidgetDTO} from "../../../../../interfaces";
import {EventTypeEnum} from "../../../../../enums";
import {Subscription} from "rxjs";
import {ActivatedRoute} from "@angular/router";
import {HelpersService} from "../../../../../global/services";
import {StatusEnum} from '../../../../../global/enums';

@Component({
  selector: 'app-widget-list',
  templateUrl: './widget-list.component.html',
  styleUrls: ['./widget-list.component.scss'],
})

export class WidgetListComponent implements OnInit, OnDestroy {

  widgets: ICWidgetDTO[] = [];
  private sub: Subscription;
  icContainerGroupId?: string;
  statusOptions: { label: string; value: number }[];
  statusName: {} = {};

  constructor(
    public url: URLService,
    private eventService: EventService,
    private route: ActivatedRoute,
    private helperService: HelpersService,
    private widgetService: WidgetService,
  ) {

    this.icContainerGroupId = this.route.snapshot.params.icContainerGroupId;

    this.statusOptions = this.helperService.getLabelsAndValues(StatusEnum);

    this.statusOptions.forEach(x => {
      this.statusName[x.value] = x.label;
    });
  }

  ngOnInit() {
    this.getWidgets();
    this.sub = this.eventService.ICEvent.subscribe((data) => {
      this.processEvents(data);
    });
  }

  ngOnDestroy() {
    this.sub.unsubscribe();
  }

  getWidgets() {

    this.widgetService.search({
      component: 'widget-list',
      filters: {icContainerGroupId: this.icContainerGroupId}
    }).then((x) => {
      this.widgets = x.results;
    });
  }

  addWidget() {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.WidgetAddInit, object: null});
  }

  processEvents(data) {
    if (data.eventType === EventTypeEnum.WidgetSaved) {
      this.getWidgets();
    }
    if (data.eventType === EventTypeEnum.WidgetDeleted) {
      this.getWidgets();
    }
  }

  editWidget(widget: ICWidgetDTO) {
    this.eventService.ICEvent.emit({eventType: EventTypeEnum.WidgetEditInit, object: {icWidgetId: widget.id}});
  }
}
