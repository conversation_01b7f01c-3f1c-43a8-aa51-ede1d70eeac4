import {Component, OnInit, ViewEncapsulation, NgZone, Input} from '@angular/core';
import {BaseWidgetComponent} from "../base-widget.component";
import {text} from "d3";

@Component({
  selector: 'widget-button',
  templateUrl: './widget-button.component.html',
  styleUrls: ['./widget-button.component.scss'],
})

export class WidgetButtonComponent extends BaseWidgetComponent {

  @Input("content") content: string;

}
