import {CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule} from '@angular/core';
import {RouterModule} from '@angular/router';
import {BrowserModule, Meta, Title} from '@angular/platform-browser';
import {IndexRoutingModule} from './modules/index-routing.module';
import {IndexModule} from './modules/index.module';
import {CommonModule, DatePipe} from '@angular/common';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {DataService, RoleGuardService as AuthGuard, URLService} from './services';
import {CookieService} from 'ngx-cookie-service';
import {RouteReuseStrategy} from '@angular/router';
import localeEnGb from '@angular/common/locales/en-GB';
import {registerLocaleData} from '@angular/common';
import {HttpClientModule} from "@angular/common/http";
import {HttpModule} from './modules/http.module';
import {CustomRouteReuseStrategy} from './global/utils';
import {AppComponent} from './components';
import {ModalModule, ToastModule} from "ng-uikit-pro-standard";
import {NgxImageZoomModule} from "ngx-image-zoom";
import {
  ApiService,
  AuthGuardServiceInterface,
  DataServiceInterface,
  LoggerService,
  URLServiceInterface
} from './global/services';
import {InspectLoggerModule} from "./modules/inspect-logger.module";

registerLocaleData(localeEnGb);

@NgModule({
  declarations: [],
  imports: [
    InspectLoggerModule,
    BrowserModule,
    BrowserAnimationsModule,
    NgxImageZoomModule,
    HttpModule,
    HttpClientModule,
    CommonModule,
    RouterModule,
    IndexRoutingModule,
    IndexModule,
    ModalModule.forRoot(),
    ToastModule.forRoot(),
  ],
  providers: [
    {provide: DataServiceInterface, useExisting: DataService},
    {provide: URLServiceInterface, useExisting: URLService},
    {provide: AuthGuardServiceInterface, useExisting: AuthGuard},
    AuthGuard,
    CookieService,
    {
      provide: RouteReuseStrategy,
      useClass: CustomRouteReuseStrategy
    },
    {provide: LOCALE_ID, useValue: "en-GB"},
    Title,
    Meta,
    ApiService,
    DatePipe,
    LoggerService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  bootstrap: [AppComponent],
  exports: []
})

export class AppModule {
}
