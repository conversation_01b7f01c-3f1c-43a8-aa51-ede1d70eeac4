import {Injectable} from '@angular/core';
import {
  ICContainerGroupSearchDTO,
  ICContainerGroupSearchResultDTO,
  ICLayoutDTO,
  ICLayoutSearchDTO,
  ICLayoutSearchResultDTO
} from "../interfaces";
import {ApiService} from '../global/services';
import {DataService} from "./data.service";

@Injectable({providedIn: 'root'})

export class ContainerGroupService {

  urlPath = '';

  constructor(private apiClient: ApiService,
              private data: DataService) {

    this.urlPath = `${this.data.apiUrl}/api/inspect-collect/container-group`;

  }

  search(query: ICContainerGroupSearchDTO): Promise<ICContainerGroupSearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/container-groups`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  create(dto: { name: string }) {
    return this.apiClient.post({url: this.urlPath, data: dto}) as Promise<ICLayoutDTO>;
  }

  delete(id: string) {
    const url = this.urlPath + "/" + id;
    return this.apiClient.delete({url}) as Promise<boolean>;
  }
}
