import {Injectable, Inject} from '@angular/core';
import {AppComponent} from "../components/index";
import {environment} from "../../environments/environment";
import {DomainData} from "../global/shared";
import {DataServiceInterface} from '../global/services';

@Injectable()
export class DataService implements DataServiceInterface {

  public apiUrl: string;
  public messageHubUrl: string;
  public googleMapsAPIKey: string;
  public stripePublicKey: string;
  public globals: DomainData;

  constructor() {

    if (environment.production) {

      const domain = document.domain;

      this.messageHubUrl = `${environment.messageHubUrl}`;
      this.apiUrl = AppComponent.globals.apiURL;
      this.googleMapsAPIKey = environment.googleMapsAPIKey;

    } else {

      this.messageHubUrl = `${environment.messageHubUrl}`;
      this.apiUrl = `${environment.serviceUrl}`;
      this.googleMapsAPIKey = environment.googleMapsAPIKey;
    }

    this.globals = AppComponent.globals;
  }


}
