import {Injectable} from '@angular/core';
import {ICLayoutDTO, ICLayoutSearchDTO, ICLayoutSearchResultDTO} from "../interfaces";
import {ApiService} from '../global/services';
import {DataService} from "./data.service";
import { ValidatedResultDTO } from '../global/interfaces';

@Injectable({providedIn: 'root'})

export class LayoutService {

  urlPath = '';

  constructor(private apiClient: ApiService,
              private data: DataService) {

    this.urlPath = `${this.data.apiUrl}/api/inspect-collect/layout`;

  }

  get(layoutId: string, query: ICLayoutSearchDTO = {}): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/layout/` + layoutId;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<ValidatedResultDTO>;
  }

  search(query: ICLayoutSearchDTO): Promise<ICLayoutSearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/layouts`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  create(dto: ICLayoutDTO) {
    return this.apiClient.post({url: this.urlPath, data: dto}) as Promise<ICLayoutDTO>;
  }

  patch(layoutId: string, patch: {}) {
    const url = `${this.data.apiUrl}/api/inspect-collect/layout/` + layoutId;
    return this.apiClient.patch({url: url, data: patch}) as Promise<ICLayoutDTO>;
  }

  delete(id: string) {

    const url = this.urlPath + "/" + id;
    return this.apiClient.delete({url}) as Promise<boolean>;
  }
}
