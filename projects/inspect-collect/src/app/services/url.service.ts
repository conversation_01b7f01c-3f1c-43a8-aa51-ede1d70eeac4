import {Inject, Injectable} from '@angular/core';
import {Router} from "@angular/router";
import {URLHelperService} from '../global/services';
import {DOCUMENT} from '@angular/common';

@Injectable({providedIn: 'root'})
export class URLService {

  constructor(
    @Inject(DOCUMENT) private document: Document,
    private router: Router, private urlHelperService: URLHelperService) {
    const localStorage = document.defaultView?.localStorage;
  }

  redirect(path, params = null, replaceUrl = false) {

    this.router.navigate([path], {replaceUrl, queryParams: params}).then(() => {
    });
  }

  homepage(returnOnly: boolean = false) {
    const path = "";
    if (returnOnly) {
      return path;
    } else {
      this.redirect(path);
    }
  }

  login(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/login", returnOnly);
  }

  signup(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/register", returnOnly);
  }

  dashboard(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/dashboard", returnOnly);
  }

  layouts(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/layouts", returnOnly);
  }

  containerGroups(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/container-groups", returnOnly);
  }

  widgets(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/widgets", returnOnly);
  }

  inputs(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/inputs", returnOnly);
  }

  inputCategories(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/input-categories", returnOnly);
  }

  viewLayout(layoutId: string, returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/layout/" + layoutId, returnOnly);
  }

  containerGroupLayouts(id: string, returnOnly: boolean = false, toUrl = false) {
    return this.urlHelperService.urlFrom("/main/container-group/" + id + "/layouts", returnOnly);
  }

  containerGroupWidgets(id: string, returnOnly: boolean = false, toUrl = false) {
    return this.urlHelperService.urlFrom("/main/container-group/" + id + "/widgets", returnOnly);
  }
}

