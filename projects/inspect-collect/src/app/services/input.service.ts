import {ApiService} from '../global/services';
import {DataService} from "./data.service";
import {ValidatedResultDTO} from '../global/interfaces';
import {Injectable} from "@angular/core";
import {ICInputDTO, ICInputSearchDTO, ICInputSearchResultDTO} from "../interfaces";

@Injectable({providedIn: 'root'})

export class InputService {

  urlPath = '';

  constructor(private apiClient: ApiService,
              private data: DataService) {

    this.urlPath = `${this.data.apiUrl}/api/inspect-collect/input`;

  }

  get(inputId: string, query: ICInputSearchDTO = {}): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/input/` + inputId;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<ValidatedResultDTO>;
  }

  search(query: ICInputSearchDTO): Promise<ICInputSearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/inputs`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  create(dto: ICInputDTO) {
    return this.apiClient.post({url: this.urlPath, data: dto}) as Promise<ICInputDTO>;
  }

  patch(inputId: string, patch: {}) {
    const url = `${this.data.apiUrl}/api/inspect-collect/input/` + inputId;
    return this.apiClient.patch({url: url, data: patch}) as Promise<ICInputDTO>;
  }

  delete(id: string) {

    const url = this.urlPath + "/" + id;
    return this.apiClient.delete({url}) as Promise<boolean>;
  }
}
