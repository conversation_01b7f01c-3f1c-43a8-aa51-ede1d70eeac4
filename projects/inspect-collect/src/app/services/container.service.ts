import {Injectable} from '@angular/core';
import {ICContainerDTO, ICContainerSearchDTO, ICContainerSearchResultDTO, ICLayoutDTO} from "../interfaces";
import {ApiService} from '../global/services';
import {DataService} from "./data.service";
import {ValidatedResultDTO} from '../global/interfaces';

@Injectable({providedIn: 'root'})

export class ContainerService {

  urlPath = '';

  constructor(private apiClient: ApiService,
              private data: DataService) {

    this.urlPath = `${this.data.apiUrl}/api/inspect-collect/container`;

  }

  get(containerId: string, query: ICContainerSearchDTO = {}): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/container/` + containerId;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<ValidatedResultDTO>;
  }

  layoutContainers(layoutId: string, query: ICContainerSearchDTO): Promise<ICContainerSearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/layout/` + layoutId + `/containers`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  search(query: ICContainerSearchDTO): Promise<ICContainerSearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/containers`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  patch(containerId: string, patch: {}) {
    const url = `${this.data.apiUrl}/api/inspect-collect/container/` + containerId;
    return this.apiClient.patch({url: url, data: patch}) as Promise<ICContainerDTO>;
  }

  containerGroupContainers(containerGroupId: string, query: ICContainerSearchDTO): Promise<ICContainerSearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/container-group/` + containerGroupId + "/containers";

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  create(dto: { name: string, icContainerGroupId: string }) {
    return this.apiClient.post({url: this.urlPath, data: dto}) as Promise<ICContainerDTO>;
  }

  delete(id: string) {

    const url = this.urlPath + "/" + id;
    return this.apiClient.delete({url}) as Promise<boolean>;
  }
}
