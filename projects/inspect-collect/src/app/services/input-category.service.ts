import {Injectable} from '@angular/core';
import {
  ICInputCategoryDTO,
  ICInputCategorySearchDTO, ICInputCategorySearchResultDTO, ICInputCategoryTreeDTO, ICLayoutSearchDTO, ICWidgetDTO
} from "../interfaces";
import {ApiService} from '../global/services';
import {DataService} from "./data.service";
import {ValidatedResultDTO} from '../global/interfaces';

@Injectable({providedIn: 'root'})

export class InputCategoryService {

  urlPath = '';

  constructor(private apiClient: ApiService,
              private data: DataService) {

    this.urlPath = `${this.data.apiUrl}/api/inspect-collect/input-category`;

  }

  search(query: ICInputCategorySearchDTO): Promise<ICInputCategorySearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/input-categories`;
    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  get(inputCategoryId: string, query: ICInputCategorySearchDTO = {}): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/input-category/` + inputCategoryId;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<ValidatedResultDTO>;
  }

  create(dto: {
    name: string,
    icContainerGroupId: string,
    parentCategoryId: string
  }) {
    return this.apiClient.post({url: this.urlPath, data: dto}) as Promise<ICInputCategoryDTO>;
  }

  delete(id: string) {
    const url = this.urlPath + "/" + id;
    return this.apiClient.delete({url}) as Promise<boolean>;
  }

  patch(inputCategoryId: string, patch: {}) {
    const url = `${this.data.apiUrl}/api/inspect-collect/input-category/` + inputCategoryId;
    return this.apiClient.patch({url, data: patch}) as Promise<ICInputCategoryDTO>;
  }

  buildCategoryTree(inputCategories: ICInputCategoryTreeDTO[], parentCategoryId?: string, level = 0): ICInputCategoryTreeDTO[] {
    const categories = inputCategories.filter((x) => x.parentCategoryId == parentCategoryId) as ICInputCategoryTreeDTO[];
    categories.forEach((x) => {
      x.children = this.buildCategoryTree(inputCategories, x.id, level + 1);
      x.level = level;
    });
    return categories;
  }

  buildOrderedCategories(inputCategories: ICInputCategoryTreeDTO[], parentCategoryId?: string, level = 0): ICInputCategoryTreeDTO[] {

    let returnCategories = [];

    const categories = inputCategories.filter((x) => x.parentCategoryId == parentCategoryId) as ICInputCategoryTreeDTO[];

    categories.forEach((x) => {

      x.level = level;
      x.indentedLabel = "——".repeat(level) + " " + x.name;
      returnCategories.push(x);
      returnCategories.push(...this.buildOrderedCategories(inputCategories, x.id, level + 1));
    });

    return returnCategories;
  }
}
