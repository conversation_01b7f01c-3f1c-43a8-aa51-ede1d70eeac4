import {Injectable} from '@angular/core';
import {
  ICWidgetDTO,
  ICWidgetSearchDTO, ICWidgetSearchResultDTO
} from "../interfaces";
import {ApiService} from '../global/services';
import {DataService} from "./data.service";
import {ValidatedResultDTO} from '../global/interfaces';

@Injectable({providedIn: 'root'})

export class WidgetService {

  urlPath = '';

  constructor(private apiClient: ApiService,
              private data: DataService) {

    this.urlPath = `${this.data.apiUrl}/api/inspect-collect/widget`;

  }

  get(widgetId: string, query: ICWidgetSearchDTO = {}): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/widget/` + widgetId;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<ValidatedResultDTO>;
  }

  search(query: ICWidgetSearchDTO): Promise<ICWidgetSearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/widgets`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  patch(widgetId: string, patch: {}) {
    const url = `${this.data.apiUrl}/api/inspect-collect/widget/` + widgetId;
    return this.apiClient.patch({url: url, data: patch}) as Promise<ICWidgetDTO>;
  }

  create(dto: { name: string, component: string, containerGroupId: string }) {
    return this.apiClient.post({url: this.urlPath, data: dto}) as Promise<ICWidgetDTO>;
  }

  delete(id: string) {

    console.log("DELETING ", id);

    const url = this.urlPath + "/" + id;
    return this.apiClient.delete({url}) as Promise<boolean>;
  }
}
