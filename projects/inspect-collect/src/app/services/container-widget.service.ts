import {Injectable} from '@angular/core';
import {
  ICContainerWidgetDTO,
  ICContainerWidgetSearchDTO, ICContainerWidgetSearchResultDTO
} from "../interfaces";
import {ApiService} from '../global/services';
import {DataService} from "./data.service";
import {ValidatedResultDTO} from '../global/interfaces';

@Injectable({providedIn: 'root'})

export class ContainerWidgetService {

  urlPath = '';

  constructor(private apiClient: ApiService,
              private data: DataService) {

    this.urlPath = `${this.data.apiUrl}/api/inspect-collect/container-widget`;

  }

  get(containerWidgetId: string, query: ICContainerWidgetSearchDTO = {}): Promise<ValidatedResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/container-widget/` + containerWidgetId;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<ValidatedResultDTO>;
  }

  search(query: ICContainerWidgetSearchDTO): Promise<ICContainerWidgetSearchResultDTO> {

    const url = `${this.data.apiUrl}/api/inspect-collect/container-widgets`;

    return this.apiClient.get({url, headers: {accept: 'application/json'}}, {query}) as Promise<any>;
  }

  patch(containerWidgetId: string, patch: {}) {
    const url = `${this.data.apiUrl}/api/inspect-collect/container-widget/` + containerWidgetId;
    return this.apiClient.patch({url: url, data: patch}) as Promise<ICContainerWidgetDTO>;
  }

  create(dto: ICContainerWidgetDTO) {
    return this.apiClient.post({url: this.urlPath, data: dto}) as Promise<ICContainerWidgetDTO>;
  }

  delete(id: string) {

    const url = this.urlPath + "/" + id;
    return this.apiClient.delete({url}) as Promise<boolean>;
  }
}
