import {NgModule} from '@angular/core';

import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {
  ButtonsModule,
  DropdownModule,
  MDBRootModules,
  MDBSpinningPreloader,
  ModalModule,
  TabsModule,
  TooltipModule
} from "ng-uikit-pro-standard";

import {HttpClientJsonpModule} from "@angular/common/http";
import {WebcamModule} from "ngx-webcam";
import {InspectAppRoutingModule} from "./inspect-app-routing.module";
import {CommonModule} from "@angular/common";
import {ApiService, ImageService} from "../global/services";
import {RouterModule} from "@angular/router";
import {InspectAppComponent} from "../components/app/inspect-app/inspect-app.component";


@NgModule({
  declarations: [
    InspectAppComponent,
  ],
  imports: [
    DropdownModule.forRoot(),
    ButtonsModule.forRoot(),
    ModalModule.forRoot(),
    TabsModule.forRoot(),
    TooltipModule.forRoot(),
    ReactiveFormsModule,
    InspectAppRoutingModule,
    HttpClientJsonpModule,
    WebcamModule,
    FormsModule,
    CommonModule,
    RouterModule
  ],
  providers: [
    ApiService,
    MDBSpinningPreloader,
    ImageService
  ],
  exports: []
})
export class InspectAppModule {
}
