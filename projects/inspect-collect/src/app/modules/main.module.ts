import {NonAppModule} from "./non-app-module";
import {MainComponent} from "../components/app/non-app/main/main.component";
import {MainRoutingModule} from "./main-routing.module";
import {RouterModule} from "@angular/router";
import {MDBBootstrapModulesPro, MDBRootModules, WavesModule} from "ng-uikit-pro-standard";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {NgModule} from "@angular/core";
import {LayoutService} from "../services";
import {HelpersService, ImageService, LoggerService, UserService} from "../global/services";
import {NgxImageZoomModule} from "ngx-image-zoom";
import {AmplifyAuthenticatorModule} from "@aws-amplify/ui-angular";
import {CommonModule} from "@angular/common";
import {HttpClientJsonpModule, HttpClientModule} from "@angular/common/http";
import {RoleGuardService as AuthGuard} from "../services";
import {AppSideMenuComponent} from "../global/components";
import {DashboardComponent} from "../components/app/non-app/main/dashboard/dashboard.component";
import {LayoutListComponent} from "../components/app/non-app/main/layout-list/layout-list.component";
import {
  ContainerGroupListComponent
} from "../components/app/non-app/main/container-group-list/container-group-list.component";
import {LayoutViewComponent} from "../components/app/non-app/main/layout-view/layout-view.component";
import {
  LayoutEditDialogComponent
} from "../components/app/non-app/main/dialogs/layout-edit/layout-edit-dialog.component";
import {EventService} from "../services";
import {
  ContainerEditDialogComponent
} from "../components/app/non-app/main/dialogs/container-edit/container-edit-dialog.component";
import {WidgetListComponent} from "../components/app/non-app/main/widget-list/widget-list.component";
import {ContainerViewComponent} from "../components/app/non-app/main/container-view/container-view.component";
import {
  WidgetEditDialogComponent
} from "../components/app/non-app/main/dialogs/widget-edit/widget-edit-dialog.component";
import {ContainerListComponent} from "../components/app/non-app/main/container-list/container-list.component";
import {ContainerWidgetService} from "../services";
import {IndexModule} from "./index.module";
import {
  ContainerWidgetEditDialogComponent
} from "../components/app/non-app/main/dialogs/container-widget-edit/container-widget-edit-dialog.component";
import {DndModule} from "ngx-drag-drop";
import {CdkDrag, CdkDropList} from "@angular/cdk/drag-drop";
import {InputListComponent} from "../components/app/non-app/main/input-list/input-list.component";
import {InputEditDialogComponent} from "../components/app/non-app/main/dialogs/input-edit/input-edit-dialog.component";
import {
  InputCategoryListComponent
} from "../components/app/non-app/main/input-category-list/input-category-list.component";
import {
  InputCategoryEditDialogComponent
} from "../components/app/non-app/main/dialogs/input-category-edit/input-category-edit-dialog.component";

@NgModule({
  declarations: [
    MainComponent,
    AppSideMenuComponent,
    DashboardComponent,
    LayoutListComponent,
    ContainerGroupListComponent,
    LayoutViewComponent,
    LayoutEditDialogComponent,
    ContainerEditDialogComponent,
    ContainerWidgetEditDialogComponent,
    ContainerViewComponent,
    ContainerListComponent,
    WidgetListComponent,
    WidgetEditDialogComponent,
    InputListComponent,
    InputCategoryListComponent,
    InputEditDialogComponent,
    InputCategoryEditDialogComponent
  ],
  imports: [
    NonAppModule,
    MDBBootstrapModulesPro.forRoot(),
    MainRoutingModule,
    RouterModule,
    MDBRootModules,
    WavesModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    CommonModule,
    AmplifyAuthenticatorModule,
    NgxImageZoomModule,
    IndexModule,
    DndModule,
    CdkDropList,
    CdkDrag,
  ],
  providers: [
    AuthGuard,
    LayoutService,
    ImageService,
    HelpersService,
    UserService,
    LoggerService,
    ContainerWidgetService,
    EventService,
  ],
  exports: []
})

export class MainModule {
}
