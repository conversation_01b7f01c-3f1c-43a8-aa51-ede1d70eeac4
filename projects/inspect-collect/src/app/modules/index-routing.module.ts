import {NgModule} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {Routes, RouterModule} from '@angular/router';

const routes: Routes = [

  { path: '', loadChildren: () => import("./non-app-module").then(m => m.NonAppModule) },
  { path: 'inspect-collect', loadChildren: () => import('./inspect-app.module').then(m => m.InspectAppModule) },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
    scrollPositionRestoration: 'enabled'
})],
  exports: [RouterModule, FormsModule],
  providers: []
})

export class IndexRoutingModule {
}
