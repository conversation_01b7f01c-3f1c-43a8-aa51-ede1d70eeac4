import {NgModule} from '@angular/core';
import {RouterModule} from "@angular/router";
import {HTTP_INTERCEPTORS} from "@angular/common/http";
import {HeaderInterceptor} from "../interceptors/config.interceptor";
import {DataService, LocalStorageService} from "../services";
import {CommonModule} from "@angular/common";
import {AppComponent} from "../components";
import {ApiService} from "../global/services";
import {WidgetTextBoxComponent} from "../components/app/non-app/main/widgets/text-box/widget-text-box.component";
import {WidgetButtonComponent} from "../components/app/non-app/main/widgets/button/widget-button.component";
import {WidgetLinkComponent} from "../components/app/non-app/main/widgets/link/widget-link.component";

@NgModule({
  declarations: [
    AppComponent,
    WidgetTextBoxComponent,
    WidgetButtonComponent,
    WidgetLinkComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HeaderInterceptor,
      multi: true
    },
    ApiService,
    DataService,
    LocalStorageService
  ],
  exports: [
    RouterModule,
    WidgetTextBoxComponent,
    WidgetButtonComponent,
    WidgetLinkComponent,
  ]
})
export class IndexModule {
}
