import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import {NonAppComponent} from "../components/app/non-app/non-app.component";

const routes: Routes = [
  {
    path: '',
    component: NonAppComponent,
    children: [
      {path: 'main', loadChildren: () => import('./main.module').then(m => m.MainModule)},
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
})

export class NonAppRoutingModule {
}
