import {NgModule} from '@angular/core';

import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {
  ButtonsModule,
  DropdownModule, MDBBootstrapModulesPro,
  MDBSpinningPreloader,
  ModalModule,
  TabsModule,
  TooltipModule
} from "ng-uikit-pro-standard";

import {HttpClientJsonpModule} from "@angular/common/http";
import {WebcamModule} from "ngx-webcam";
import {CommonModule} from "@angular/common";
import {ApiService, ImageService} from "../global/services";
import {RouterModule} from "@angular/router";
import {NonAppRoutingModule} from "./non-app-routing.module";
import {NonAppComponent} from "../components/app/non-app/non-app.component";
import {EventService} from "../services";
import {IndexModule} from "./index.module";


@NgModule({
  declarations: [
    NonAppComponent,
  ],
  imports: [
    IndexModule,
    NonAppRoutingModule,
    DropdownModule.forRoot(),
    ButtonsModule,
    ModalModule,
    TabsModule,
    TooltipModule,
    ReactiveFormsModule,
    HttpClientJsonpModule,
    WebcamModule,
    FormsModule,
    CommonModule,
    RouterModule
  ],
  providers: [
    ApiService,
    MDBSpinningPreloader,
    ImageService,
    EventService
  ],
  exports: []
})
export class NonAppModule {
}
