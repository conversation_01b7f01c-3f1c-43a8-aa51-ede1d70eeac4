import {NgModule} from "@angular/core";
import {RouterModule, Routes} from "@angular/router";
import {MainComponent} from "../components/app/non-app/main/main.component";
import {LayoutListComponent} from "../components/app/non-app/main/layout-list/layout-list.component";
import {DashboardComponent} from "../components/app/non-app/main/dashboard/dashboard.component";
import {
  ContainerGroupListComponent
} from "../components/app/non-app/main/container-group-list/container-group-list.component";
import {LayoutViewComponent} from "../components/app/non-app/main/layout-view/layout-view.component";
import {WidgetListComponent} from "../components/app/non-app/main/widget-list/widget-list.component";
import {InputListComponent} from "../components/app/non-app/main/input-list/input-list.component";
import {
  InputCategoryListComponent
} from "../components/app/non-app/main/input-category-list/input-category-list.component";

const routes: Routes = [
  {
    path: '',
    component: MainComponent,
    children: [
      {path: 'container-groups', component: ContainerGroupListComponent},
      {path: 'container-group/:icContainerGroupId/layouts', component: LayoutListComponent},
      {path: 'container-group/:icContainerGroupId/widgets', component: WidgetListComponent},
      {path: 'layouts', component: LayoutListComponent},
      {path: 'widgets', component: WidgetListComponent},
      {path: 'inputs', component: InputListComponent},
      {path: 'input-categories', component: InputCategoryListComponent},
      {path: 'dashboard', component: DashboardComponent},
      {
        path: 'layout',
        children: [
          {path: ':layoutId', component: LayoutViewComponent},
        ]
      }
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
})

export class MainRoutingModule {
}
