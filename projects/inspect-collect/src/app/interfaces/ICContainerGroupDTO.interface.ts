import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {ICContainerDTO} from "./ICContainerDTO.interface";
import {ICLayoutDTO} from "./ICLayoutDTO.interface";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICContainerGroupDTO extends BaseDTO {
  name: string;
  icContainers: ICContainerDTO[];
  icLayouts: ICLayoutDTO[];
}

export interface ICContainerGroupSearchDTO extends BaseSearchDTO{
  filters?: ICContainerGroupSearchFilters;
}

export interface ICContainerGroupSearchFilters {
  name?: string;
  statusId?: number;
  ignoreDeleted?: boolean;
}

export interface ICContainerGroupCreateDTO {
  name: string;
}

export interface ICContainerGroupSearchResultDTO extends TypedSearchResultDTO<ICContainerGroupDTO> {

}
