import {ICWidgetInputDTO} from "./ICWidgetInputDTO.interface";
import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICWidgetDTO extends BaseDTO {
  name: string;
  description: string;
  component: string;
  icContainerGroupId?: string;
  icWidgetInputs?: ICWidgetInputDTO[];
}

export interface ICWidgetSearchDTO extends BaseSearchDTO {
  filters?: ICWidgetSearchFilters;
}

export interface ICWidgetSearchFilters {
  name?: string;
  icContainerGroupId?: string;
}

export interface ICWidgetCreateDTO {
  name: string;
  component: string;
}

export interface ICWidgetSearchResultDTO extends TypedSearchResultDTO<ICWidgetDTO> {

}
