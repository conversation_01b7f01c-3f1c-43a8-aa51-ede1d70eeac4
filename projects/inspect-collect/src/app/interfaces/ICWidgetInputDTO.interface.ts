import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICWidgetInputDTO extends BaseDTO {
  icWidgetId: string;
  icInputId: string;
}

export interface ICWidgetInputSearchDTO extends BaseSearchDTO {
  filters?: ICWidgetInputSearchFilters;
}

export interface ICWidgetInputSearchFilters {
  // Add properties as needed
}

export interface ICWidgetInputCreateDTO {
  icWidgetId: string;
  icInputId: string;
}

export interface ICWidgetInputSearchResultDTO extends TypedSearchResultDTO<ICWidgetInputDTO> {

}
