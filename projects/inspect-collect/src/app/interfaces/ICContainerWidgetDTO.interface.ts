import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {ICContainerGroupDTO} from "./ICContainerGroupDTO.interface";
import {BaseDTO} from "./_baseDTO.interface";
import {ICWidgetDTO} from "./ICWidgetDTO.interface";
import {ICContainerWidgetInputDTO} from "./ICContainerWidgetInputDTO.interface";

export interface ICContainerWidgetDTO {
  id?: string;
  icContainerId?: string;
  icWidgetId?: string;
  position?: number;
  name?: string;
  label?: string;
  content?: string;
  collapsible?: boolean;
  icWidget?: ICWidgetDTO;
  icContainerWidgetInputs?: ICContainerWidgetInputDTO[];
}

export interface ICContainerWidgetSearchDTO extends BaseSearchDTO {
  filters?: ICContainerWidgetSearchFilters;
}

export interface ICContainerWidgetSearchFilters {
  name: string;
}

export interface ICContainerWidgetCreateDTO {
  name: string;
}

export interface ICContainerWidgetSearchResultDTO extends TypedSearchResultDTO<ICContainerWidgetDTO> {

}
