import {ICResponseInputValueDTO} from "./ICResponseInputValueDTO.interface";
import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICResponseInputDTO extends BaseDTO {
  icResponseId?: string;
  icInputId?: string;
  icResponseInputValues: ICResponseInputValueDTO[];
}

export interface ICResponseInputSearchDTO extends BaseSearchDTO {
  filters?: ICResponseInputSearchFilters;
}

export interface ICResponseInputSearchFilters {
  // Add properties as needed
}

export interface ICResponseInputCreateDTO {
  icResponseId?: string;
  icInputId?: string;
}

export interface ICResponseInputSearchResultDTO extends TypedSearchResultDTO<ICResponseInputDTO> {

}
