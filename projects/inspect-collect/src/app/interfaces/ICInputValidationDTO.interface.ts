import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICInputValidationDTO extends BaseDTO {
  name: string;
  icInputId: string;
  validationType: string;
  validationRegExp: string;
}

export interface ICInputValidationSearchDTO extends BaseSearchDTO {
  filters?: ICInputValidationSearchFilters;
}

export interface ICInputValidationSearchFilters {
  name: string;
}

export interface ICInputValidationCreateDTO {
  name: string;
  icInputId: string;
  validationType: string;
  validationRegExp: string;
}

export interface ICInputValidationSearchResultDTO extends TypedSearchResultDTO<ICInputValidationDTO> {

}
