import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {ICContainerWidgetDTO} from "./ICContainerWidgetDTO.interface";
import {BaseDTO} from "./_baseDTO.interface";
import {ICContainerAlignmentEnum, ICContainerOrientationEnum} from "../enums";

export interface ICContainerDTO extends BaseDTO {
  name: string;
  helpContainerId?: string;
  nextContainerId?: string;
  isHelpContainer?: boolean;
  autoShowHelp?: boolean;
  orientation?: ICContainerOrientationEnum;
  alignment?: ICContainerAlignmentEnum;
  background?: string;
  icContainerGroupId?: string;
  icContainerWidgets: ICContainerWidgetDTO[];
}

export interface ICContainerSearchDTO extends BaseSearchDTO {
  filters?: ICContainerSearchFilters;
}

export interface ICContainerSearchFilters {
  name?: string;
  icContainerGroupId?: string;
}

export interface ICContainerCreateDTO {
  name: string;
}

export interface ICContainerSearchResultDTO extends TypedSearchResultDTO<ICContainerDTO> {

}
