import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {ICContainerDTO} from "./ICContainerDTO.interface";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICInputOptionDTO extends BaseDTO {
  label: string;
  value: string;
  nextContainerId?: string;
  nextContainer?: ICContainerDTO;
}

export interface ICInputOptionSearchDTO extends BaseSearchDTO {
  filters?: ICInputOptionSearchFilters;
}

export interface ICInputOptionSearchFilters {
  name: string;
}

export interface ICInputOptionCreateDTO {
  label: string;
  value: string;
}

export interface ICInputOptionSearchResultDTO extends TypedSearchResultDTO<ICInputOptionDTO> {

}
