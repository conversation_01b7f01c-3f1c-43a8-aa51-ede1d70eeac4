import {BaseSearchDTO, TypedSearchResultDTO } from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICLayoutDTO extends BaseDTO {
  name: string;
  firstContainerId?: string;
  lastContainerId?: string;
  icContainerGroupId?: string;
}

export interface ICLayoutSearchDTO extends BaseSearchDTO {
  filters?: ICLayoutFilters;
}

export interface ICLayoutFilters {
  name?: string;
  firstContainerId?: string;
  lastContainerId?: string;
  icContainerGroupId?: string;
}

export interface ICLayoutCreateDTO {
  name: string;
  firstContainerId?: string;
  lastContainerId?: string;
  icContainerGroupId?: string;
}

export interface ICLayoutSearchResultDTO extends TypedSearchResultDTO<ICLayoutDTO> {

}
