import {ICInputValidationDTO} from "./ICInputValidationDTO.interface";
import {ICInputOptionDTO} from "./ICInputOptionDTO.interface";
import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";
import {ICContainerGroupDTO} from "./ICContainerGroupDTO.interface";

export interface ICInputDTO extends BaseDTO {
  name: string;
  dataType?: string;
  icInputCategoryId?: string;
  explanation?: string;
  maxItems?: number;
  minItems?: number;
  icInputValidations?: ICInputValidationDTO[];
  icInputOptions?: ICInputOptionDTO[];
  icContainerGroup?: ICContainerGroupDTO;
  icContainerGroupId?: string;
}

export interface ICInputSearchDTO extends BaseSearchDTO {
  filters?: ICInputSearchFilters;
}

export interface ICInputSearchFilters {
  name?: string;
  icInputCategoryId?: string;
  icContainerGroupId?: string;
}

export interface ICInputCreateDTO {
  name: string;
  dataType: string;
  explanation: string;
  maxItems?: number;
  minItems?: number;
}

export interface ICInputSearchResultDTO extends TypedSearchResultDTO<ICInputDTO> {

}
