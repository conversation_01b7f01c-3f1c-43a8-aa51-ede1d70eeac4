import {ICResponseInputDTO} from "./ICResponseInputDTO.interface";
import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICResponseDTO extends BaseDTO {
  name: string;
  icResponseInputs: ICResponseInputDTO[];
  responseStarted: Date;
  responseGeoIP: string;
}

export interface ICResponseSearchDTO extends BaseSearchDTO {
  filters?: ICResponseSearchFilters;
}

export interface ICResponseSearchFilters {
  name: string;
}

export interface ICResponseCreateDTO {
  name: string;
}

export interface ICResponseSearchResultDTO extends TypedSearchResultDTO<ICResponseDTO> {

}
