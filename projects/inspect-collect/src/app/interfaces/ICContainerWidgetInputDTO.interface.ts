import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {ICContainerWidgetDTO} from "./ICContainerWidgetDTO.interface";
import {ICInputDTO} from "./ICInputDTO.interface";

export interface ICContainerWidgetInputDTO {
  icContainerWidgetId: string;
  icContainerWidget: ICContainerWidgetDTO;
  icInputId: string;
  icInput: ICInputDTO;
}

export interface ICContainerWidgetInputSearchDTO extends BaseSearchDTO {
  filters?: ICContainerWidgetInputSearchFilters;
}

export interface ICContainerWidgetInputSearchFilters {
  name: string;
}

export interface ICContainerWidgetInputCreateDTO {
  name: string;
  label: string;
  collapsible?: boolean;
}

export interface ICContainerWidgetInputSearchResultDTO extends TypedSearchResultDTO<ICContainerWidgetInputDTO> {

}
