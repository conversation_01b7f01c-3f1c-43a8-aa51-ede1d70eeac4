import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";
import {ICContainerGroupDTO} from "./ICContainerGroupDTO.interface";
import {ICInputDTO} from "./ICInputDTO.interface";

export interface ICInputCategoryDTO extends BaseDTO {
  name: string;
  parentCategoryId?: string;
  parentCategory?: ICInputCategoryDTO;

  icContainerGroupId?: string;
  icContainerGroup?: ICContainerGroupDTO;
  icInputs: ICInputDTO[];
}

export interface ICInputCategoryTreeDTO extends ICInputCategoryDTO {
  children?: ICInputCategoryDTO[];
  level?: number;
  indentedLabel?: string;
}

export interface ICInputCategorySearchDTO extends BaseSearchDTO {
  filters?: ICInputCategorySearchFilters;
}

export interface ICInputCategorySearchFilters {
  name?: string;
  icContainerGroupId?: string;
  parentCategoryId?: string;
}

export interface ICInputCategoryCreateDTO {
  name: string;
  parentCategoryId: string;
  icContainerGroupId: string;
}

export interface ICInputCategorySearchResultDTO extends TypedSearchResultDTO<ICInputCategoryDTO> {

}
