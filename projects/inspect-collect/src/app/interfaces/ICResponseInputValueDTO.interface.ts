import {BaseSearchDTO, TypedSearchResultDTO} from "../global/interfaces";
import {BaseDTO} from "./_baseDTO.interface";

export interface ICResponseInputValueDTO extends BaseDTO {
  icResponseInputId?: string;
  value: string;
}

export interface ICResponseInputValueSearchDTO extends BaseSearchDTO {
  filters?: ICResponseInputValueSearchFilters;
}

export interface ICResponseInputValueSearchFilters {
  // Add properties as needed
}

export interface ICResponseInputValueCreateDTO {
  icResponseInputId?: string;
  value: string;
}

export interface ICInputResponseInputValueSearchResultDTO extends TypedSearchResultDTO<ICResponseInputValueDTO> {

}
