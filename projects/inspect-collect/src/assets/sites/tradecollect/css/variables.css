:root {

  [data-amplify-authenticator] {
    --amplify-colors-font-primary: #21303D;
    --amplify-components-button-primary-background-color: var(--linkColour);
    --amplify-components-tabs-item-active-color: var(--linkColour);
    --amplify-components-tabs-item-active-border-color: var(--linkColour);
    --amplify-components-tabs-item-hover-color: var(--linkColour);

    color: #21303D;

    .amplify-authenticator__subtitle {
      font-weight: 400;
    }

    .amplify-input {
      font-weight: 500;
    }

    .amplify-label {
      font-weight: 500;
    }

    .amplify-divider {
      opacity: 100% !important;
      color: var(--textColour);
    }

    .amplify-divider::after {
      font-weight: 500;
      margin-top: 1rem;
    }

    .federated-sign-in-button {
      border: 1.5px solid var(--linkColour);

    }

    .federated-sign-in-container {
      border-bottom: 1px solid #dfdfdf;
      padding-bottom: 1rem;
      margin-bottom: 2rem;

      .amplify-button {

        .amplify-text {
          font-weight: 500;
          color: var(--linkColour);
        }
      }
    }
  }


  --headerHeight: 64px;
  --underNavHeight: 30px;

  --font1: "Arial";
  --fontAwesome: "Font Awesome\ 5 Free";
  --defaultFont: var(--font1);

  /* Palette */
  --colour1: #303e4d;
  --colour2: #6698c8;
  --colour2a: rgba(18, 100, 163, 0.7);
  --colour2b: rgba(18, 100, 163, 0.5);
  --colour2c: rgba(18, 100, 163, 0.3);
  --colour3: rgba(255, 255, 255, 1);
  --colour3a: rgba(255, 255, 255, 0.7);
  --colour3b: rgba(255, 255, 255, 0.35);
  --colour3c: rgba(255, 255, 255, 0.17);
  --colour4: #4a5664;
  --colour5: #466AB1;
  --colour6: #78af8f;
  --colour7: #2c3849;
  --colour8: #007a5a;
  --colour9: #b7bbc1;
  --colour10: rgba(29, 28, 29, 1);
  --colour10a: rgba(29, 28, 29, 0.7);
  --colour10b: rgba(29, 28, 29, 0.35);
  --colour10c: rgba(29, 28, 29, 0.17);
  --colour10d: rgba(29, 28, 29, 0.08);
  --colour10e: rgba(29, 28, 29, 0.04);
  --colour12: #0f6cbd;
  --colour13: #f8f8f8;
  --colour14: #e0eee7;
  --colour15: #232E49;
  --vrmBackgroundColour: #FFFFCC;

  --modalHeaderText-colour: var(--textColour);
  --row-selected-background-colour: #badbf8;

  --text-emphasis: var(--colour10);
  --text-plain: var(--colour10a);

  --inputBorderColour: var(--colour10b);
  --softInputBorderColour: #ced4da;
  --placeholderColour: var(--colour10b);

  --bgColour: var(--colour10e);
  --primaryButtonColour: #0F6CBD;
  --smallCheckboxColour: #0F6CBD;
  --secondaryButtonColour: #29567D;
  --tertiaryButtonColour: var(--colour13);
  --topbarIconColour: #ffffff;

  --modalHeaderBackgroundColour: #eee;
  --modalHeaderFontWeight: 600;
  --modalHeaderFontSize: 1rem;

  --sideNavBackgroundColour: #f5f5f5;
  --sideNavTextColour: var(--textColour);

  --sideNavHoverBackgroundColour: var(--colour12);
  --sideNavHoverTextColour: var(--colour3);
  --tabFocusColour: #456BB4;
  --successColour: #09B677;
  --warningColour: #ffc107;
  --dangerColour: #DC3545;
  --errorColour: #DC3545;
  --textColour: #292929;
  --linkColour: #0f6cbd;
  --textLabelColour: var(--colour10a);
  --actionColour: var(--secondaryButtonColour);
  --actionColourHighlight: var(--primaryButtonColour);

  --dropdownBackgroundColour: var(--colour13);
  --dropdownTextColour: var(--linkColour);
  --dropdownItemBackgroundColour: var(--colour13);
  --dropdownItemTextColour: var(--textColour);
  --homepageCalendarBackgroundColour: var(--colour2a);

  --primaryColour: #0F6CBD;
  --secondaryColour: var(--colour12);
  --tertiaryColour: var(--colour13);

  --underNavBgColour: #23313D;
  --underNavTextColour: #ffffff;

  --dropdownHoverItemBackgroundColour: var(--colour12);
  --dropdownHoverItemTextColour: var(--colour3);

  --navbarTextHoverColour: var(--textColour);

  --attentionBoxBackgroundColour: #E1E3E5;
  --attentionBoxTextColour: #154B56;
  --widgetBorderColour: var(--colour10c);
  --navbarBgColour: #0f6cbd;
  --navbarTextColour: #ffffff;
  --navSearchBgColour: var(--colour3);
  --navSearchTextColour: var(--colour10);
  --footerBackgroundColour: #23313D;
  --footerTextColour: #fff;

  --buyNowColour: var(--primaryButtonColour);
  --timedSaleColour: #0e2839;
  --managedSaleColour: #1e3b4e;
  --underwriteSaleColour: #476375;

  --tableStripedBackgroundColour: rgba(80, 172, 255, 0.07);
  --tableStripedHoverBackgroundColour: rgba(80, 172, 255, 0.1);

  /*

  --underNavTextColour:
  --navbarTextHoverColour:
  --navbarTextColour:

  --feintColour:
  --highlightTextColour:

  --inputTextColour:
  --inputBackgroundColour:
  --textLabelColour:
  --actionColour:
  --actionColourHighlight:
  --inputBorderColour:

  --floatLabelColour:
  --floatLabelFocusColour:
  --floatLabelBackgroundColour:
  --floatInputFocusBorderColour:
  --floatInputFocusLabelColour:
  --defaultFont:

  --dangerColour:
  --successColour:
  --warningColour:

  --widgetBgColour:
  --smallCheckboxColour:
  --btnGroupBgColour:
  --tabFocusColour:
  --tableHeaderColour:
  --tableHeaderBackgroundColour:
  --tableHeaderDividerColour:
  --dialogCloseIconColour:
  --imageOutlineColour:
  --vrmBackgroundColour:
  --danger:
  --backgroundHighlight:
   */
}


