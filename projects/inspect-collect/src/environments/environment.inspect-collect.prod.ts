import {NgxLoggerLevel} from "ngx-logger";

export const environment = {
  production: true,
  serviceUrl: "https://api.tradesales.com",
  homepage: "https://www.tradesales.com",
  messageHubUrl: "https://wss.tradesales.com/messagehub",
  mobileHost: "app.tradesales.com",
  googleMapsAPIKey: "AIzaSyDRREM8vnh8GSDRXLsVt_Mi1tnhJCBYb6I",
  stripePublicKey: "pk_live_51Q6rvoFkvrxV59xa9zKf4biEsH2XgmFkVFVm01WrAxegBnVJoODVpW48sUArGVZrnTdtWn7vYR6vfHv1FrPM8xfw00BCHqxJcn", // live
  logging: {
    level: NgxLoggerLevel.DEBUG,
    disableConsoleLogging: false
  }
};

