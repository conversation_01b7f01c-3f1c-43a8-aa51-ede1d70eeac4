{"name": "tradesales", "version": "1.0.0", "scripts": {"ng": "ng", "start": "set NG_PERSISTENT_BUILD_CACHE=1&&ng serve -o --ssl --ssl-key certificates/localhost.key  --ssl-cert certificates/localhost.crt", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@capacitor-community/http": "^1.4.1", "@capacitor/android": "^4.0.0", "@capacitor/app": "^4.0.0", "@capacitor/camera": "^4.0.0", "@capacitor/core": "^4.8.2", "@capacitor/haptics": "^4.0.0", "@capacitor/ios": "^4.0.0", "@capacitor/keyboard": "^4.0.0", "@capacitor/splash-screen": "^4.0.0", "@capacitor/status-bar": "^4.0.0"}, "devDependencies": {"@capacitor/cli": "^4.8.2"}}