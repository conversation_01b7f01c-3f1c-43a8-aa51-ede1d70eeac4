import {CapacitorConfig} from '@capacitor/cli';
let config: CapacitorConfig;

const baseConfig: CapacitorConfig = {
  webDir: '../../dist/remarq',
  bundledWebRuntime: false,
  android: {
    allowMixedContent: true
  },
};

// These are called from package.json postbuild

switch (process.env.NODE_ENV) {

  case 'remarq-dev':
    config = {
      ...baseConfig,
      appId: 'uk.tradeins.app',
      appName: 'tradeins',
      server: {hostname: "app.tradesales.com", androidScheme: "https"},
    };
    break;
  case 'bigauction-dev':
    config = {
      ...baseConfig,
      appId: 'uk.co.bigauctions.dev.app',
      appName: 'bigauction',
      server: {hostname: "app.dev.bigauctions.co.uk", androidScheme: "https"},
    };
    break;
  case 'bigauction-prod':
    config = {
      ...baseConfig,
      appId: 'uk.co.bigauctions.app',
      appName: 'bigauction',
      server: {hostname: "app.bidup.co.uk", androidScheme: "https"},
    };
    break;
}

export default config;
