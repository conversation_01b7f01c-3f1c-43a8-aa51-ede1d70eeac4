const name = document.currentScript.dataset.param1 ?? "N/A";
const email = document.currentScript.dataset.param2 ?? "N/A";
const phone1 = document.currentScript.dataset.param3 ?? "N/A";

var $zoho=$zoho || {};$zoho.salesiq = $zoho.salesiq ||
  {widgetcode:"siqb8245a8644ea6d87bf27417fdf623446ae26d0cd42d2aafb98eae17928133654", values:{},ready:function(){
    $zoho.salesiq.visitor.name(name);
    $zoho.salesiq.visitor.email(email);
    $zoho.salesiq.visitor.contactnumber(phone1);
    $zoho.salesiq.visitor.info({ 'X': 'Y'});
    }};
var d=document;s=d.createElement("script");s.type="text/javascript";s.id="zsiqscript";s.defer=true;
s.src="https://salesiq.zoho.eu/widget";t=d.getElementsByTagName("script")[0];t.parentNode.insertBefore(s,t);d.write("<div id=‘zsiqwidget’></div>");
