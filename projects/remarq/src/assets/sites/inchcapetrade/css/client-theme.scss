#site-inchcapetrade {

  #bidbox {
    .table-label { color: var(--colour1); opacity: 0.7 }
    .timer-date { opacity: 0.7 }
    label { color: #ced4da !important; }
    .winning-status {
      background-color: var(--colour1);
      .with-you { color: #fff !important; }
    }
  }

  .attention-box { border-radius: 8px; }

  .btn-primary, .bg-primary, .btn.btn-primary {
    border: 0 !important;
  }

  .form-control {

    border: 1px solid #ced4da !important;

  }

  .email-count {
    color: #fff !important;
  }

  #customer-header-widget {
    background-color: #dee2e6 !important;
  }

  .mobile-bar {
    background-color: var(--colour1);
  }

  .classic-tabs .nav li:hover {
    color: var(--colour1) !important;
  }

  .modal-content .modal-header {
    background-color: var(--colour2) !important;
    color: #fff !important;

  }

  .btn-primary, .btn.btn-primary, .btn.btn-secondary, .btn-secondary { color: #fff !important }

  .side-nav .collapsible .card-body li a {
    background-color: rgba(0, 0, 0, 0.05) !important;
  }

  .switch.blue-white-switch label input[type=checkbox]:checked + .lever {

    background-color: var(--colour2);

  }

  .btn-secondary { color: #fff !important; }

  .fa.good {
    color: forestgreen;
  }

  .fa.bad {
    color: #c00;
  }

  .badger.bg-badge-color {
    background-color: var(--colour2);
  }

  .background-input {
    background-color: transparent;
    border-top: 1px solid #ced4da !important;
    border-bottom: 1px solid #ced4da !important;
  }

  .modal-body .form-control {
    background-color: #fff !important;
    border: 1px solid #ccc !important;
  }

  .modal-header {
    background-color: var(--colour2);
    color: #fff;
  }

  .btn-group .btn.active {
    background-color: var(--colour1) !important;
    color: #fff !important;
  }

  .btn-primary-outline {
    border: 1px solid var(--colour1) !important;
    color: var(--colour1) !important;
  }

  .btn-primary {
    background-color: var(--colour3) !important;
    color: #fff !important;
  }

  .search-box {
    background-color: #1A394D !important;
  }

  .search-submit {
    color: #fff;
  }

  .your-advert {
    background-color: rgba(0, 0, 0, 0.4);

  }

  .item-price-container {
    color: #fff;
  }

  .summary-inner {
    color: #fff;
  }

  .navbar {

    .form-control {
      border: 0 !important;
    }

    ::placeholder {
      opacity: 0.7;
    }

  }

  .won { color: #fff; }

  .navbar-brand {
    background-image: url('/assets/sites/inchcapetrade/images/sitelogo.svg');
    background-repeat: no-repeat;
    background-size: contain;
    height: 35px;
    width: 195px;
    margin-right: 0;
    padding-bottom: 0;
  }

  .undernav {
    background-color: #fff;
  }

  .undernav a.undernav-item, .undernav a.undernav-item:visited {

    font-weight: 500 !important;

  }

  .item-price-container { color: var(--colour1); }
  .user-name { color: #fff; opacity: 0.7 }

  .summary-inner { background-color: var(--colour2); }

  #dashboard-header-widget {
    background-color: var(--colour2) !important;
    color: #fff !important;

    .sub-text { color: #fff; opacity: 0.7; }
  }

  #homepage {

    .block-1, .block-2 {
      background-color: var(--colour1) !important;
      color: #fff !important;

      .sub-header {
        color: var(--colour1);
      }
    }

    color: var(--colour1) !important;


    .btn-sign-up {
      background-color: var(--colour2) !important;
    }

    .homepage-car {
      width: 100%;
      height: 100%;
      background-image: ("/assets/inchcapetrade/images/jaguar.png");
      background-repeat: no-repeat;
      background-size: contain;

    }
  }
}
