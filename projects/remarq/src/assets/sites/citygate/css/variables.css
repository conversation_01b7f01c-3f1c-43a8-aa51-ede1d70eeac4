:root {

  --font1: "Poppins";
  --font2: "Poppins";
  --font3: "Barlow";
  --defaultFont: var(--font1);
  --widgetBorderRadius: 8px;
  --imageOutlineColour: var(--widgetBorderColour);
  --footerHeight: 118px;
  /* Palette */

  --colour1: #002239;
  --colour2: #DD1921;
  --colour3: #ffffff;
  --colour4: #19c784;
  --colour5: #1E4F67;

  --navbarBgColour: var(--colour5);
  --secondaryButtonColour: var(--colour5);
  --primaryColour: var(--colour5);
  --softInputBorderColour: #ced4da;
  --secondaryColour: var(--colour5);
  --navbarTextColour: #fff;
  --textColour: var(--colour1);
  --bgColour: #f0f1f2;
  --dropdownBackgroundColour: #fff;
  --dropdownItemTextColour: var(--colour1);
  --dropdownHoverItemBackgroundColour: var(--colour1);
  --dropdownHoverItemTextColour: #fff;
  --sideNavBackgroundColour: #fff;
  --footerBackgroundColour: #222;
  --footerTextColour: #fff;
  --switchColour: var(--colour4);
  --linkColour: var(--colour1);
  --inputBorderColour: #ddd;
  --floatInputFocusBorderColour: #bbb;
  --actionColour: var(--colour2);
  --navbarTextColour: #fff;
  --attentionBoxBackgroundColour: #EEF4F9;
  --sideNavHoverTextColour: var(--colour4);
  --navSearchBgColour: #356176;
  --underNavBgColour: #fff;

  --buyNowColour: #FFB154;
  --timedSaleColour: #51ADDF;
  --managedSaleColour: #606DE4;
  --sideNavTextColour: var(--colour1);
  --primaryButtonColour: var(--colour4);

}

.device {
  --headerHeight: 96px;
  --headerPaddingTop: 30px;
}



