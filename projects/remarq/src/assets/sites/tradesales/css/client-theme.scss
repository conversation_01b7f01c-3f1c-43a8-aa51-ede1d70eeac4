#site-tradesales {

  #navbar {
    background-color: #fff !important;

    &.devmode {
      background-color: purple !important;
    }
  }

  .navbar-brand {

    background-image: url('/assets/sites/tradesales/images/sitelogo.svg');
    background-repeat: no-repeat;
    background-size: contain;
    height: 36px;
    width: 160px;
  }

  .ngx-pagination .current {
    background-color: #222 !important;
  }

  @media (max-width: 400px) {
    .navbar-brand {
      width: 120px;
    }

  }

  .homepage-car-png {
    background-image: url(/assets/sites/tradesales/images/homepage-car.png);
    width: 100%;
    aspect-ratio: 1.7;
    background-position: center center;
    background-size: contain;
    background-repeat: no-repeat;
  }

  .watchlist-container {

    .watchlist-state {
      &.in-watchlist {
        color: var(--primaryButtonColour);
      }

      &:not(.in-watchlist) {
        color: var(--floatLabelColour);
      }
    }
  }
}
