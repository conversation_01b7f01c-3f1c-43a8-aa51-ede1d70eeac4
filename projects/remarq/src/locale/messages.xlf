<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="1.2">
  <file source-language="en-GB" datatype="plaintext" original="ng2.template">
    <body>
      <trans-unit id="mileage" datatype="html">
        <source>Mileage</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/components/main/advert/create-advert-vehicle/create-advert-vehicle.component.html
          </context>
          <context context-type="linenumber">31</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/components/main/advert/advert-search-summary/advert-search-summary.component.html
          </context>
          <context context-type="linenumber">61</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/main/advert/advert-view/advert-view.component.html
          </context>
          <context context-type="linenumber">85</context>
        </context-group>
      </trans-unit>
      <trans-unit id="mileage_lower" datatype="html">
        <source>mileage</source>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/components/main/advert/create-advert-vehicle/create-advert-vehicle.component.html
          </context>
          <context context-type="linenumber">33</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/components/main/advert/create-advert-vehicle/create-advert-vehicle.component.html
          </context>
          <context context-type="linenumber">34</context>
        </context-group>
        <context-group purpose="location">
          <context context-type="sourcefile">
            src/app/components/main/advert/create-advert-vehicle/create-advert-vehicle.component.html
          </context>
          <context context-type="linenumber">79</context>
        </context-group>
      </trans-unit>
    </body>
  </file>
</xliff>
