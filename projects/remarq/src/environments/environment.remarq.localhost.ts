import {NgxLoggerLevel} from "ngx-logger";

export const environment = {
  production: false,
  mobileHost: 'app.tradesales.com',
  homepage: "https://localhost:44322",
  serviceUrl: "https://localhost:5001",
  messageHubUrl: "https://localhost:5001/messagehub",
  googleMapsAPIKey: "AIzaSyDRREM8vnh8GSDRXLsVt_Mi1tnhJCBYb6I",
  stripePublicKey: "pk_test_51Q6sIHCLElIQ4Dc0mCNEG1kItFEYRbze0vM2PhE1bZYRNRePrxYD8aROD37IDKiJEeU8gmHN2A882PKEpyL6OjxU00CJJq5Tdp", // sandbox
  uInspectUrl: "https://localhost:44322",
  engineiusKey: "731b6b87f5c245f987c264e48feadc21",
  logging: {
    level: NgxLoggerLevel.DEBUG,
    disableConsoleLogging: false
  }
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
