import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnChanges, OnInit, ChangeDetectorRef } from '@angular/core';
import {
  EastbourneImportPreview,
  ImportWorkflowState,
  EastbourneVehiclePreview,
  EastbourneImportApiResponse,
  ImportHistoryEntry,
  ImportProgressStatus,
  CreateImportRecordDTO, EastbourneVehicleData
} from 'projects/common/interfaces';
import { EastbourneImportService } from '../services/eastbourne-import.service';
import { ImportService } from '../services/import.service';

@Component({
  selector: 'app-eastbourne-import-upload',
  templateUrl: './eastbourne-import-upload.component.html',
  styleUrls: ['./eastbourne-import-upload.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EastbourneImportUploadComponent implements OnChanges, OnInit {
  @Input() selectedFile: File | null = null;
  @Input() previewData: CreateImportRecordDTO | null = null;
  @Input() isLoading: boolean = false;
  @Input() currentState: ImportWorkflowState = ImportWorkflowState.Upload;

  @Output() fileSelected = new EventEmitter<File>();
  @Output() previewRequested = new EventEmitter<void>();
  @Output() importStarted = new EventEmitter<void>();
  @Output() backToUpload = new EventEmitter<void>();
  @Output() existingImportSelected = new EventEmitter<CreateImportRecordDTO>();

  ImportWorkflowState = ImportWorkflowState;
  dragOver = false;
  showJsonView = false;
  showImportHistory = false;

  // Processed data from parsing jsonBlob
  parsedVehicles: EastbourneVehicleData[] = [];
  processedPreviewData: EastbourneImportPreview | null = null;

  // Import history data
  importHistory: ImportHistoryEntry[] = [];
  isLoadingHistory = false;
  selectedImportDetails: ImportProgressStatus | null = null;
  selectedImportData: CreateImportRecordDTO | null = null;

  constructor(
    private eastbourneImportService: EastbourneImportService,
    private importService: ImportService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Don't load history automatically - only when user clicks the button
  }

  ngOnChanges(): void {
    if (this.previewData) {
      this.processPreviewData();
    }
  }

  toggleImportHistory(): void {
    this.showImportHistory = !this.showImportHistory;

    // Load history when showing for the first time
    if (this.showImportHistory && this.importHistory.length === 0) {
      this.loadImportHistory();
    }
  }

  private async loadImportHistory(): Promise<void> {
    console.log('Starting loadImportHistory, setting loading to true');
    this.isLoadingHistory = true;
    this.cdr.detectChanges(); // Force change detection

    try {
      console.log('Making API call...');
      const response = await this.eastbourneImportService.getImportHistory(2, 10, 0);
      console.log('API response received:', response);
      this.importHistory = response.imports;
      console.log('Import history set:', this.importHistory);
      console.log('Setting loading to false');
      this.isLoadingHistory = false;
      this.cdr.detectChanges(); // Force change detection
    } catch (error) {
      console.error('Error in loadImportHistory:', error);
      this.importHistory = [];
      this.isLoadingHistory = false;
      this.cdr.detectChanges(); // Force change detection
    }
  }

  onViewImportDetails(importId: string): void {
    this.importService.getImportProgress(importId).subscribe({
      next: (details) => {
        this.selectedImportDetails = details;
      },
      error: (error) => {
        console.error('Failed to load import details:', error);
      }
    });
  }

  onCloseImportDetails(): void {
    this.selectedImportDetails = null;
  }

  private processPreviewData(): void {
    try {
      if (this.previewData?.jsonBlob) {
        // Parse the JSON string from the blob
        this.parsedVehicles = JSON.parse(this.previewData.jsonBlob);

        // Create processed preview data for table display
        this.processedPreviewData = {
          vehicleCount: this.parsedVehicles.length,
          sampleVehicles: this.parsedVehicles.slice(0, 5).map((vehicle, index) => ({
            make: vehicle.basicInfo?.make || 'N/A',
            model: vehicle.basicInfo?.model || 'N/A',
            year: this.extractYear(vehicle),
            registration: vehicle.basicInfo?.registration || 'N/A',
            mileage: this.extractMileage(vehicle),
            fuelType: this.extractFuelType(vehicle),
            reserve: this.extractPrice(vehicle),
            rowIndex: index + 1
          })),
          errors: []
        };
      }
    } catch (error) {
      console.error('Error processing preview data:', error);
      this.processedPreviewData = {
        vehicleCount: 0,
        sampleVehicles: [],
        errors: ['Failed to parse vehicle data']
      };
    }
  }

  private extractYear(vehicle: EastbourneVehicleData): number {
    const yearDetail = vehicle.details?.find(d => d.fieldName.toLowerCase().includes('registered'));
    const auctionDate = vehicle.basicInfo?.auctionDateTime;

    if (yearDetail?.fieldValue) {
      const year = parseInt(yearDetail.fieldValue);
      if (!isNaN(year)) return year;
    }

    if (auctionDate) {
      const year = new Date(auctionDate).getFullYear();
      if (year > 1900) return year;
    }

    return 0;
  }

  private extractMileage(vehicle: EastbourneVehicleData): number {
    const mileageDetail = vehicle.details?.find(d =>
      d.fieldName.toLowerCase().includes('mileage') ||
      d.fieldName.toLowerCase().includes('odometer')
    );

    if (mileageDetail?.fieldValue) {
      const mileage = parseInt(mileageDetail.fieldValue.replace(/[^\d]/g, ''));
      if (!isNaN(mileage)) return mileage;
    }

    return 0;
  }

  private extractFuelType(vehicle: EastbourneVehicleData): string {
    const fuelDetail = vehicle.details?.find(d =>
      d.fieldName.toLowerCase().includes('fuel') ||
      d.fieldName.toLowerCase().includes('engine')
    );
    return fuelDetail?.fieldValue || 'Unknown';
  }

  private extractPrice(vehicle: EastbourneVehicleData): number {
    const priceDetail = vehicle.additionalDetails?.find(d =>
      d.fieldName.toLowerCase().includes('price') ||
      d.fieldName.toLowerCase().includes('reserve') ||
      d.fieldName.toLowerCase().includes('estimate')
    );

    if (priceDetail?.fieldValue) {
      const price = parseFloat(priceDetail.fieldValue.replace(/[^\d.]/g, ''));
      if (!isNaN(price)) return price;
    }

    return 0;
  }

  onFileInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input?.files?.length) {
      this.handleFileSelection(input.files[0]);
    }
  }

  onFileDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.dragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.dragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.dragOver = false;
  }

  private handleFileSelection(file: File): void {
    if (this.isValidFile(file)) {
      this.fileSelected.emit(file);
    }
  }

  private isValidFile(file: File): boolean {
    const validTypes = ['text/csv', 'application/vnd.ms-excel', 'text/plain'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!validTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.csv')) {
      alert('Please select a valid CSV file.');
      return false;
    }

    if (file.size > maxSize) {
      alert('File size must be less than 10MB.');
      return false;
    }

    return true;
  }

  onPreviewClick(): void {
    this.previewRequested.emit();
  }

  onImportClick(): void {
    this.importStarted.emit();
  }

  onBackClick(): void {
    this.backToUpload.emit();
  }

  get fileSize(): string {
    if (!this.selectedFile) return '';
    const bytes = this.selectedFile.size;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  get showFileInfo(): boolean {
    return this.selectedFile !== null &&
           (this.currentState === ImportWorkflowState.Upload ||
            this.currentState === ImportWorkflowState.PreviewError);
  }

  get showPreviewData(): boolean {
    return this.previewData !== null;
  }

  get hasPreviewErrors(): boolean {
    return this.processedPreviewData !== null &&
           this.processedPreviewData.errors &&
           this.processedPreviewData.errors.length > 0;
  }

  get canPreview(): boolean {
    return this.selectedFile !== null &&
           (this.currentState === ImportWorkflowState.Upload ||
            this.currentState === ImportWorkflowState.PreviewError) &&
           !this.isLoading;
  }

  get previewDataAsJson(): string {
    if (!this.previewData) {
      return '{\n  "message": "No preview data available"\n}';
    }
    return JSON.stringify(this.previewData, null, 2);
  }

  get cleanedParsedVehicles(): any {
    if (!this.parsedVehicles || this.parsedVehicles.length === 0) {
      return null;
    }

    // Clean up the JSON data by removing \r\n characters
    const jsonString = JSON.stringify(this.parsedVehicles, null, 2);
    const cleanString = jsonString.replace(/\\r\\n/g, '\\n').replace(/\\"/g, '"');
    return JSON.parse(cleanString);
  }

  // New methods for existing import functionality
  onUseExistingImport(importId: string): void {
    const importEntry = this.importHistory.find(imp => imp.importId === importId);
    if (importEntry) {
      // We need to fetch the actual import data first
      this.onViewImportData(importId);
    }
  }

  onViewImportData(importId: string): void {
    this.importService.getImportData(importId).subscribe({
      next: (importData: CreateImportRecordDTO) => {
        this.selectedImportData = importData;
      },
      error: (error) => {
        console.error('Failed to load import data:', error);
        // Show error to user
        alert('Failed to load import data: ' + (error?.error?.message || 'Unknown error'));
      }
    });
  }

  onCloseImportData(): void {
    this.selectedImportData = null;
  }

  onUseThisImportData(): void {
    if (this.selectedImportData) {
      this.existingImportSelected.emit(this.selectedImportData);
      this.onCloseImportData();
    }
  }

  getParsedJson(jsonBlob: string): any {
    try {
      return JSON.parse(jsonBlob);
    } catch {
      return { error: 'Invalid JSON data' };
    }
  }

  getVehicleCountFromJson(jsonBlob: string): number {
    try {
      const data = JSON.parse(jsonBlob);
      return Array.isArray(data) ? data.length : 0;
    } catch {
      return 0;
    }
  }

  copyJsonToClipboard(jsonBlob: string): void {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(jsonBlob).then(() => {
        // Could add a toast notification here
        console.log('JSON copied to clipboard');
      });
    }
  }
}
