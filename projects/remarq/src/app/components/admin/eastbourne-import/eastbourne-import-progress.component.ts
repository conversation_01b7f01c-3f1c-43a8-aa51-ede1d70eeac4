import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import {
  ImportProgressStatus,
  ImportLogEntry,
  ImportWorkflowState
} from 'projects/common/interfaces';

@Component({
  selector: 'app-eastbourne-import-progress',
  templateUrl: './eastbourne-import-progress.component.html',
  styleUrls: ['./eastbourne-import-progress.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EastbourneImportProgressComponent {
  @Input() importId: string = '';
  @Input() progressStatus: ImportProgressStatus | null = null;
  @Input() currentState: ImportWorkflowState = ImportWorkflowState.Processing;
  @Output() retryFailed = new EventEmitter<void>();

  ImportWorkflowState = ImportWorkflowState;

  get progressPercentage(): number {
    if (!this.progressStatus || this.progressStatus.totalLogs === 0) {
      return 0;
    }
    return Math.round((this.progressStatus.successfulLogs / this.progressStatus.totalLogs) * 100);
  }

  get isProcessing(): boolean {
    return this.currentState === ImportWorkflowState.Processing &&
           this.progressStatus?.importState === 'Processing';
  }

  get isCompleted(): boolean {
    return this.progressStatus?.importState === 'Processed' && !this.progressStatus.hasErrors;
  }

  get isFailed(): boolean {
    return this.progressStatus?.importState === 'Processed' && this.progressStatus.hasErrors;
  }

  get hasFailedImports(): boolean {
    return this.progressStatus?.errorLogs > 0;
  }

  get statusIcon(): string {
    if (this.isProcessing) {
      return 'fas fa-spinner fa-spin';
    } else if (this.isCompleted) {
      return 'fas fa-check-circle text-success';
    } else if (this.isFailed) {
      return 'fas fa-exclamation-circle text-danger';
    } else {
      return 'fas fa-clock';
    }
  }

  get statusText(): string {
    switch (this.currentState) {
      case ImportWorkflowState.ImportingSelected:
        return 'Starting import of selected vehicles...';
      case ImportWorkflowState.Processing:
        return 'Processing vehicles...';
      case ImportWorkflowState.Completed:
        return 'Import completed successfully!';
      case ImportWorkflowState.Failed:
        return 'Import completed with errors.';
      default:
        return 'Processing...';
    }
  }

  get successRate(): number {
    if (!this.progressStatus || this.progressStatus.totalLogs === 0) {
      return 0;
    }
    return Math.round((this.progressStatus.successfulLogs / this.progressStatus.totalLogs) * 100);
  }

  get allLogs(): ImportLogEntry[] {
    if (!this.progressStatus?.logs) {
      return [];
    }
    return this.progressStatus.logs;
  }

  get successfulLogs(): ImportLogEntry[] {
    return this.allLogs.filter(log => log.imported);
  }

  get failedLogs(): ImportLogEntry[] {
    return this.allLogs.filter(log => !log.imported);
  }

  get recentLogs(): ImportLogEntry[] {
    // Return the last 10 logs for display
    return this.allLogs.slice(-10);
  }

  get elapsedTime(): string {
    // For now, return a placeholder. This would need start time tracking
    return 'N/A';
  }

  get estimatedTimeRemaining(): string {
    // For now, return a placeholder. This would need progress rate calculation
    return 'Calculating...';
  }

  getLogIcon(log: ImportLogEntry): string {
    if (log.imported) {
      return 'fas fa-check-circle text-success';
    } else {
      return 'fas fa-exclamation-circle text-danger';
    }
  }

  getLogClass(log: ImportLogEntry): string {
    if (log.imported) {
      return 'list-group-item-success';
    } else {
      return 'list-group-item-danger';
    }
  }

  trackByLogId(index: number, log: ImportLogEntry): number {
    return log.id;
  }

  onRetryFailed(): void {
    this.retryFailed.emit();
  }
}
