import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import {
  ImportProgressStatus,
  ImportLogEntry,
  ImportWorkflowState
} from 'projects/common/interfaces';

@Component({
  selector: 'app-eastbourne-import-results',
  templateUrl: './eastbourne-import-results.component.html',
  styleUrls: ['./eastbourne-import-results.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class EastbourneImportResultsComponent {
  @Input() progressStatus: ImportProgressStatus | null = null;
  @Input() currentState: ImportWorkflowState = ImportWorkflowState.Completed;
  @Input() importId: string = '';

  @Output() startNewImport = new EventEmitter<void>();

  ImportWorkflowState = ImportWorkflowState;

  get isCompleted(): boolean {
    return this.currentState === ImportWorkflowState.Completed;
  }

  get isFailed(): boolean {
    return this.currentState === ImportWorkflowState.Failed ||
           this.currentState === ImportWorkflowState.ImportError;
  }

  get successRate(): number {
    if (!this.progressStatus || this.progressStatus.totalLogs === 0) {
      return 0;
    }
    return Math.round((this.progressStatus.successfulLogs / this.progressStatus.totalLogs) * 100);
  }

  get successfulVehicles(): ImportLogEntry[] {
    if (!this.progressStatus?.logs) {
      return [];
    }
    return this.progressStatus.logs.filter(log => log.imported);
  }

  get failedVehicles(): ImportLogEntry[] {
    if (!this.progressStatus?.logs) {
      return [];
    }
    return this.progressStatus.logs.filter(log => !log.imported);
  }

  get statusIcon(): string {
    if (this.isCompleted) {
      return 'fas fa-check-circle text-success';
    } else if (this.isFailed) {
      return 'fas fa-times-circle text-danger';
    } else {
      return 'fas fa-exclamation-circle text-warning';
    }
  }

  get statusTitle(): string {
    if (this.isCompleted) {
      return 'Import Completed Successfully';
    } else if (this.currentState === ImportWorkflowState.Failed) {
      return 'Import Failed';
    } else if (this.currentState === ImportWorkflowState.ImportError) {
      return 'Import Error';
    } else {
      return 'Import Status Unknown';
    }
  }

  get statusMessage(): string {
    if (this.isCompleted) {
      if (this.progressStatus?.successfulLogs === this.progressStatus?.totalLogs) {
        return 'All vehicles were processed successfully!';
      } else {
        return `Import completed with ${this.progressStatus?.successfulLogs} successful and ${this.progressStatus?.errorLogs} failed vehicles.`;
      }
    } else if (this.isFailed) {
      return 'The import process encountered errors and could not complete successfully.';
    } else {
      return 'The import process ended in an unknown state.';
    }
  }

  get processingTime(): string {
    // For now, return a placeholder. This would need start/end time tracking
    return 'N/A';
  }

  onStartNewImport(): void {
    this.startNewImport.emit();
  }

  trackByLogId(index: number, log: ImportLogEntry): number {
    return log.id;
  }

  downloadResults(): void {
    if (!this.progressStatus?.logs) {
      return;
    }

    const csvContent = this.generateCSVReport();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `eastbourne_import_${this.importId}_results.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  private generateCSVReport(): string {
    if (!this.progressStatus?.logs) {
      return '';
    }

    const headers = ['Registration', 'Status', 'Error Message'];
    const csvRows = [headers.join(',')];

    this.progressStatus.logs.forEach(log => {
      const row = [
        `"${log.identifier}"`,
        `"${log.imported ? 'Success' : 'Failed'}"`,
        `"${log.errorText || ''}"`
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }
}
