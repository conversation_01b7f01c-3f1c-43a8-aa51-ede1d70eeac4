.vehicle-selection-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px 0;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.search-container {
  flex: 1;
  margin-right: 20px;
}

.search-input {
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.select-all-container {
  display: flex;
  align-items: center;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  user-select: none;

  input {
    margin-right: 8px;
    transform: scale(1.2);
  }
}

.vehicle-checkbox {
  width: 18px !important;
  height: 18px !important;
  margin: 0 !important;
  cursor: pointer !important;
  transform: scale(1.5) !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  position: relative !important;
  z-index: 10 !important;
}

.col-select {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 60px;
  padding: 8px;
}

.select-all-checkbox {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  cursor: pointer;
  transform: scale(1.2);
}

.vehicle-grid {
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
}

.vehicle-header {
  display: grid;
  grid-template-columns: 80px 120px 1fr 80px 120px 120px 100px 140px;
  gap: 10px;
  background: #f8f9fa;
  padding: 12px;
  font-weight: 600;
  font-size: 13px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #eee;
}

.vehicle-row {
  display: grid;
  grid-template-columns: 80px 120px 1fr 80px 120px 120px 100px 140px;
  gap: 10px;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

.vrm-badge {
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.vehicle-info {
  .make-model {
    font-weight: 500;
    color: #333;
    display: block;
    margin-bottom: 2px;
  }

  .fuel-type {
    font-size: 12px;
    color: #666;
    text-transform: capitalize;
  }
}

.price {
  font-weight: 600;
  color: #28a745;
}

.no-data {
  color: #999;
  font-style: italic;
  font-size: 12px;
}

.enhanced-icons {
  display: flex;
  gap: 4px;

  .enhanced-icon {
    font-size: 16px;
    cursor: help;
  }

  .no-enhanced {
    color: #ccc;
    font-size: 18px;
  }
}

.col-status {
  .badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;

    i {
      margin-right: 4px;
      font-size: 10px;
    }

    &.bg-success {
      background-color: #28a745 !important;
    }

    &.bg-danger {
      background-color: #dc3545 !important;
      cursor: help;
    }

    &.bg-warning {
      background-color: #ffc107 !important;
      color: #212529 !important;
    }

    &.bg-secondary {
      background-color: #6c757d !important;
    }
  }
}

.selection-summary {
  margin-top: 15px;
  padding: 12px;
  background: #e8f4fd;
  border-radius: 4px;
  text-align: center;

  .summary-text {
    font-size: 14px;
    color: #0056b3;
  }
}

// Responsive design
@media (max-width: 1200px) {
  .vehicle-header,
  .vehicle-row {
    grid-template-columns: 60px 100px 1fr 70px 100px 100px 90px 120px;
    gap: 8px;
    font-size: 13px;
  }
}

@media (max-width: 900px) {
  .selection-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .vehicle-header,
  .vehicle-row {
    grid-template-columns: 60px 90px 1fr 60px 90px 90px 80px 100px;
    gap: 6px;
    font-size: 12px;
  }

  .search-input {
    max-width: none;
  }
}
