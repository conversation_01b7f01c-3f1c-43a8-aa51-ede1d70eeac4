import { Component, Input, Output, EventEmitter } from '@angular/core';
import { VehiclePreviewData } from 'projects/common/interfaces';

@Component({
  selector: 'app-vehicle-selection-grid',
  templateUrl: './vehicle-selection-grid.component.html',
  styleUrls: ['./vehicle-selection-grid.component.scss']
})
export class VehicleSelectionGridComponent {
  @Input() vehicles: VehiclePreviewData[] = [];
  @Input() selectedIds: number[] = [];
  @Output() selectionChange = new EventEmitter<number[]>();

  searchTerm: string = '';
  selectAll: boolean = false;

  get filteredVehicles(): VehiclePreviewData[] {
    if (!this.searchTerm) {
      return this.vehicles;
    }
    return this.vehicles.filter(vehicle =>
      vehicle.registration.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      vehicle.make.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get isSelectAllChecked(): boolean {
    const filteredIds = this.filteredVehicles.map(vehicle => vehicle.id).filter(id => id !== undefined) as number[];
    return filteredIds.length > 0 && filteredIds.every(id => this.selectedIds.includes(id));
  }

  get isSelectAllIndeterminate(): boolean {
    const filteredIds = this.filteredVehicles.map(vehicle => vehicle.id).filter(id => id !== undefined) as number[];
    const selectedCount = filteredIds.filter(id => this.selectedIds.includes(id)).length;
    return selectedCount > 0 && selectedCount < filteredIds.length;
  }

  onSelectAllChange(): void {
    const filteredIds = this.filteredVehicles.map(vehicle => vehicle.id).filter(id => id !== undefined) as number[];

    if (this.isSelectAllChecked) {
      // Unselect all filtered vehicles
      const newSelectedIds = this.selectedIds.filter(id => !filteredIds.includes(id));
      this.selectionChange.emit(newSelectedIds);
    } else {
      // Select all filtered vehicles
      const newSelectedIds = [...new Set([...this.selectedIds, ...filteredIds])];
      this.selectionChange.emit(newSelectedIds);
    }
  }

  onVehicleSelectionToggle(vehicleId: number): void {
    console.log('Vehicle selection toggle:', vehicleId, 'Currently selected:', this.isVehicleSelected(vehicleId));
    let newSelectedIds: number[];

    if (this.isVehicleSelected(vehicleId)) {
      // Unselect
      newSelectedIds = this.selectedIds.filter(id => id !== vehicleId);
    } else {
      // Select
      newSelectedIds = [...this.selectedIds, vehicleId];
    }

    console.log('New selected IDs:', newSelectedIds);
    this.selectionChange.emit(newSelectedIds);
  }

  isVehicleSelected(vehicleId: number): boolean {
    return this.selectedIds.includes(vehicleId);
  }

  getEnhancedDataIcons(vehicle: VehiclePreviewData): string[] {
    const icons: string[] = [];
    if (vehicle.hasImages) icons.push('🖼️');
    if (vehicle.hasDamageItems) icons.push('📋');
    if (vehicle.hasTyreInfo) icons.push('🔧');
    // console.log('Enhanced data for vehicle', vehicle.registration, ':', {
    //   hasImages: vehicle.hasImages,
    //   hasDamageItems: vehicle.hasDamageItems,
    //   hasTyreInfo: vehicle.hasTyreInfo,
    //   icons
    // });
    return icons;
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(price);
  }

  formatMileage(mileage: number): string {
    return new Intl.NumberFormat('en-GB').format(mileage) + ' miles';
  }

  getIconTitle(icon: string): string {
    switch (icon) {
      case '🖼️': return 'Gallery Images available';
      case '📋': return 'Damage Items available';
      case '🔧': return 'Tyre Information available';
      default: return '';
    }
  }
}
