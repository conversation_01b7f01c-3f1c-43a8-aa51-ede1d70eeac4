<div class="vehicle-selection-container">
  <div class="selection-header">
    <div class="search-container">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Search by VRM, Make, or Model..."
        class="search-input">
    </div>

    <div class="select-all-container">
      <label class="checkbox-container">
        <input
          type="checkbox"
          [checked]="isSelectAllChecked"
          [indeterminate]="isSelectAllIndeterminate"
          (change)="onSelectAllChange()"
          class="select-all-checkbox">
        Select All ({{filteredVehicles.length}} vehicles)
      </label>
    </div>
  </div>

  <div class="vehicle-grid">
    <div class="vehicle-header">
      <div class="col-select">Select</div>
      <div class="col-vrm">VRM</div>
      <div class="col-vehicle">Vehicle</div>
      <div class="col-year">Year</div>
      <div class="col-mileage">Mileage</div>
      <div class="col-price">Reserve Price</div>
      <div class="col-status">Status</div>
      <div class="col-enhanced">Enhanced Data</div>
    </div>

    <div class="vehicle-row" *ngFor="let vehicle of filteredVehicles; let i = index">
      <div class="col-select">
        <input
          type="checkbox"
          [checked]="isVehicleSelected(vehicle.id)"
          (click)="onVehicleSelectionToggle(vehicle.id)"
          class="vehicle-checkbox">
      </div>

      <div class="col-vrm">
        <span class="vrm-badge">{{vehicle.registration}}</span>
      </div>

      <div class="col-vehicle">
        <div class="vehicle-info">
          <span class="make-model">{{vehicle.make}} {{vehicle.model}}</span>
          <span class="fuel-type">{{vehicle.fuelType}}</span>
        </div>
      </div>

      <div class="col-year">{{vehicle.year}}</div>

      <div class="col-mileage">
        <span *ngIf="vehicle.mileage > 0">{{formatMileage(vehicle.mileage)}}</span>
        <span *ngIf="vehicle.mileage === 0" class="no-data">Not specified</span>
      </div>

      <div class="col-price">
        <span *ngIf="vehicle.reserve > 0" class="price">{{formatPrice(vehicle.reserve)}}</span>
        <span *ngIf="vehicle.reserve === 0" class="no-data">Not specified</span>
      </div>

      <div class="col-status">
        <span *ngIf="vehicle.imported === true" class="badge bg-success">
          <i class="fas fa-check"></i> Imported
        </span>
        <span *ngIf="vehicle.imported === false && vehicle.errorText"
              class="badge bg-danger"
              [title]="vehicle.errorText">
          <i class="fas fa-exclamation-triangle"></i> Failed
        </span>
        <span *ngIf="vehicle.imported === false && !vehicle.errorText" class="badge bg-warning">
          <i class="fas fa-clock"></i> Pending
        </span>
        <span *ngIf="vehicle.imported === undefined" class="badge bg-secondary">
          <i class="fas fa-question"></i> New
        </span>
      </div>

      <div class="col-enhanced">
        <span class="enhanced-icons">
          <span
            *ngFor="let icon of getEnhancedDataIcons(vehicle)"
            class="enhanced-icon"
            [title]="getIconTitle(icon)">
            {{icon}}
          </span>
          <span *ngIf="getEnhancedDataIcons(vehicle).length === 0" class="no-enhanced">—</span>
        </span>
      </div>
    </div>
  </div>

  <div class="selection-summary" *ngIf="selectedIds.length > 0">
    <div class="summary-text">
      <strong>{{selectedIds.length}}</strong> of <strong>{{vehicles.length}}</strong> vehicles selected
    </div>
  </div>
</div>
