import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { EastbourneImportService } from '../services/eastbourne-import.service';
import { ImportService } from '../services/import.service';
import {
  EastbourneImportPreview,
  ImportProgressStatus,
  ImportWorkflowState,
  EastbourneImportStartResponse,
  CreateImportRecordDTO,
  VehiclePreviewData,
  SelectiveImportResponse
} from 'projects/common/interfaces';

@Component({
  selector: 'app-eastbourne-import-container',
  templateUrl: './eastbourne-import-container.component.html',
  styleUrls: ['./eastbourne-import-container.component.scss']
})
export class EastbourneImportContainerComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  currentState: ImportWorkflowState = ImportWorkflowState.Upload;
  ImportWorkflowState = ImportWorkflowState;

  selectedFile: File | null = null;
  csvContent: string = '';
  previewData: CreateImportRecordDTO | null = null;
  vehicleData: VehiclePreviewData[] = [];
  selectedVehicleIds: number[] = [];
  importId: string = '';
  progressStatus: ImportProgressStatus | null = null;

  errorMessage: string = '';
  isLoading = false;

  constructor(
    private eastbourneImportService: EastbourneImportService,
    private importService: ImportService
  ) {}

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onFileSelected(file: File): void {
    this.selectedFile = file;
    this.errorMessage = '';
    this.previewData = null;
    this.vehicleData = [];
    this.selectedVehicleIds = [];
    this.currentState = ImportWorkflowState.Upload;
  }

  onPreviewRequested(): void {
    if (!this.selectedFile) {
      this.errorMessage = 'Please select a file first.';
      return;
    }

    this.currentState = ImportWorkflowState.Previewing;
    this.errorMessage = '';
    this.isLoading = true;

    this.eastbourneImportService.previewCsvImport(this.selectedFile)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (preview: CreateImportRecordDTO) => {
          this.previewData = preview;
          this.importId = preview.importId;

          // Fetch ImportLog data to get correct IDs for vehicle selection
          this.eastbourneImportService.getImportLogs(preview.importId)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: (importLogs) => {
                this.vehicleData = this.eastbourneImportService.parseVehicleData(preview.jsonBlob, importLogs);
                this.isLoading = false;

                if (this.vehicleData.length === 0) {
                  this.currentState = ImportWorkflowState.PreviewError;
                  this.errorMessage = 'No vehicles found in the CSV file';
                } else {
                  this.currentState = ImportWorkflowState.VehicleSelection;
                }
              },
              error: (error) => {
                console.warn('Failed to load ImportLog data, using fallback IDs:', error);
                // Fallback to parsing without ImportLog data
                this.vehicleData = this.eastbourneImportService.parseVehicleData(preview.jsonBlob);
                this.isLoading = false;
                this.currentState = ImportWorkflowState.VehicleSelection;
              }
            });
        },
        error: (error) => {
          this.isLoading = false;
          this.currentState = ImportWorkflowState.PreviewError;
          this.errorMessage = error?.error?.message || 'Failed to preview CSV file';
        }
      });
  }

  onVehicleSelectionChanged(selectedIds: number[]): void {
    this.selectedVehicleIds = selectedIds;
  }

  onImportSelected(): void {
    if (this.selectedVehicleIds.length === 0) {
      this.errorMessage = 'Please select at least one vehicle to import.';
      return;
    }

    this.currentState = ImportWorkflowState.ImportingSelected;
    this.errorMessage = '';
    this.isLoading = true;

    this.eastbourneImportService.importSelectedVehicles(this.importId, this.selectedVehicleIds)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: SelectiveImportResponse) => {
          this.isLoading = false;
          this.currentState = ImportWorkflowState.Processing;
          this.startProgressTracking();
        },
        error: (error) => {
          this.isLoading = false;
          this.currentState = ImportWorkflowState.ImportError;
          this.errorMessage = error?.error?.message || 'Failed to start import';
        }
      });
  }

  onImportAll(): void {
    const allIds = this.vehicleData.map(vehicle => vehicle.id).filter(id => id !== undefined) as number[];
    this.selectedVehicleIds = allIds;

    this.onImportSelected();
  }

  private startProgressTracking(): void {
    if (!this.importId) return;

    this.importService.pollImportProgress(this.importId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (progress: ImportProgressStatus) => {
          this.progressStatus = progress;

          if (progress.importState === 'Processed') {
            if (progress.hasErrors) {
              this.currentState = ImportWorkflowState.Failed;
            } else {
              this.currentState = ImportWorkflowState.Completed;
            }
          }
        },
        error: (error) => {
          this.currentState = ImportWorkflowState.Failed;
          this.errorMessage = error?.error?.message || 'Failed to track import progress';
        }
      });
  }

  onRetryFailedImports(): void {
    if (!this.progressStatus?.logs) return;

    const failedLogIds = this.progressStatus.logs
      .filter(log => !log.imported)
      .map(log => log.id);

    if (failedLogIds.length === 0) {
      this.errorMessage = 'No failed imports to retry.';
      return;
    }

    this.selectedVehicleIds = failedLogIds;
    this.onImportSelected();
  }

  onExistingImportSelected(importData: CreateImportRecordDTO): void {
    // Set the preview data
    this.previewData = importData;
    this.importId = importData.importId;
    this.isLoading = true;

    // Fetch ImportLog data to get correct IDs for vehicle selection
    this.eastbourneImportService.getImportLogs(importData.importId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (importLogs) => {
          this.vehicleData = this.eastbourneImportService.parseVehicleData(importData.jsonBlob, importLogs);
          this.isLoading = false;

          // Clear any existing selections
          this.selectedVehicleIds = [];

          // Move to vehicle selection state
          this.currentState = ImportWorkflowState.VehicleSelection;
          this.errorMessage = '';
        },
        error: (error) => {
          console.warn('Failed to load ImportLog data for existing import, using fallback IDs:', error);
          // Fallback to parsing without ImportLog data
          this.vehicleData = this.eastbourneImportService.parseVehicleData(importData.jsonBlob);
          this.isLoading = false;

          // Clear any existing selections
          this.selectedVehicleIds = [];

          // Move to vehicle selection state
          this.currentState = ImportWorkflowState.VehicleSelection;
          this.errorMessage = '';
        }
      });
  }

  onBackToUpload(): void {
    this.currentState = ImportWorkflowState.Upload;
    this.selectedFile = null;
    this.previewData = null;
    this.vehicleData = [];
    this.selectedVehicleIds = [];
    this.csvContent = '';
    this.importId = '';
    this.progressStatus = null;
    this.errorMessage = '';
    this.isLoading = false;
  }

  onStartNewImport(): void {
    this.onBackToUpload();
  }

  get showUpload(): boolean {
    return this.currentState === ImportWorkflowState.Upload ||
           this.currentState === ImportWorkflowState.Uploading ||
           this.currentState === ImportWorkflowState.Previewing ||
           this.currentState === ImportWorkflowState.PreviewError;
  }

  get showVehicleSelection(): boolean {
    return this.currentState === ImportWorkflowState.VehicleSelection;
  }

  get showProgress(): boolean {
    return this.currentState === ImportWorkflowState.ImportingSelected ||
           this.currentState === ImportWorkflowState.Processing;
  }

  get showResults(): boolean {
    return this.currentState === ImportWorkflowState.Completed ||
           this.currentState === ImportWorkflowState.Failed ||
           this.currentState === ImportWorkflowState.ImportError;
  }

  get selectedVehicleCount(): number {
    return this.selectedVehicleIds.length;
  }

  get totalVehicleCount(): number {
    return this.vehicleData.length;
  }
}
