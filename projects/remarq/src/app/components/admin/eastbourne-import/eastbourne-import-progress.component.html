<div class="progress-section">

  <!-- Import Status Header -->
  <div class="status-header">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i [class]="statusIcon" class="mr-2"></i>
              Import Progress
            </h5>
          </div>
          <div class="card-body">

            <!-- Import ID and Status -->
            <div class="import-info mb-4">
              <div class="row">
                <div class="col-md-6">
                  <small class="text-muted d-block">Import ID</small>
                  <span class="font-monospace">{{ importId }}</span>
                </div>
                <div class="col-md-6">
                  <small class="text-muted d-block">Status</small>
                  <span [class]="'badge badge-' + (isProcessing ? 'primary' : isCompleted ? 'success' : 'danger')">
                    {{ statusText }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress-info mb-4" *ngIf="progressStatus">
              <div class="d-flex justify-content-between mb-2">
                <span class="progress-label">Processing Vehicles</span>
                <span class="progress-percentage">
                  {{ progressStatus.successfulLogs + progressStatus.errorLogs }} / {{ progressStatus.totalLogs }}
                  ({{ progressPercentage }}%)
                </span>
              </div>

              <div class="progress progress-lg mb-3">
                <div class="progress-bar"
                     [class.progress-bar-striped]="isProcessing"
                     [class.progress-bar-animated]="isProcessing"
                     [class.bg-success]="isCompleted"
                     [class.bg-danger]="isFailed"
                     [style.width.%]="progressPercentage">
                </div>
              </div>

              <!-- Statistics Row -->
              <div class="row stats-row">
                <div class="col-md-3 col-6">
                  <div class="stat-item">
                    <div class="stat-value text-success">{{ progressStatus.successfulLogs }}</div>
                    <div class="stat-label">Successful</div>
                  </div>
                </div>
                <div class="col-md-3 col-6">
                  <div class="stat-item">
                    <div class="stat-value text-danger">{{ progressStatus.errorLogs }}</div>
                    <div class="stat-label">Failed</div>
                  </div>
                </div>
                <div class="col-md-3 col-6">
                  <div class="stat-item">
                    <div class="stat-value text-info">{{ successRate }}%</div>
                    <div class="stat-label">Success Rate</div>
                  </div>
                </div>
                <div class="col-md-3 col-6">
                  <div class="stat-item">
                    <div class="stat-value text-muted">{{ elapsedTime }}</div>
                    <div class="stat-label">Elapsed Time</div>
                  </div>
                </div>
              </div>

              <!-- Time Estimate -->
              <div class="time-estimate mt-3" *ngIf="isProcessing">
                <small class="text-muted">
                  <i class="fas fa-clock mr-1"></i>
                  {{ estimatedTimeRemaining }}
                </small>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Real-time Logs -->
  <div class="logs-section mt-4" *ngIf="progressStatus?.logs?.length">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-list-alt mr-2"></i>
          Recent Activity
          <small class="text-muted ml-2">(Last 10 entries)</small>
        </h6>
      </div>
      <div class="card-body p-0">
        <div class="logs-container">
          <div class="list-group list-group-flush">
            <div *ngFor="let log of recentLogs; trackBy: trackByLogId"
                 class="list-group-item"
                 [class]="getLogClass(log.status)">
              <div class="d-flex justify-content-between align-items-start">
                <div class="log-content">
                  <div class="log-vehicle">
                    <i [class]="getLogIcon(log.status)" class="mr-2"></i>
                    <span class="vehicle-reg">{{ log.vehicleRegistration }}</span>
                    <span *ngIf="log.step" class="log-step ml-2">
                      <small class="text-muted">{{ log.step }}</small>
                    </span>
                  </div>
                  <div *ngIf="log.message" class="log-message mt-1">
                    <small>{{ log.message }}</small>
                  </div>
                </div>
                <div class="log-time">
                  <small class="text-muted">{{ log.timestamp | date:'HH:mm:ss' }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading States -->
  <div *ngIf="currentState === ImportWorkflowState.ImportingSelected" class="text-center py-5">
    <div class="spinner-border text-primary mb-3" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <h5>Starting Import Process</h5>
    <p class="text-muted">Setting up the vehicle import workflow...</p>
  </div>

</div>
