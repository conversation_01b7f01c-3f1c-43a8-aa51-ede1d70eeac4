<div class="results-section">

  <!-- Results Header -->
  <div class="results-header">
    <div class="card">
      <div class="card-body text-center py-5">

        <!-- Status Icon and Title -->
        <div class="status-display mb-4">
          <i [class]="statusIcon" style="font-size: 4rem;"></i>
          <h3 class="mt-3 mb-2">{{ statusTitle }}</h3>
          <p class="text-muted">{{ statusMessage }}</p>
        </div>

        <!-- Import Summary Stats -->
        <div class="summary-stats" *ngIf="progressStatus">
          <div class="row">
            <div class="col-md-3 col-6 mb-3">
              <div class="stat-card">
                <div class="stat-value text-primary">{{ progressStatus.totalLogs }}</div>
                <div class="stat-label">Total Vehicles</div>
              </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
              <div class="stat-card">
                <div class="stat-value text-success">{{ progressStatus.successfulLogs }}</div>
                <div class="stat-label">Successful</div>
              </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
              <div class="stat-card">
                <div class="stat-value text-danger">{{ progressStatus.errorLogs }}</div>
                <div class="stat-label">Failed</div>
              </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
              <div class="stat-card">
                <div class="stat-value text-info">{{ successRate }}%</div>
                <div class="stat-label">Success Rate</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Processing Time -->
        <div class="processing-time" *ngIf="progressStatus">
          <small class="text-muted">
            <i class="fas fa-clock mr-1"></i>
            Completed in {{ processingTime }}
          </small>
        </div>

      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="action-section mb-4">
    <div class="card">
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3 mb-md-0">
            <button class="btn btn-outline-primary btn-block"
                    (click)="downloadResults()"
                    [disabled]="!progressStatus?.logs?.length">
              <i class="fas fa-download mr-2"></i>
              Download Detailed Report
            </button>
          </div>
          <div class="col-md-6">
            <button class="btn btn-primary btn-block"
                    (click)="onStartNewImport()">
              <i class="fas fa-plus mr-2"></i>
              Start New Import
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed Results -->
  <div class="detailed-results" *ngIf="progressStatus?.logs?.length">

    <!-- Success Tab -->
    <div class="card" *ngIf="successfulVehicles.length">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-check-circle text-success mr-2"></i>
          Successfully Processed Vehicles ({{ successfulVehicles.length }})
        </h6>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Registration</th>
                <th>Message</th>
                <th>Processed At</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let vehicle of successfulVehicles; trackBy: trackByLogId">
                <td>
                  <span class="badge badge-success">{{ vehicle.vehicleRegistration }}</span>
                </td>
                <td>{{ vehicle.message || 'Successfully processed' }}</td>
                <td>
                  <small class="text-muted">{{ vehicle.timestamp | date:'medium' }}</small>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Failures Tab -->
    <div class="card mt-3" *ngIf="failedVehicles.length">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-exclamation-circle text-danger mr-2"></i>
          Failed Vehicles ({{ failedVehicles.length }})
        </h6>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th>Registration</th>
                <th>Error Message</th>
                <th>Failed At</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let vehicle of failedVehicles; trackBy: trackByLogId">
                <td>
                  <span class="badge badge-danger">{{ vehicle.vehicleRegistration }}</span>
                </td>
                <td class="text-danger">{{ vehicle.message || 'Processing failed' }}</td>
                <td>
                  <small class="text-muted">{{ vehicle.timestamp | date:'medium' }}</small>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- No Results Message -->
    <div *ngIf="!successfulVehicles.length && !failedVehicles.length" class="card">
      <div class="card-body text-center py-5">
        <i class="fas fa-info-circle text-muted mb-3" style="font-size: 2rem;"></i>
        <h5>No Detailed Results Available</h5>
        <p class="text-muted">
          The import process completed, but no detailed log entries were recorded.
        </p>
      </div>
    </div>

  </div>

  <!-- Import Error Details -->
  <div class="error-details" *ngIf="isFailed">
    <div class="card border-danger">
      <div class="card-header bg-danger text-white">
        <h6 class="mb-0">
          <i class="fas fa-exclamation-triangle mr-2"></i>
          Error Details
        </h6>
      </div>
      <div class="card-body">
        <div class="alert alert-danger">
          <h6>Import Process Failed</h6>
          <p class="mb-0">
            The import encountered critical errors and was unable to complete.
            Please check the error logs above for specific details about which vehicles failed to process.
          </p>
        </div>

        <div class="troubleshooting">
          <h6>Common Solutions:</h6>
          <ul>
            <li>Verify the CSV file format matches the expected Eastbourne format</li>
            <li>Check that all required fields are present and properly formatted</li>
            <li>Ensure vehicle registrations are valid and not duplicated</li>
            <li>Verify that the system has sufficient resources to process the import</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

</div>
