import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService, DataServiceInterface } from '../../../global/services';
import {
  CreateImportRecordDTO,
  ImportHistoryEntry,
  ImportHistoryResponse,
  ImportProgressStatus
} from 'projects/common/interfaces';

@Injectable({
  providedIn: 'root'
})
export class ImportService {
  constructor(
    private apiClient: ApiService,
    private data: DataServiceInterface
  ) {}

  getImportProgress(importId: string): Observable<ImportProgressStatus> {
    const url = `${this.data.apiUrl}/api/imports/${importId}/progress`;

    return new Observable(observer => {
      this.apiClient.get({
        url,
        headers: { accept: 'application/json' }
      })
      .then((result: ImportProgressStatus) => {
        observer.next(result);
        observer.complete();
      })
      .catch(error => {
        observer.error(error);
      });
    });
  }


  getImportData(importId: string): Observable<CreateImportRecordDTO> {
    const url = `${this.data.apiUrl}/api/import/${importId}/details`;

    return new Observable(observer => {
      this.apiClient.get({
        url,
        headers: { accept: 'application/json' }
      })
        .then((result: CreateImportRecordDTO) => {
          observer.next(result);
          observer.complete();
        })
        .catch(error => {
          observer.error(error);
        });
    });
  }


  pollImportProgress(importId: string, pollIntervalMs: number = 2000): Observable<ImportProgressStatus> {
    return new Observable(observer => {
      const poll = () => {
        this.getImportProgress(importId).subscribe({
          next: (status) => {
            observer.next(status);
            if (status.importState === 'Processed') {
              observer.complete();
            } else {
              setTimeout(poll, pollIntervalMs);
            }
          },
          error: (error) => observer.error(error)
        });
      };
      poll();
    });
  }
}
