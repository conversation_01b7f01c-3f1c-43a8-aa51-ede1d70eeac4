import { Injectable } from '@angular/core';
import { Observable, interval } from 'rxjs';
import { map, switchMap, takeUntil, filter } from 'rxjs/operators';
import { ApiService, DataServiceInterface } from '../../../global/services';
import {
  EastbourneImportPreview,
  ImportProgressStatus,
  EastbourneImportStartResponse,
  ImportHistoryResponse,
  ImportHistoryEntry,
  CreateImportRecordDTO,
  VehiclePreviewData,
  SelectiveImportRequest,
  SelectiveImportResponse,
  EastbourneVehicleData,
  ImportLogEntry
} from 'projects/common/interfaces';

@Injectable()
export class EastbourneImportService {
  private serviceUrl = '/api/eastbourne';

  constructor(
    private apiClient: ApiService,
    private data: DataServiceInterface
  ) {}

  previewCsvImport(file: File): Observable<CreateImportRecordDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/preview-stock-conversion`;

    const formData = new FormData();
    formData.append('file', file, file.name);

    return new Observable(observer => {
      this.apiClient.post({
        url,
        data: formData,
        headers: { accept: 'application/json' }
      })
      .then((result: CreateImportRecordDTO) => {
        observer.next(result);
        observer.complete();
      })
      .catch(error => {
        observer.error(error);
      });
    });
  }

  previewCsvContent(csvContent: string): Observable<CreateImportRecordDTO> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/preview-stock-conversion-content`;

    return new Observable(observer => {
      this.apiClient.post({
        url,
        data: { csvContent },
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json'
        }
      })
      .then((result: CreateImportRecordDTO) => {
        observer.next(result);
        observer.complete();
      })
      .catch(error => {
        observer.error(error);
      });
    });
  }

  importSelectedVehicles(importId: string, importLogIds: number[]): Observable<SelectiveImportResponse> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/imports/${importId}/import-selected-vehicles`;

    return new Observable(observer => {
      this.apiClient.post({
        url,
        data: { importLogIds },
        headers: {
          accept: 'application/json',
          'Content-Type': 'application/json'
        }
      })
      .then((result: SelectiveImportResponse) => {
        observer.next(result);
        observer.complete();
      })
      .catch(error => {
        observer.error(error);
      });
    });
  }

  getImportProgress(importId: string): Observable<ImportProgressStatus> {
    const url = `${this.data.apiUrl}${this.serviceUrl}/imports/${importId}/progress`;

    return new Observable(observer => {
      this.apiClient.get({
        url,
        headers: { accept: 'application/json' }
      })
      .then((result: ImportProgressStatus) => {
        observer.next(result);
        observer.complete();
      })
      .catch(error => {
        observer.error(error);
      });
    });
  }

  pollImportProgress(importId: string, pollIntervalMs: number = 2000): Observable<ImportProgressStatus> {
    return interval(pollIntervalMs).pipe(
      switchMap(() => this.getImportProgress(importId)),
      takeUntil(
        this.getImportProgress(importId).pipe(
          filter(status => status.importState === 'Processed')
        )
      )
    );
  }

  parseVehicleData(jsonBlob: string, importLogs?: ImportLogEntry[]): VehiclePreviewData[] {
    try {
      const vehicles: EastbourneVehicleData[] = JSON.parse(jsonBlob);

      return vehicles.map((vehicle: EastbourneVehicleData, index: number) => {
        const registration = vehicle.basicInfo?.registration || 'Unknown';

        // Find matching ImportLog entry by VRM (identifier)
        const matchingLog = importLogs?.find(log => log.identifier === registration);

        return {
          id: matchingLog?.id || index, // Use ImportLog ID if available, fallback to index
          make: vehicle.basicInfo?.make || 'Unknown',
          model: vehicle.basicInfo?.model || 'Unknown',
          year: this.extractYear(vehicle),
          registration: registration,
          mileage: this.extractMileage(vehicle),
          fuelType: this.extractFuelType(vehicle),
          reserve: this.extractReserve(vehicle),
          hasImages: this.hasImages(vehicle),
          hasDamageItems: this.hasDamageItems(vehicle),
          hasTyreInfo: this.hasTyreInfo(vehicle),
          // Include ImportLog status information
          imported: matchingLog?.imported,
          errorText: matchingLog?.errorText
        };
      });
    } catch (error) {
      console.error('Error parsing vehicle data:', error);
      return [];
    }
  }

  private extractYear(vehicle: EastbourneVehicleData): number {
    const yearDetail = vehicle.details?.find((d: any) => d.fieldName?.toLowerCase().includes('registered') || d.fieldName?.toLowerCase().includes('year'));
    return yearDetail ? parseInt(yearDetail.fieldValue) || 0 : 0;
  }

  private extractMileage(vehicle: EastbourneVehicleData): number {
    const mileageDetail = vehicle.details?.find((d: any) => d.fieldName?.toLowerCase().includes('miles') || d.fieldName?.toLowerCase().includes('mileage'));
    return mileageDetail ? parseInt(mileageDetail.fieldValue.replace(/[^\d]/g, '')) || 0 : 0;
  }

  private extractFuelType(vehicle: EastbourneVehicleData): string {
    const fuelDetail = vehicle.details?.find((d: any) => d.fieldName?.toLowerCase().includes('fuel'));
    return fuelDetail?.fieldValue || 'Unknown';
  }

  private extractReserve(vehicle: EastbourneVehicleData): number {
    const reserveDetail = vehicle.details?.find((d: any) => d.fieldName?.toLowerCase().includes('reserve') || d.fieldName?.toLowerCase().includes('price'));
    return reserveDetail ? parseFloat(reserveDetail.fieldValue.replace(/[^\d.]/g, '')) || 0 : 0;
  }

  private hasImages(vehicle: EastbourneVehicleData): boolean {
    return (vehicle.mainGalleryImages && vehicle.mainGalleryImages.length > 0);
  }

  private hasDamageItems(vehicle: EastbourneVehicleData): boolean {
    return (vehicle.damageItems && vehicle.damageItems.length > 0);
  }

  private hasTyreInfo(vehicle: EastbourneVehicleData): boolean {
    return (vehicle.tyres && vehicle.tyres.length > 0);
  }

  async getImportHistory(providerId: number = 2, limit: number = 10, offset: number = 0): Promise<ImportHistoryResponse> {
    const url = `${this.data.apiUrl}/api/imports/provider/${providerId}`;

    try {
      const result = await this.apiClient.get({
        url,
        headers: { accept: 'application/json' }
      }) as ImportHistoryEntry[];

      // API returns direct array, wrap it in expected format
      return {
        imports: result,
        total: result.length
      };
    } catch (error) {
      console.error('Failed to load import history:', error);
      throw error;
    }
  }

  getImportDetails(importId: string): Observable<ImportHistoryEntry> {
    const url = `${this.data.apiUrl}/api/imports/${importId}/details`;

    return new Observable(observer => {
      this.apiClient.get({
        url,
        headers: { accept: 'application/json' }
      })
      .then((result: ImportHistoryEntry) => {
        observer.next(result);
        observer.complete();
      })
      .catch(error => {
        observer.error(error);
      });
    });
  }
}
