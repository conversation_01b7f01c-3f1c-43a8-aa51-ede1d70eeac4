@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap");
@import '../../../node_modules/@aws-amplify/ui-angular/theme.css';

// If we get weirdness.. we may need to remove this
// @import "./assets/utils/ghosts.scss";

:root {
  --inverseAspectRatio: calc(1 / var(--aspectRatio));
}

// Local font
@font-face {
  font-family: 'UKNumberPlate';
  font-style: normal;
  color: #333;
  src: url('/assets/fonts/UKNumberPlate.ttf');
}

html,
body,
app-root {
  height: 100%;
  width: 100%;
  margin: 0;
}

body {
  background-color: var(--bgColour) !important;
  font-family: var(--defaultFont);
  font-weight: 300;
}

a,
a:visited,
.link-style {
  color: var(--linkColour) !important;
  cursor: pointer;

  &.link-style-danger {
    color: var(--dangerColour) !important;
  }

  &.link-style-basic {
    color: var(--textColour) !important;
  }

  &.link-style-action {
    color: var(--actionColour) !important;
  }
}

.text-danger {
  color: var(--dangerColour) !important;
}


.form-control {
  border: 0px solid var(--inputBorderColour) !important;
  background-color: var(--inputBackgroundColour);
  border-radius: var(--input-radius) !important;

  &:focus {
    box-shadow: none !important;
    background-color: var(--inputBackgroundColour);
    -webkit-box-shadow: none;
  }

  &.inline-block {
    display: inline-block !important;
  }
}


/* ------------------------------------------
   Single-file SCSS (no mixins, no imports)
   ------------------------------------------ */

/* HOME PAGE */
#homepage {
  color: var(--colour5);
}

.w-50 {
  width: 50%;
}

.attention-box {
  background-color: var(--attentionBoxBackgroundColour) !important;
  box-shadow: 2px 2px 5px 0px rgb(50 50 50 / 5%);
  border-radius: 16px;
  cursor: pointer;
}

.vertical-centre-cells td {
  vertical-align: middle;
}

.text-danger-alt {
  color: var(--altDangerColour);
}

.overflow-x-auto {
  overflow-x: auto;
}

.text-success {
  color: var(--successColour) !important;
}

.card {
  border: 0 solid transparent !important;
  border-radius: var(--input-radius);

  .card-header {
    background-color: transparent !important;
  }
}

.accordion .card .card-header {
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-top: 5px !important;
  padding-bottom: 5px !important;

  .mdb-accordion-indicator {
    top: 5px;
  }

  a {
    &:hover {
      text-decoration: none !important;
    }
  }
}

.menu-topbar {
  background-color: #f35e48;
  height: 50px;
  padding: 20px;
  color: white;
}

.menu-topbar-left {
  float: left;
}

.menu-topbar-right {
  display: flex;
  float: right;
}

.shrink-cell {
  width: 0.1%;
  white-space: nowrap;
}

.fa-15x {
  font-size: 1.5em;
}

.fa-12x {
  font-size: 1.2em;
}

/* TABLE */
.table {
  margin-bottom: 0 !important;

  &.table-narrow {
    td,
    th {
      padding: 0.375rem;
    }

    th {
      font-size: 0.6rem !important;
    }
  }
}

/***** GLOBAL STYLES FOR THIRD PARTY COMPONENTS ****/

/* BOOTSTRAP 4 */
.popover {
  width: 440px;
  max-width: 440px !important;
}

/* NGX PAGINATION */
.pagination-page-size {
  font-size: 0.875rem;
  font-weight: 400;
  margin-top: 2px;
  padding-left: 20px;
}

.pagination-divider {
  border-top: 1px solid var(--widgetBorderColour);
  padding-top: 10px;
}

.pagination {
  a {
    color: #0a0a0a;
    display: block;
    padding: 0.1875rem 0.625rem;
    border-radius: 15px;

    &:hover {
      background-color: var(--colour12);

    }
  }

  a.disabled {
    pointer-events: none;
    cursor: default;
    color: #cacaca !important;
  }

  .current a {
    background-color: #fff;
    color: var(--textColour) !important;

    &:hover {
      background-color: var(--f7f7f7);
    }
  }
}

ul.ngx-pagination {
  margin-bottom: 3px;
}

.ngx-pagination .current,
.pagination .current {
  background-color: var(--linkColour) !important;
  border-radius: 50% !important;
  width: 30px;
  height: 30px;
  text-align: center;
  font-weight: 600;
}

/* PICKER */
.picker {
  .md-form {
    .form-control {
      padding: 0.375rem 0.75rem !important;
    }
  }
}

.toggle-label {
  display: inline-block;
  line-height: 25px;
  height: 25px;
  vertical-align: top;
  padding-left: 10px;
  font-size: 0.8rem;
  font-weight: 400;
  color: var(--floatLabelColour);
}

.cellpadding-0 {
  /* cellpadding */
  th,
  td {
    padding: 0;
  }

  /* cellspacing */
  table {
    border-collapse: separate;
    border-spacing: 0;
  }

  table {
    border-collapse: collapse;
    border-spacing: 0;
  }

  /* valign */
  th,
  td {
    vertical-align: top;
  }

  /* align (center) */
  table {
    margin: 0 auto;
  }
}

mdb-select-2 {
  position: relative;
}

// Special float labels ONLY for large dropdowns
// All other drop downs must use side-labels (input-group)
.input-group:not(.input-group-sm):not(.input-group-xs),
.select:not(.select-sm):not(.select-xs) {

  mdb-select, mdb-select-2 {


    &.mdb-select-outline {

      label.active {
        left: var(--floatLabelLeft);
      }

      label.active, .mdb-select-label {

        &.focused {
          color: var(--floatLabelFocusColour) !important;
        }

        &.active {
          background-color: transparent !important;
          top: var(--floatLabelTop);
          font-size: var(--floatLabelFontSize);
          font-weight: var(--floatLabelWeight) !important;
          transform: none;

          + div .form-control .value {
            padding: var(--floatLabelInputPadding) !important;
            line-height: 21px !important;
          }

          + .mdb-select-wrapper .mdb-select-value:not(:has(.mdb-select-placeholder)) {
            padding: var(--floatLabelInputPadding) !important;
            line-height: 21px !important;
          }
        }
      }

      label.active.mdb-select-label:empty {
        + .mdb-select-wrapper .mdb-select-value.form-control {
          padding-top: var(--btn-v-padding) !important;
          padding-bottom: var(--btn-v-padding) !important;
        }
      }

    }
  }
}

.mdb-select-outline {
  .mdb-select-value.focused, .single.focused {
    box-shadow: none !important;
  }
}

.mdb-select-dropdown, .dropdown-content {

  border-radius: var(--input-radius) !important;

  li {

    font-weight: 400;

    span {
      color: var(--textColour) !important;
    }

    &.active {
      background-color: var(--dropdownHoverItemBackgroundColour) !important;
      background-image: none;
      font-weight: 500;
    }

  }

  .mdb-select-options {

    color: var(--textColour);

    mdb-select-option {

      &.active, &:hover {
        background-color: var(--dropdownHoverItemBackgroundColour) !important;
      }

      &.active {
        font-weight: 500;
      }

      &:first-of-type {
        border-top-left-radius: var(--input-radius) !important;
        border-top-right-radius: var(--input-radius) !important;
      }

      &:last-of-type {
        border-bottom-left-radius: var(--input-radius) !important;
        border-bottom-right-radius: var(--input-radius) !important;
      }
    }
  }
}


.input-group, .select {

  &.no-wrap {
    white-space: nowrap;
    flex-wrap: nowrap;
  }

  mdb-select, mdb-select-2 {

    * {
      font-family: var(--defaultFont);
    }

    height: var(--btn-height);
    border-radius: var(--btn-radius);
    background-color: var(--inputBackgroundColour);
    box-sizing: border-box;


    &:has(.placeholder), &:has(.mdb-select-placeholder) {
      label.active {
        display: none;
      }
    }

    .form-control {

      border-radius: var(--btn-radius) !important;

      /* First one is for mdb-select, the other for mdb-select-2 */
      & > .value, &.mdb-select-value {
        padding-top: var(--btn-vpadding) !important;
        padding-bottom: var(--btn-vpadding) !important;
        line-height: var(--btn-line-height) !important;
        font-size: 0.875rem !important;
        margin-bottom: 0px;
        font-weight: 500;

        .mdb-select-value-label {
          font-weight: 500;
          color: var(--textColour);
        }

        .mdb-select-placeholder {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--placeholderColour);
          max-width: 100%;
        }
      }
    }

    & > div {
      height: 100% !important;

      & > value {
        height: 100% !important;
      }
    }

    .mdb-select-arrow,
    .mdb-select-toggle {

      top: calc(50% + 0px) !important;
      transform: translateY(-50%) !important;
      margin-top: 0px !important;
      height: calc(100% - 30%) !important;

      border-left: 1.5px solid var(--dropdownDividerColour) !important;
      width: 36px !important;
      line-height: var(--btn-line-height) !important;

      &::before {
        content: '\f107' !important;
        font-weight: 900;
        font-family: var(--fontAwesome), serif !important;
        display: inline-block;
        color: var(--dropdownArrowColour) !important;
        padding-top: 0;
        font-size: 16px;
      }
    }

    .single, .mdb-select-wrapper {

      padding-top: 0 !important;
      padding-bottom: 0 !important;

      &.form-control, & > .form-control {
        box-sizing: border-box;
        height: 100%;

        & > .value, &.mdb-select-value {
          height: 100% !important;
          padding-right: 36px !important;
          padding-left: 12px;
          padding-top: var(--btn-vpadding) !important;
          padding-bottom: var(--btn-vpadding) !important;
          line-height: var(--btn-line-height) !important;
        }
      }

      .placeholder {
        width: calc(100% - 36px) !important;
        height: var(--btn-height);
        font-size: 0.875rem;
        line-height: var(--btn-line-height) !important;
        padding-top: var(--btn-vpadding) !important;
        padding-bottom: var(--btn-vpadding) !important;
      }

    }
  }

  border: 0 solid transparent;

  .input-group-append {
    > {
      .input-group-text {
        border-top-right-radius: var(--input-radius);
        border-bottom-right-radius: var(--input-radius);
      }
    }
  }

  .input-group-prepend {

    /* Inside the prepend */
    > {
      .input-group-text {
        padding-left: var(--btn-hpadding) !important;
        padding-right: 3px;
        border-top-left-radius: var(--input-radius);
        border-bottom-left-radius: var(--input-radius);
      }
    }

    /* AFTER the prepend */
    + {
      .input-group-text {

        padding-right: 0;

        /* Surely there's no need to round it if we've just prepended ?
        border-top-left-radius: var(--input-radius);
        border-bottom-left-radius: var(--input-radius);
         */

        &:has(+ .input-group-append) {
          border-top-right-radius: 0 !important;
          border-bottom-right-radius: 0 !important;
        }
      }

      input.form-control {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }

      // TODO - ideally want to deprecate using a bare div
      .input-group-item, div {
        height: 100%;
        position: relative;

        mdb-select, mdb-select-2 {
          border-top-left-radius: 0 !important;
          border-bottom-left-radius: 0 !important;
        }
      }
    }
  }

  .input-group-text {
    border: 0 solid transparent;
    padding-top: var(--input-group-padding) !important;
    padding-bottom: var(--input-group-padding) !important;
  }

  height: var(--btn-height);

  input.form-control {
    height: 100%;
  }
}

.input-group.input-group-sm, .select.select-sm {

  height: var(--btn-sm-height) !important;
  border-radius: var(--input-sm-radius) !important;
  box-sizing: border-box;

  > .input-group-item .form-control {
    border-radius: var(--btn-sm-radius) !important;
  }

  mdb-select, mdb-select-2 {

    height: var(--btn-sm-height) !important;
    border-radius: var(--btn-sm-radius) !important;


    .form-control {

      border-radius: var(--btn-sm-radius) !important;

      & > .value, &.mdb-select-value {
        padding-top: var(--btn-sm-vpadding) !important;
        padding-bottom: var(--btn-sm-vpadding) !important;
        line-height: var(--btn-sm-line-height) !important;
      }

      .placeholder {
        width: calc(100% - 36px) !important;
        height: var(--btn--sm-height);
        line-height: var(--btn-sm-line-height) !important;
        padding-top: var(--btn-sm-vpadding) !important;
        padding-bottom: var(--btn-sm-padding) !important;
      }
    }

    .mdb-select-arrow,
    .mdb-select-toggle {
      line-height: var(--btn-sm-line-height) !important;
    }
  }

  .input-group-append {
    > {
      .input-group-text {
        border-top-right-radius: var(--btn-sm-radius);
        border-bottom-right-radius: var(--btn-sm-radius);
      }
    }
  }


  .input-group-prepend {
    .input-group-text {
      padding-left: var(--btn-sm-hpadding) !important;
      border-top-left-radius: var(--btn-sm-radius);
      border-bottom-left-radius: var(--btn-sm-radius);
    }

    + input.form-control {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
    }
  }

  .input-group-text {
    height: var(--btn-sm-height);
  }

  /*
  > input.form-control {
    border-top-left-radius: var(--btn-sm-radius) !important;
    border-bottom-left-radius: var(--btn-sm-radius) !important;
  }
  */

  > input.form-control:not(:has(+ .input-group-append)) {

    border-top-right-radius: var(--btn-sm-radius) !important;
    border-bottom-right-radius: var(--btn-sm-radius) !important;


    height: 100% !important;

    .value {
      border-top-right-radius: var(--btn-sm-radius) !important;
      border-bottom-right-radius: var(--btn-sm-radius) !important;
    }
  }
}

.input-group.input-group-xs, .select.select-xs {

  height: var(--btn-xs-height) !important;
  border-radius: var(--input-xs-radius) !important;

  mdb-select, mdb-select-2 {

    height: var(--btn-xs-height) !important;
    border-radius: var(--btn-xs-radius) !important;

    .form-control {

      border-radius: var(--btn-xs-radius) !important;

      & > .value, &.mdb-select-value {
        padding-top: var(--btn-xs-vpadding) !important;
        padding-bottom: var(--btn-xs-vpadding) !important;
        line-height: var(--btn-xs-line-height) !important;
      }

      .placeholder {
        width: calc(100% - 36px) !important;
        height: var(--btn--xs-height);
        line-height: var(--btn-xs-line-height) !important;
        padding-top: var(--btn-xs-vpadding) !important;
        padding-bottom: var(--btn-xs-padding) !important;
      }
    }

    .mdb-select-arrow,
    .mdb-select-toggle {
      line-height: var(--btn-xs-line-height) !important;
    }
  }

  .input-group-append {
    > {
      .input-group-text {
        border-top-right-radius: var(--btn-xs-radius);
        border-bottom-right-radius: var(--btn-xs-radius);
      }
    }
  }


  .input-group-prepend {
    .input-group-text {
      padding-left: var(--btn-xs-hpadding) !important;
      border-top-left-radius: var(--btn-xs-radius);
      border-bottom-left-radius: var(--btn-xs-radius);
    }

    + input.form-control {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
    }
  }

  .input-group-text {
    height: var(--btn-xs-height);
    font-size: var(--btn-xs-fontSize);
  }

  /*

  > input.form-control {
    border-top-left-radius: var(--btn-xs-radius) !important;
    border-bottom-left-radius: var(--btn-xs-radius) !important;
  }
  */

  > input.form-control:not(:has(+ .input-group-append)) {

    border-top-right-radius: var(--btn-xs-radius) !important;
    border-bottom-right-radius: var(--btn-xs-radius) !important;


    height: 100% !important;

    .value {
      border-top-right-radius: var(--btn-xs-radius) !important;
      border-bottom-right-radius: var(--btn-xs-radius) !important;
    }
  }
}

.input-group-text {
  border-radius: 0;
}


.input-group {

  .input-group-item {

    &:has(+ .input-group-append) {
      .form-control {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
      }
    }

    + .input-group-item {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;

      .form-control {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }
    }

    &:has(+ .input-group-item) {
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;

      mdb-select, mdb-select-2 {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
      }

      .form-control {
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
      }
    }
  }

  .input-group-prepend + .input-group-text {
    .form-control {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
    }
  }

  .input-group-prepend + .input-group-item {
    .form-control {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
    }
  }
}


/* MDB FORM FLOAT LABELS */
h1.inline-input {
  margin-bottom: 0;
  line-height: 34px;
}

/*

.input-group {


  .input-group-prepend {

    border-top-left-radius: 18px;
    border-bottom-left-radius: 18px;

    .input-group-text {
      border-top-left-radius: 18px;
      border-bottom-left-radius: 18px;
      border: 0px solid transparent;
      font-size: 0.875rem;
      padding-right: 0px;
    }

    & + div, & + input.form-control {
      border-top-left-radius: 0px !important;
      border-bottom-left-radius: 0px !important;

      mdb-select, mdb-select-2 {
        .form-control {
          border-top-left-radius: 0px !important;
          border-bottom-left-radius: 0px !important;
        }
      }
    }
  }

  &.input-group-sm {

    height: 37px;
    box-sizing: border-box;

    .input-group-prepend {
      border-top-left-radius: 18px;
      border-bottom-left-radius: 18px;

      .input-group-text {
        border-top-left-radius: 18px;
        border-bottom-left-radius: 18px;
        padding-left: 11px;
      }
    }

    mdb-select, mdb-select-2 {
      .form-control, .value {
        border-top-right-radius: 18px !important;
        border-bottom-right-radius: 18px !important;
      }
    }
  }

  &.input-group-xs {
    height: 27px;

    .input-group-prepend {
      border-top-left-radius: 13px;
      border-bottom-left-radius: 13px;

      .input-group-text {
        border-top-left-radius: 13px;
        border-bottom-left-radius: 13px;
      }
    }
  }
}

 */


/*
.md-form.narrowest {
  margin-bottom: 0 !important;

  label {
    font-size: 0.9rem;
    transform: translateY(9px);
  }

  .float-label.form-control:focus,
  .float-label.form-control:not(:placeholder-shown) {
    padding: 5px 12px 2px 12px !important;
  }

  .form-control {
    line-height: 1.7rem;
    margin-bottom: 0;
    font-size: 0.9rem;
  }
}
 */

.inline-error-message {
  color: var(--errorColour);
  font-size: 0.75rem;
  font-weight: 400;
}

input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0.5;
}

.tooltip-600 .tooltip-inner {
  max-width: 600px !important;
}

.tooltip-500 .tooltip-inner {
  max-width: 500px !important;
}

mdb-select .single.form-control {
  background-color: var(--inputBackgroundColour) !important;
}

textarea.form-control {
  background-color: var(--inputBackgroundColour) !important;
}


/* .md-form with float-labels */

.select-form, .md-form {
  margin-bottom: 1.2rem !important;

  &.mb-0 {
    margin-bottom: 0px !important;
  }
}

.md-form {

  position: relative;
  margin-top: 0 !important;

  .form-control {
    padding: var(--input-padding) !important;
  }

  &.input-sm, .input-sm {

    label {
      transform: translateY(8px);
      font-size: 14px;
    }

    &.mb-0 {
      margin-bottom: 0 !important;
    }

    input.form-control {
      padding: var(--input-sm-padding) !important;
      line-height: var(--input-sm-line-height) !important;
      height: var(--input-sm-height) !important;
      border-radius: var(--input-sm-radius) !important;
    }
  }

  &.input-xs, .input-xs {
    label {
      transform: translateY(2px);
    }

    &.mb-0 {
      margin-bottom: 0 !important;
    }

    input.form-control {
      padding: var(--input-xs-padding) !important;
      line-height: var(--input-xs-line-height) !important;
      height: var(--input-xs-height) !important;
      border-radius: var(--input-xs-radius) !important;
    }
  }

  input.form-control {
    border-radius: var(--input-radius);

    &.mb-0 {
      margin-bottom: 0 !important;
    }
  }

  .currencyPrepend {
    left: 23px !important;
  }

  label.active {
    transform: none !important;
  }

  &.non-fixed-height {
    margin-bottom: 0.1rem !important;
    margin-top: 0.9rem !important;

    .error-message {
      margin-left: 5px;
      position: relative;
      top: -8px !important;
      height: 5px !important;
      margin-bottom: 25px;
      font-size: 0.75rem;
      font-weight: 400;
    }
  }

  .form-control {
    box-sizing: border-box !important;
    background-color: var(--inputBackgroundColour) !important;
  }

  input[type="number"].form-control {
    background-color: var(--inputBackgroundColour) !important;
  }

  /*

  input[type="date"].float-label.form-control {
    padding-left: 10px !important;
    height: 43.2px !important;
    background-color: var(--inputBackgroundColour);

    &.focus {
      padding-top: 10px !important;
      color: var(--floatInputFocusLabelColour) !important;
    }

    &:focus,
    &:not(:placeholder-shown) {
      padding: 19.5px 6.4px 6.5px 12px !important;
    }

    & + label {
      top: var(--floatLabelTop) !important;
      padding-left: 12px !important;
      color: var(--placeholderColour) !important;
      font-size: 11px !important;
      font-weight: var(--placeholderWeight) !important;
      transform: none;
      background-color: transparent;
    }

    & + label.active {
      transform: none !important;
      position: absolute !important;
      left: var(--floatLabelLeft) !important;
      background-color: transparent;
      padding-left: var(--floatLabelPadding) !important;
      padding-right: var(--floatLabelPadding) !important;
      font-weight: var(--floatLabelWeight) !important;
      z-index: 3;
      color: #747b86 !important;
      font-size: 11px !important;
      top: var(--floatLabelTop) !important;
    }

    &:focus + label {
      color: var(--floatInputFocusLabelColour) !important;
      font-weight: var(--floatInputFocusLabelWeight) !important;
    }
  }

   */

  /* Standard Float Label */
  .float-label.form-control {
    padding-left: 10px !important;

    &.ng-invalid.ng-dirty:not(:focus) {
      border-color: var(--errorColour) !important;
    }

    &.ng-invalid.ng-dirty:not(:focus) + label {
      color: var(--errorColour) !important;
    }

    &:focus {
      padding-top: 10px !important;
      border-color: var(--floatInputFocusBorderColour) !important;
    }

    &:focus,
    &:not(:placeholder-shown) {
      padding: var(--floatLabelInputPadding) !important;
    }

    & + label {
      top: 0 !important;
      padding-left: 12px !important;
      color: var(--placeholderColour) !important;
      font-weight: var(--placeholderWeight) !important;
    }

    & + label.active, &[type="date"] + label, &[type="time"] + label {
      transform: none !important;
      position: absolute !important;
      left: var(--floatLabelLeft) !important;
      background-color: var(--floatLabelBackgroundColour);
      padding-left: var(--floatLabelPadding) !important;
      padding-right: var(--floatLabelPadding) !important;
      font-weight: var(--floatLabelWeight) !important;
      font-size: var(--floatLabelFontSize) !important;
      z-index: 3;
      color: #747b86 !important;
      top: 4px !important;
    }

    & + label.currencyPrepend.active,
    .currencyPrepend {
      left: 31px !important;
    }

    &:focus + label {
      color: var(--floatLabelFocusColour) !important;
      font-weight: var(--floatInputFocusLabelWeight) !important;
    }
  }

  /* For inline float labels search forms -- no space for errors */
  &.inline {
    margin-top: 0.0rem !important;
    margin-bottom: 0.0rem !important;

    .float-label.form-control {
      padding-top: 0.375rem !important;
      padding-bottom: 0.375rem !important;
      margin-bottom: 0;

      & + label {
        top: -4px !important;
      }

      & + label.active {
        position: relative;
        top: var(--floatLabelTop);
        left: var(--floatLabelLeft);
        font-weight: var(--floatLabelWeight);
      }
    }
  }

  &.narrow {
    margin-top: 0.4rem !important;
    margin-bottom: 0.4rem !important;

    .form-control {
      margin-bottom: 0;
    }
  }

  &.prepend-spacer {
    margin-top: 0.4rem !important;

    .form-control {
      margin-bottom: 0;
    }
  }

  select.form-control {
    padding-left: 7px !important;
  }
}

/* Error messages for .md-form, .md-select */
.md-form,
.md-select {

  .error-message {
    top: 49px;
    left: 9px !important;
    color: var(--errorColour);
    font-weight: 400;
    position: absolute;
    font-size: 0.65rem;
    line-height: 0.65rem;
    padding-left: var(--floatLabelPadding);
    padding-right: var(--floatLabelPadding);
    background-color: var(--floatLabelBackgroundColour);

    &.no-float {
      top: 0 !important;
      position: relative !important;
    }
  }

  &.input-sm, .input-sm {

    .error-message {
      top: 39px !important;
    }
  }
}

.dropdown-menu {
  border-radius: 10px;
  overflow-x: hidden;
}

/* Button Group */
.btn-group {

  background-color: var(--buttonGroupBackgroundColour);
  padding: 5px;
  border-radius: 20px;

  .btn-sm {
    height: 27px;
    line-height: 15px;
  }

  .btn {
    font-weight: 400;
    margin-bottom: 0px;
    border: 0px !important;
    color: var(--floatLabelColour);
    white-space: nowrap;
  }

  .btn.active {
    color: var(--textColour) !important;
    background-color: #fff !important;
    border-radius: 16px 16px 16px 16px !important;
    font-weight: 500;
  }

  &.on-background {
    .btn {
      font-weight: 400;
    }
  }
}

/* DELETE MODAL */
.modal-danger {
  a.btn {
    color: var(--colour3) !important;
  }
}

/* MDB SWITCHES */
.switch.round label .lever {
  width: 47px;
  height: 25px;
  border-radius: 10em;
}

.switch.round label .lever:after {
  width: 21px;
  height: 21px;
  border-radius: 50%;
  left: 2px;
  top: 2px;
  box-shadow: none !important;
}

.switch {
  &.blue-white-switch label {
    input[type="checkbox"]:checked + .lever {
      background-color: var(--switchColour);
    }

    input[type="checkbox"]:checked + .lever:after {
      background-color: #fff;
    }

    .lever {
      background-color: var(--inputBackgroundColour);
      margin-left: 0;
    }

    .lever:after {
      background-color: #fff;
    }
  }

  &.green-white-switch label {
    input[type="checkbox"]:checked + .lever {
      background-color: forestgreen;
    }

    input[type="checkbox"]:checked + .lever:after {
      background-color: #fff;
    }

    .lever {
      background-color: #ccc;
      margin-left: 0;
    }

    .lever:after {
      background-color: #fff;
    }
  }

  &.red-white-switch label {
    input[type="checkbox"]:checked + .lever {
      background-color: var(--errorColour);
    }

    input[type="checkbox"]:checked + .lever:after {
      background-color: #fff;
    }

    .lever {
      background-color: #ccc;
      margin-left: 0;
    }

    .lever:after {
      background-color: #fff;
    }
  }
}

.fa-times-circle {
  color: #c00 !important;
}

/*
.micro-select {
  &.on-background mdb-select .form-control {
    border: 1px solid #ced4da !important;
  }

  .mdb-select-toggle {
    height: 29px;
    line-height: 29px;
    top: calc(50% + 8px);
  }

  mdb-select:not(.inline) .single,
  mdb-select:not(.inline) .mdb-select-value,
  mdb-select-2:not(.inline) .single,
  mdb-select-2:not(.inline) .mdb-select-value,
  mdb-date-picker:not(.inline) .single,
  mdb-date-picker:not(.inline) .mdb-select-value {

    padding-bottom: 0.15rem !important;
    padding-top: 0.15rem !important;

    .mdb-select-arrow {
      height: 30px !important;
      line-height: 30px !important;
      width: 30px !important;
    }
  }

  .single .value {
    font-size: 0.9rem !important;
  }
}

.narrow-select {
  mdb-select:not(.inline) .single,
  mdb-select:not(.inline) .mdb-select-value,
  mdb-select-2:not(.inline) .single,
  mdb-select-2:not(.inline) .mdb-select-value,
  mdb-date-picker:not(.inline) .single,
  mdb-date-picker:not(.inline) .mdb-select-value {
    padding-bottom: 0.35rem !important;
    padding-top: 0.375rem !important;

    .mdb-select-arrow {
      height: 47px !important;
      line-height: 47px !important;
      width: 38px !important;
    }
  }

  .single .value {
    font-size: 0.9rem !important;
  }

  label:not(.active) {
    font-weight: 500;
    color: #bbb;
    font-size: 0.9rem;
  }
}

.select-xs {
  &.no-margin {
    .mdb-select-value {
      margin-bottom: 0 !important;
    }

    .mdb-select-wrapper {
      height: 25px !important;
    }
  }

  &.no-margin mdb-select:not(.inline) .single,
  mdb-select:not(.inline) .mdb-select-value,
  mdb-select-2:not(.inline) .single,
  mdb-select-2:not(.inline) .mdb-select-value,
  mdb-date-picker:not(.inline) .single,
  mdb-date-picker:not(.inline) .mdb-select-value {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  mdb-select:not(.inline) .single,
  mdb-select:not(.inline) .mdb-select-value,
  mdb-select-2:not(.inline) .single,
  mdb-select-2:not(.inline) .mdb-select-value,
  mdb-date-picker:not(.inline) .single,
  mdb-date-picker:not(.inline) .mdb-select-value {
    line-height: 25px;
    height: 25px;
    font-size: 13px !important;

    .mdb-select-arrow {
      height: 25px !important;
      line-height: 25px !important;
      width: 30px !important;

      &:before {
        height: 25px;
      }
    }
  }

  .single .value {
    font-size: 0.75rem !important;
  }

  label:not(.active) {
    font-weight: 500;
    color: #bbb;
    font-size: 0.75rem !important;
  }
}
 */

.input-checkbox-sm {
}

.input-sm {
  /* For type=text|date|time with .form-control */
  input[type="text"].form-control,
  input[type="number"].form-control,
  input[type="date"].form-control,
  input[type="time"].form-control {

    padding-top: var(--button-sm-vpadding) !important;
    padding-bottom: var(--button-sm-vpadding) !important;
    line-height: var(--input-sm-line-height) !important;
    height: var(--btn-sm-height) !important;
  }
}

/*
.mdb-select.white-background .mdb-select-value.form-control {
  background-color: #fff !important;
}

.select-sm {
  &.no-margin {
    .mdb-select-value {
      margin-bottom: 0 !important;
    }

    .mdb-select-wrapper {
      height: 31px !important;
    }
  }

  &.no-margin mdb-select:not(.inline) .single,
  mdb-select:not(.inline) .mdb-select-value,
  mdb-select-2:not(.inline) .single,
  mdb-select-2:not(.inline) .mdb-select-value,
  mdb-date-picker:not(.inline) .single,
  mdb-date-picker:not(.inline) .mdb-select-value {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  mdb-select:not(.inline) .single,
  mdb-select:not(.inline) .mdb-select-value,
  mdb-select-2:not(.inline) .single,
  mdb-select-2:not(.inline) .mdb-select-value,
  mdb-date-picker:not(.inline) .single,
  mdb-date-picker:not(.inline) .mdb-select-value {
    line-height: 31px !important;
    height: 31px !important;
    font-size: 15px !important;

    .mdb-select-arrow {
      height: 31px !important;
      line-height: 31px !important;
      width: 30px !important;

      &:before {
        height: 31px !important;
      }
    }
  }

  .single .value {
    font-size: 0.75rem !important;
  }

  label:not(.active) {
    font-weight: 500;
    color: #bbb;
    font-size: 0.75rem !important;
  }
}

.narrowest-select {
  &.no-margin {
    .mdb-select-value {
      margin-bottom: 0 !important;
    }

    .mdb-select-wrapper {
      height: auto;
    }
  }

  &.no-margin mdb-select:not(.inline) .single,
  mdb-select:not(.inline) .mdb-select-value,
  mdb-select-2:not(.inline) .single,
  mdb-select-2:not(.inline) .mdb-select-value,
  mdb-date-picker:not(.inline) .single,
  mdb-date-picker:not(.inline) .mdb-select-value {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  mdb-select:not(.inline) .single,
  mdb-select:not(.inline) .mdb-select-value,
  mdb-select-2:not(.inline) .single,
  mdb-select-2:not(.inline) .mdb-select-value,
  mdb-date-picker:not(.inline) .single,
  mdb-date-picker:not(.inline) .mdb-select-value {
    padding-bottom: 0.15rem !important;
    padding-top: 0.15rem !important;
    font-size: 0.85rem !important;

    .mdb-select-arrow {
      height: 30px !important;
      line-height: 30px !important;
      width: 30px !important;
    }
  }

  .single .value {
    font-size: 0.75rem !important;
  }

  label:not(.active) {
    font-weight: 500;
    color: #bbb;
    font-size: 0.75rem !important;
  }
}

mdb-select.mdb-select-outline > label,
mdb-select.mdb-select-outline > label.active {
  color: var(--floatLabelColour) !important;
  font-size: 11px !important;
}

mdb-select-dropdown .dropdown-content li > a,
mdb-select-dropdown .dropdown-content li > span {
  color: var(--inputTextColour) !important;
  height: 20px;
  line-height: 20px;
  font-weight: 400;
}

.on-background {
  .md-form input[type="text"]:focus:not([readonly]) + label {
    background-color: var(--bgColour) !important;
  }

  mdb-select.mdb-select-outline > label.active {
    background-color: var(--bgColour) !important;
  }
}

mdb-select.mdb-select-outline > label.active {
  top: var(--floatLabelTop) !important;
  left: var(--floatLabelLeft) !important;
  font-size: var(--floatLabelFontSize) !important;
  position: absolute !important;
  transform: none !important;
}

.heavy-rain-gradient {
  background-image: none;
}

 */

.form-control {
  font-size: 0.875rem;
  color: var(--textColour);
  font-weight: 500;
}

/* SELECT

mdb-select,
mdb-select-2,
mdb-date-picker {
  position: relative;

  .md-form {
    .form-control {
      padding: 0.7rem 0 0.5rem 12px !important;
    }

    .datepicker-icon {
      padding: 6px 12px;
      background-color: #eee;
      right: 1px;
      top: 1px;
      line-height: 31px;
      border-left: 1px solid var(--softInputBorderColour);
    }

    label:not(.form-check-label) {
      top: var(--floatLabelTop) !important;
      left: var(--floatLabelLeft) !important;
      font-size: var(--floatLabelFontSize) !important;
      font-weight: var(--floatLabelWeight) !important;
      color: var(--floatLabelColour) !important;
      background-color: var(--floatLabelBackgroundColour) !important;
      padding-left: var(--floatLabelPadding) !important;
      padding-right: var(--floatLabelPadding) !important;
    }
  }

  &.ng-invalid,
  &.ng-invalid .md-form {
    input:not(:focus) {
      border: 1px solid var(--errorColour) !important;

      & + label {
        color: var(--errorColour) !important;
      }
    }
  }

  &:focus-within {
    label {
      color: var(--floatInputFocusLabelColour) !important;
    }

    .below > .form-control {
      box-shadow: none !important;
      border-color: var(--floatInputFocusBorderColour) !important;
    }
  }

  & .mdb-select-label {
    top: var(--floatLabelTop) !important;
    left: 7px !important;
    font-size: 11px !important;

    &.active {
      transform: none;
      font-size: 11px !important;
    }

    &.focused {
      color: var(--floatInputFocusLabelColour) !important;
    }
  }

  & .mdb-select-value.focused {
    box-shadow: inset 0 0 0 1px var(--floatInputFocusBorderColour) !important;
  }

  & .placeholder,
  .mdb-select-placeholder {
    color: #bbb !important;
    font-weight: 500 !important;
  }

  & > label.active {
    font-size: 0.8rem !important;
    font-weight: 500 !important;
    background-color: var(--floatLabelBackgroundColour) !important;
  }

  .single.form-control {
    padding-left: 12px !important;
    background-color: var(--inputBackgroundColour);
    padding: 5px 0 5px 0px !important;
    font-size: 0.875rem !important;

    .value {
      padding-top: 0;
      padding-bottom: 0;
      padding-right: 36px !important;
    }

    .mdb-select-value-label {
      font-weight: 500 !important;
      width: 100%;
      margin-right: 36px;
    }
  }

  &.inline {
    .mdb-select-value {
      padding-top: 0.375rem !important;
      padding-bottom: 0.375rem !important;
    }
  }

  &:not(.inline) {
    .single,
    .mdb-select-value {
    }

    .mdb-select-arrow {
      top: 3px !important;
      height: 37px !important;
      line-height: 37px !important;
      width: 36px !important;
    }
  }

  & *:not(.fas) {
    font-family: var(--font1) !important;
  }

  & > div > div.single {
    padding-top: 6px !important;
    padding-bottom: 6px !important;
    line-height: 1.5rem !important;
    font-size: 1rem !important;

    & > div.value {
      color: var(--inputTextColour) !important;
    }
  }
}

.mdb-select-arrow,
.mdb-select-toggle {

  top: calc(50% + 0px) !important;
  transform: translateY(-50%) !important;
  margin-top: 0px !important;
  height: calc(100% - 30%) !important;

  border-left: 1.5px solid var(--dropdownDividerColour) !important;
  width: 36px !important;

  &::before {
    content: '\f107' !important;
    font-weight: 900;
    font-family: var(--fontAwesome), serif !important;
    display: inline-block;
    color: var(--dropdownArrowColour) !important;
    padding-top: 0px;
    font-size: 16px;
  }
}

 */

/* Multi-select side arrow */
.mdb-select-arrow {
  right: 0 !important;
  top: 0 !important;
}

/* Single-select side arrow */
.mdb-select-toggle {
}

/* MDB CARD */
mdb-card-title {
  background-color: var(--colour1);
  color: var(--colour3);
}

.select-form {
  .mdb-select-outline {
    margin-bottom: 1.7rem;
  }
}

/* MDB CHECKBOX */
.tick-cross {
  .form-check-input[type='checkbox']:checked + label:before,
  label.btn input[type='checkbox']:checked + label:before {
    /* Tick */
    content: '\f00c' !important;
    margin-top: 0 !important;
    left: 0 !important;
    top: 0 !important;
    transform-origin: 50% 50% !important;
    transform: rotate(0deg) !important;
    color: var(--successColour) !important;
  }

  .form-check-input[type='checkbox'] + label:before,
  .form-check-input[type='checkbox']:not(.filled-in) + label:after,
  label.btn input[type='checkbox'] + label:before,
  label.btn input[type='checkbox']:not(.filled-in) + label:after {
    /* Cross */
    content: '\f00d' !important;
    font-weight: var(--fas);
    font-family: var(--fontAwesome);
    border: 1px solid var(--inputBorderColour) !important;
    width: 38px !important;
    height: 38px !important;
    margin-top: 0 !important;
    text-align: center;
    line-height: 36px;
    font-size: 26px;
    border-radius: 3px !important;
    display: inline-block !important;
    color: var(--dangerColour) !important;
  }
}


/* MDB MODAL */

.modal-dialog {
  .modal-content {
    border-radius: 16px 16px 16px 16px !important;

    .modal-header {
      border-radius: 16px 16px 0 0 !important;
      padding-left: 16px !important;
    }

    .modal-body {
      border-radius: 0 0 16px 16px !important;
      font-size: 0.875rem;
    }
  }
}

.modal-header {
  background-color: var(--modalHeaderBackgroundColour);
  color: var(--modalHeaderText-colour);
  font-weight: var(--modalHeaderFontWeight);
  font-size: var(--modalHeaderFontSize);
  padding: 0.5rem 0.7rem !important;


  &.narrow {
    padding: 0.5rem 0.5rem !important;

    h5 {
      font-size: 1rem;
    }
  }
}

.modal-body {
  background-color: var(--modalBackgroundColour);
}

.x-point .tooltip-inner {
  max-width: 300px !important;
  white-space: nowrap;
  font-size: 0.875rem;
}

.tooltip-smaller {
  .tooltip-inner {
    font-size: 0.75rem !important;
  }
}

.tooltip-width200 {
  .tooltip-inner {
    width: 200px;
  }
}

/* Side nav */
.side-nav {

  .slim.side-nav {
    width: 3rem !important;
  }

  .nav-item {
    .nav-label {
      font-size: var(--sideNavFontSize);
      font-weight: 400;
    }
  }

  z-index: 1000 !important;
  margin-top: calc(var(--totalHeaderHeight) + 2px) !important;
  width: var(--searchMenuWidth) !important;
  box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%) !important;
  border-top: 1px solid rgb(0 0 0 / 16%) !important;
  background-color: var(--sideNavBackgroundColour) !important;

  .nav-link {
    padding-bottom: 4px;
    padding-top: 4px;
    height: 34px !important;
    line-height: 24px !important;
  }

  a,
  a:visited {
    color: var(--sideNavTextColour) !important;

    &:hover {
      background-color: var(--sideNavHoverBackgroundColour) !important;
      color: var(--sideNavHoverTextColour) !important;
    }
  }
}

.nav-link {
  .fa-user {
    color: var(--topbarIconColour);
    font-size: 20px;
    margin-top: 1px;
  }
}

.dropdown-menu {
  background-color: var(--dropdownItemBackgroundColour);
  color: var(--dropdownItemTextColour) !important;

  & a:hover {
    background-color: var(--dropdownHoverItemBackgroundColour);
    color: var(--dropdownHoverItemTextColour) !important;
  }
}

/*
.input-group {

  min-height: 37px;

  .input-group-text {
    background-color: var(--inputBackgroundColour);
  }

  .input-group-prepend {

    .input-group-text {
      border-width: 0px !important;
      font-size: 0.875rem !important;
      color: var(--placeholderColour);
    }

    & + input.form-control {
    }
  }

  .input-group-append {

    .input-group-text {
      border-width: 0px !important;
      font-size: 0.875rem !important;
      color: var(--placeholderColour);
    }
  }

  // Input group with appended button
  .input-group-prepend + div {

    .form-control {
      height: 26px;
      background-color: var(--inputBackgroundColour) !important;
    }

    mdb-select .single.form-control {

      height: 25px;
      background-color: var(--inputBackgroundColour) !important;

      .value {
        padding-top: 0px !important;
        padding-bottom: 0px !important;
        padding-left: 0px !important;
        padding-right: 36px !important;
        line-height: 25px !important;
        height: 25px !important;
      }

      .mdb-select-toggle {

        height: 31px !important;
        line-height: 31px;
        transform: none;
        top: 3px;
        margin-top: 0px;

        &::before {
          padding-top: 0;
        }
      }
    }
  }
}

.tiny-input.input-group {

  min-height: 29px;

  .input-group-prepend {
    border-radius: 16px;
    background-color: var(--inputBackgroundColour);

    .input-group-text {
      line-height: 30px;
      min-width: 60px;
      text-align: center;
      display: inline-block;
      font-size: 0.875rem !important;
    }
  }

  .input-group-prepend + mdb-select-2 {
    border-radius: 0 16px 16px 0 !important;

    .mdb-select-value {
      border-radius: 0 16px 16px 0 !important;
    }
  }

  .input-group-append {
    border-radius: 16px;

    .input-group-text {
      line-height: 30px;
    }
  }


  .input-group-text,
  .form-control,
  .input-group-append .btn {
    padding: 0px 10px !important;
    height: inherit;
    font-size: 0.875rem;
  }

  .form-control {
    line-height: 30px;
    height: 30px;
    background-color: var(--inputBackgroundColour);
  }

  mdb-select-2 {
    &.background-input .mdb-select-value {
      background-color: #fff !important;
    }

    .mdb-select-wrapper {
      height: 30px !important;
    }


    .mdb-select-value {
      height: 30px !important;
      font-size: 14px !important;
      padding-top: 0px !important;
      padding-bottom: 0px !important;
      margin-bottom: 0px !important;

      .mdb-select-arrow {
        line-height: 24px !important;
        height: 24px !important;

        &:before {
          padding-top: 0px;
          height: 24px;
          line-height: 24px;
        }
      }
    }
  }
}
 */

/* MDB TABS */
.classic-tabs {

  &.alt-style {

    .nav {

      li a {
        margin-right: 10px !important;
        border-bottom: 0px solid transparent !important;

        &:hover {
          background-color: var(--underNavSelectedBgColour) !important;
        }

        &.active {
          background-color: var(--underNavSelectedBgColour) !important;
        }
      }
    }
  }

  .container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .tab-content {
    margin: 0 !important;
    box-shadow: none;
    padding: 0 !important;

    &.card {
      border-radius: 16px !important;
    }
  }

  .nav.classic-tabs {
    margin: 0 0 7px 0 !important;
  }

  .nav {
    li {
      margin-right: 5px;

      a {
        color: var(--textColour) !important;
        padding: 5px 12px 5px 12px !important;
        text-transform: none !important;
        font-size: 13px !important;
        font-weight: 600;
        line-height: 23px;
        border-bottom-width: 0px;
        border-radius: 16px !important;

        .badge {
          font-size: 100% !important;
        }

        &:hover {
          background-color: var(--colour6);
        }
      }

      a.active {
        border-bottom-width: 0px !important;
        background-color: #fff;
        box-sizing: border-box !important;
        font-weight: 600;
      }
    }

    li:first-child {
      margin-left: 0 !important;
    }
  }
}

.dropdown.remarq-dropdown .dropdown-menu {
  background-color: var(--dropdownBackgroundColour) !important;
  padding: 0 0 0 0 !important;
  position: absolute;
  box-shadow: 0 8px 8px 0 rgba(0, 0, 0, 0.3);

  .dropdown-item {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    font-size: 0.9rem;
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
    margin-top: 0px !important;
    margin-bottom: 0px !important;
    background-color: var(--dropdownItemBackgroundColour) !important;
    color: var(--dropdownItemTextColour) !important;

    &:hover {
      background-color: var(--dropdownHoverItemBackgroundColour) !important;
      color: var(--dropdownHoverItemTextColour) !important;
      text-decoration: none !important;
      box-shadow: none !important;
    }
  }
}

.dropdown,
.dropleft,
.dropright,
.dropup-material {
  &.remarq-dropdown .dropdown-menu.dropdown-primary .dropdown-item {
    &.active,
    &:hover,
    &:active {
      color: var(--dropdownHoverItemTextColour) !important;
      background-color: var(--dropdownHoverItemBackgroundColour) !important;
      box-shadow: none !important;
    }
  }
}

.grid-gap-5 {
  grid-gap: 5px;
}

.grid-gap-10 {
  grid-gap: 10px;
}

.grid-gap-15 {
  grid-gap: 15px;
}

/* The ghost-loading import and mixin calls were removed */

/* Badge Count */
.badgeCount0 {
  display: none;
}

/* Google Address Auto Complete */
.pac-container {
  z-index: 1051 !important;
}

/* MDB-OPTION */
.mdb-option {
  font-size: 0.9rem !important;
  padding-left: 13px !important;
  height: 38px !important;
}

/* Hide Chat on Mobile */
@media screen and (max-width: 768px) {
  div#hubspot-messages-iframe-container {
    visibility: hidden;
  }
}

/* Extra container sizes (XXL) */
@media only screen and (min-width: 1200px) {
  .container.footer-size,
  .container.header-size {
    max-width: 1140px;
  }
}

@media only screen and (min-width: 1200px) {
  .not-logged-in .container.footer-size,
  .not-logged-in .container.header-size {
    max-width: 1440px;
  }
}

@media (min-width: 576px) {
  #main-content:has(.container-xxl) {
    .container.header-size {
      max-width: 566px;
    }
  }
  .container-xxl {
    max-width: 566px;
    width: 100%;
    padding-right: 10px;
    padding-left: 10px;
    margin-right: auto;
    margin-left: auto;
  }
}

@media (min-width: 768px) {
  #main-content:has(.container-xxl) {
    .container.header-size {
      max-width: 748px;
    }
  }
  .container-xxl {
    max-width: 748px;
    width: 100%;
    padding-right: 10px;
    padding-left: 10px;
    margin-right: auto;
    margin-left: auto;
  }
}

@media (min-width: 992px) {
  #main-content:has(.container-xxl) {
    .container.header-size {
      max-width: 992px;
    }
  }
  .container-xxl {
    max-width: 982px;
    width: 100%;
    padding-right: 10px;
    padding-left: 10px;
    margin-right: auto;
    margin-left: auto;
  }
}

@media (min-width: 1200px) {
  #main-content:has(.container-xxl) {
    .container.header-size {
      max-width: 1160px;
    }
  }
  .container-xxl {
    max-width: 1160px;
    width: 100%;
    padding-right: 10px;
    padding-left: 10px;
    margin-right: auto;
    margin-left: auto;
  }
}

@media (min-width: 1400px) {
  #main-content:has(.container-xxl) {
    .container.header-size {
      max-width: 1360px;
    }
  }
  .container-xxl {
    max-width: 1360px;
    width: 100%;
    padding-right: 10px;
    padding-left: 10px;
    margin-right: auto;
    margin-left: auto;
  }
}

.slick-list {
  margin: 0 -5px;
}

.slick-slide {
  margin: 0 5px !important;

  &.highlighted-slide {
    outline: 1.5px solid var(--linkColour);
  }
}

.slick-prev {
  left: 25px;
}

.slick-next {
  right: 25px;
}

.slick-prev,
.slick-next {
  z-index: 2;
}

.fullscreen-centered {
  padding-top: 100px;
}

.slick-dots {
  li {
    width: 10px;

    button {
      width: 10px;

      &:before {
        width: 10px;
      }
    }
  }
}

/*** Amplify Login style would normally import theme.css, removed external import ***/

/** WEBCAM **/
.webcam-wrapper,
.webcam-wrapper video {
  width: 100% !important;
  height: 100% !important;
}

/* Sales IQ Widget position */
.siq_bR {
  bottom: 10px !important;
  right: 30px !important;
}

/* CLOSE ICON */
.close {
  color: var(--dialogCloseIconColour) !important;
}

.btn {
  border-radius: 19px;
  padding: 8px 16px;
  line-height: 21px;

  &:hover {
    background-color: var(--colour6);
  }
}

/* BUTTONS */
.btn-primary,
.bg-primary,
.btn.btn-primary {
  background-color: var(--primaryButtonColour) !important;
  border-color: var(--primaryButtonColour) !important;
  color: var(--colour3) !important;
  font-weight: 500 !important;

  &:focus {
    background-color: var(--primaryButtonColour) !important;
  }
}

.btn-success {
  background-color: var(--successColour) !important;
}

.btn-primary-outline,
.btn-outline-primary,
.bg-primary-outline {
  border: 1px solid var(--primaryButtonColour) !important;
  color: var(--primaryButtonColour) !important;

  &:hover, &:visited, &:active, &:focus {
    background-color: var(--primaryButtonColour) !important;
    color: #fff !important;
  }
}

.btn-secondary,
.bg-secondary,
.badge.bg-secondary {
  background-color: var(--secondaryButtonBackgroundColour) !important;
  border-color: var(--secondaryButtonBorderColour) !important;
  color: var(--secondaryButtonTextColour) !important;

  &:hover, &:focus, &:visited, &:active {
    background-color: var(--secondaryButtonHoverBackgroundColour) !important;
    color: var(--secondaryButtonHoverTextColour) !important;
  }
}

.btn-tertiary,
.bg-tertiary,
.badge.bg-tertiary {
  background-color: var(--tertiaryButtonBackgroundColour) !important;
  border-color: var(--tertiaryButtonBorderColour) !important;
  color: var(--tertiaryButtonTextColour) !important;

  &:hover, &:visited, &:focus, &:active {
    color: var(--tertiaryButtonHoverBackgroundColour) !important;
  }
}

.btn-tertiary-outline.outline-2 {
  border: 1.5px solid var(--tertiaryButtonBackgroundColour) !important;
}

.btn-link {
  background-color: var(--linkColour) !important;
  border-color: var(--linkColour) !important;
  color: var(--colour3);

  &:hover,
  &:visited,
  &:active {
    color: var(--colour3) !important;
    opacity: 0.9 !important;
  }
}

.btn-secondary-outline,
.btn-outline-secondary,
.bg-secondary-outline {
  border: 1px solid var(--secondaryButtonTextColour) !important;
  border-radius: 3px;
  color: var(--secondaryColour) !important;

  &:hover,
  &:active,
  &:focus {
    background-color: var(--secondaryColour) !important;
    opacity: 0.9 !important;
    color: var(--colour3) !important;
  }
}

.btn-tertiary-outline,
.btn-outline-tertiary,
.bg-tertiary-outline {

  border-radius: 19px;
  color: var(--textColour) !important;

  &:hover,
  &:active,
  &:focus {
    background-color: var(--colour6);
  }
}

/* Badges in mdb-tabs vertically aligned */
.badger {
  &.bg-badge-color {
    background-color: var(--colour9);
    color: #fff;

    &.active {
      background-color: var(--colour9);
    }
  }

  margin-top: 1px !important;
  line-height: 22px;
  border-radius: 11px !important;
  min-width: 22px !important;
  min-height: 22px !important;
  padding: 0 7px !important;
  text-align: center !important;
  vertical-align: top !important;
}

.hideZero0 {
  display: none !important;
}

.btn-danger {
  background-color: var(--danger) !important;
  border-color: var(--danger) !important;
}

.btn-danger-outline,
.btn-outline-danger {
  border: 1px solid var(--danger) !important;
  color: var(--danger);
  border-radius: 3px;

  &:hover,
  &:active,
  &:focus {
    background-color: var(--danger) !important;
    color: var(--colour3) !important;
  }
}

.btn-inline-colour {
  background-color: #e9ecef;
  border: 1px solid var(--softInputBorderColour);
  color: var(--textColour);

  &:hover {
    background-color: rgba(0, 0, 64, 0.1);
  }
}

.action-fa-colour {
  color: var(--secondaryButtonColour);
}

.form-control.input-feint {
  border: 1px solid #ced4da !important;
}

.btn-feint {
  background-color: var(--feintColour);
  border-color: var(--feintColour);
  color: var(--colour23);
}

.btn-sm {
  padding: var(--btn-sm-padding) !important;
  height: var(--btn-sm-height);
  line-height: var(--btn-sm-line-height);
  border-radius: var(--btn-sm-radius);
}

.btn-xs {
  padding: var(--btn-xs-padding) !important;
  height: var(--btn-xs-height);
  line-height: var(--btn-xs-line-height);
  border-radius: var(--btn-xs-radius);
  font-size: 0.8rem;
}

.btn-xxs {
  padding: var(--btn-xxs-padding) !important;
  font-size: var(--btn-xxs-fontSize) !important;
  height: var(--btn-xxs-height) !important;
  line-height: var(--btn-xxs-line-height) !important;
  border-radius: var(--btn-xxs-radius) !important;
}


/* TABLES */
.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--tableStripedBackgroundColour) !important;
}

.table-striped tbody tr:hover {
  background-color: var(--tableStripedHoverBackgroundColour) !important;
}

table td,
table.table-compressed td {
  font-size: 0.8rem;
  line-height: 1.5rem;
  cursor: pointer;
  font-weight: 400;

  .table-line-1, &.table-line-1 {
    font-weight: 400;
    line-height: 0.9rem;
    font-size: 0.8rem;
  }

  .table-line-2, &.table-line-2 {
    font-weight: 300;
    line-height: 0.9rem;
    font-size: 0.75rem;
    color: var(--tableLine2);
  }
}

.table-compressed td, .table-compressed th {
  padding: 8px !important;
}


.table-condensed td, .table-condensed th {
  &:first-of-type {
    padding-left: 16px !important;
  }

  &:last-of-type {
    padding-right: 16px !important;
  }

  padding: 8px !important;
}

.table-condensed th {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}

.table-condensed td {
  font-size: 0.8rem;
  line-height: 1.5rem;
  cursor: pointer;
  font-weight: 400;
}


h1 {
  letter-spacing: -1px;
  font-size: 18px;
  font-weight: 600;
  color: var(--headerColour);
}

h2,
.sub-header {
  font-size: 14px;
  font-weight: 500;
}

h3 {
  font-size: 14px;
  font-weight: 400;
}

.round-button {
  color: var(--colour7);
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }
}

.modal-background {
  background-color: #f8f8f8;
}

.modal-body .form-control {
  background-color: var(--inputBackgroundColour) !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-text-fill-color: var(--inputTextColour) !important;
  -webkit-box-shadow: 0 0 0 1000px var(--inputBackgroundColour) inset !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-width: 0 !important;
  border-color: transparent !important;
}

input.form-control::placeholder {
  color: var(--placeholderColour) !important;
  font-weight: var(--placeholderWeight) !important;
}

body,
table,
.table {
  color: var(--textColour);
}

.vrm-style {
  background-color: var(--vrmBackgroundColour) !important;
  font-weight: 700;
}

.table th {
  border-top: 0 !important;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.6rem;
  color: var(--textLabelColour);
}


.select-no-border .form-control {
  border: 0px !important;
}

/* Auto-fill fix for .md-form */
.md-form > input[type]:-webkit-autofill:not(.browser-default):not([type='search']) + label,
.md-form > input[type='time']:not(.browser-default) + label {
  display: inline-block !important;
  width: auto !important;
  position: relative;
  top: var(--floatLabelTop);
  left: var(--floatLabelleft);
  font-size: var(--floatLabelFontSize);
  font-weight: var(--floatLabelWeight);
}

/* Pair label style */
.pair-label {
  text-transform: uppercase;
  font-weight: 500;
  font-size: 0.7rem;
  color: var(--textLabelColour);
}

app-vehicle-valuation {
  .pair-value {
    font-size: 0.75rem;
  }
}

.ticklist {
  &:before {
    content: "\f00c";
    font-family: var(--fontAwesome), serif;
    display: inline-block;
    margin-left: -1.3em;
    width: 1.3em;
  }
}

.cursor-pointer {
  cursor: pointer;
}

/* Widgets */

/* For styling outside the widget */
.widget-header-style {
  padding-top: 16px;
  padding-bottom: 12px;
  font-size: 18px;
  font-weight: 500;
  color: var(--textColour);
}

.widget-padding-style {
  padding: var(--widgetPadding);
}

.widget {

  background-color: var(--widgetBgColour);
  border-radius: var(--widgetBorderRadius);

  &.widget-no-border {
    border: 0;
  }

  &.inline {
    display: inline-block;
  }

  &.padding {
    padding: 0.5rem 0.5rem;
  }

  &.form-padding {
    padding: 1.5rem 1rem;
  }

  &.widget-padding {
    padding: var(--widgetPadding);
  }

  &.side-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .small-header {
    font-size: 0.9rem;
    font-weight: 400;
  }

  .header {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--textColour);
    margin-bottom: 0.25rem;

    &.padding-top {
      padding-top: 0.75rem;
    }

    &.padding {
      padding-bottom: 0.75rem;
    }
  }

  &.margin {
    margin-bottom: 1rem;
  }
}

.widget-label {
  font-size: 1rem;
  font-weight: 500;
}

.page-header,
h1.page-header {
  line-height: 38px;
  margin-bottom: 0 !important;
}

.header-margin {
  margin-bottom: 10px;
}

/* Standard bootstrap checkboxes */
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  border-color: var(--smallCheckboxColour) !important;
  background-color: var(--smallCheckboxColour) !important;
  box-shadow: none !important;
}

.custom-control-label::before {
  border-width: 0 !important;
}

.btn:focus {
  box-shadow: none !important;
}

/* MDB override */
.form-check .form-check-input {
  width: 1rem;
  height: 1rem;
}

/* Sale type color classes */
.saletype-1 {
  color: #fff;
  background-color: var(--buyNowColour);
}

.table-hover {
  tbody tr:hover {
    td {
      background-color: var(--tableHoverBackgroundColour) !important;
    }
  }
}

ng-toggle {
  .ng-toggle-switch {
    .ng-toggle-switch-label {
      font-size: 12px;
      font-weight: 400 !important;
    }
  }
}

.modal-dialog.modal-notify .heading {
  color: var(--textColour) !important;

}

.navbar-nav {
  .dropdown-menu {
    top: 7px;
  }
}

.saletype-1 {
  color: #fff;
  background-color: var(--buyNowColour);
}

.saletype-2 {
  color: #fff;
  background-color: var(--managedSaleColour);
}

.saletype-3 {
  color: #fff;
  background-color: var(--timedSaleColour);
}

.saletype-4 {
  color: #fff;
  background-color: var(--underwriteColour);
}

.sale-type-1-outline, .sale-type-2-outline, .sale-type-3-outline, .sale-type-4-outline {
  border: 1.5px solid #dfdfdf;
  color: var(--textColour);
}

.sale-type {

  &.sale-type-1 {
    background-color: var(--buyNowColour);
    color: #fff;
  }

  &.sale-type-2 {
    background-color: var(--managedSaleColour);
    color: #fff;
  }

  &.sale-type-3 {
    background-color: var(--timedSaleColour);
    color: #fff;
  }

  &.sale-type-4 {
    background-color: var(--underwriteColour);
    color: #fff;
  }
}

