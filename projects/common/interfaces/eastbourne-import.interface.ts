export interface EastbourneVehiclePreview {
  make: string;
  model: string;
  year: number;
  registration: string;
  mileage: number;
  fuelType: string;
  reserve: number;
  rowIndex: number;
}

export interface EastbourneImportPreview {
  vehicleCount: number;
  sampleVehicles: EastbourneVehiclePreview[];
  errors: string[];
}

// New response format from preview endpoints that now create records
export interface CreateImportRecordDTO {
  importProvider: string;
  jsonBlob: string; // JSON string of vehicle data array
  importId: string;
}

export interface VehiclePreviewData {
  id?: number; // ImportLog ID for selection
  make: string;
  model: string;
  year: number;
  registration: string;
  mileage: number;
  fuelType: string;
  reserve: number;
  hasImages?: boolean;
  hasDamageItems?: boolean;
  hasTyreInfo?: boolean;
  // ImportLog status information
  imported?: boolean;
  errorText?: string;
}

export interface SelectiveImportRequest {
  importLogIds: number[];
}

export interface SelectiveImportResponse {
  message: string;
}

// Raw API response structure
export interface EastbourneImportApiResponse {
  importProvider: number;
  jsonBlob: string; // Contains JSON string of vehicle array
}

export interface ImportLogEntry {
  id: number; // ImportLog ID
  identifier: string; // VRM
  imported: boolean;
  errorText?: string;
  importId: string;
}

export interface ImportProgressStatus {
  importId: string;
  importState: 'Processing' | 'Processed';
  hasErrors: boolean;
  totalLogs: number;
  successfulLogs: number;
  errorLogs: number;
  logs: ImportLogEntry[];
}

export interface EastbourneImportStartResponse {
  importId: string;
  message: string;
}

export interface ImportHistoryEntry {
  importId: string;
  fileName?: string;
  startTime?: Date;
  endTime?: Date;
  added: string; // API uses 'added' instead of 'startTime'
  updated: string;
  status: 'processing' | 'completed' | 'failed' | 'pending';
  totalVehicles?: number; // For backwards compatibility
  totalLogs: number;
  successCount?: number; // For backwards compatibility
  successfulLogs: number;
  errorCount?: number; // For backwards compatibility
  errorLogs: number;
  importProvider: number;
  importState: number;
  statusId: number;
  hasErrors: boolean;
  processedCount: number;
  successRate: number;
  isPartialImport?: boolean;
  vehiclesImported?: string[]; // Registration numbers of vehicles that were imported
}

export interface ImportHistoryResponse {
  imports: ImportHistoryEntry[];
  total: number;
}

export enum ImportWorkflowState {
  Upload = 'upload',
  Uploading = 'uploading',
  Previewing = 'previewing',
  VehicleSelection = 'vehicle-selection',
  PreviewError = 'preview-error',
  ImportingSelected = 'importing-selected',
  ImportError = 'import-error',
  Processing = 'processing',
  Completed = 'completed',
  Failed = 'failed'
}


export interface EastbourneVehicleData {
  basicInfo: VehicleBasicInfo;
  details: VehicleDetail[];
  additionalDetails: VehicleDetail[];
  mainGalleryImages: EastbourneImage[];
  damageItems: EastbourneDamageItem[];
  interior: InteriorInfo;
  tyres: TyreInfo[];
  pdfReportUrl: string;
  namaGrade?: number;
  auctionLotId: string;
  webUrl: string;
}

export interface VehicleBasicInfo {
  make: string;
  model: string;
  fullDescription: string;
  registration: string;
  lotNumber: string;
  auctionDateTime?: string; // Use `string` to represent DateTime in JSON
}

export interface VehicleDetail {
  fieldName: string;
  fieldValue: string;
}

export interface EastbourneImage {
  thumbnailUrl: string;
  fullSizeUrl: string;
  altText: string;
  isPlaceholder: boolean;
}

export interface EastbourneDamageItem {
  component: string;
  damageType: string;
  severity: string;
  description: string;
  image: EastbourneImage;
}

export interface InteriorInfo {
  carpetCondition: string;
  seatCondition: string;
  upholsteryCondition: string;
  odour: string;
}

export interface TyreInfo {
  position: string;
  brand: string;
  depth: string;
}
