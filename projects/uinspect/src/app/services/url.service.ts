import {Injectable} from '@angular/core';
import {Router} from "@angular/router";
import {URLHelperService} from '../global/services';

@Injectable({providedIn: 'root'})
export class URLService {

  constructor(private router: Router, private urlHelperService: URLHelperService) {
  }

  redirect(path, params = null, replaceUrl = false) {

    this.router.navigate([path], {replaceUrl, queryParams: params}).then(() => {
    });
  }

  homepage(returnOnly: boolean = false) {
    const path = "";
    if (returnOnly) {
      return path;
    } else {
      this.redirect(path);
    }
  }

  login(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/login", returnOnly);
  }

  signup(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/register", returnOnly);
  }

  dashboard(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/dashboard", returnOnly);
  }

  formats(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/formats", returnOnly);
  }

  inspections(returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/main/inspections", returnOnly);
  }

  doInspection(uInspectId: string, returnOnly: boolean = false, toUrl = false): string {
    return this.urlHelperService.urlFrom("/uinspect/journey/" + uInspectId, returnOnly);
  }

  viewInspection(uInspectId: string, returnOnly: boolean = false) {

    return this.urlHelperService.urlFrom("/main/inspection/" + uInspectId, returnOnly);
  }
}

