import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import {ContactUsWrapperComponent} from "../components/non-appraisal/home/<USER>/index";
import {LoginComponent} from "../components/non-appraisal/home/<USER>/index";
import {NonAppraisalAppComponent} from "../components/non-appraisal/non-appraisal-app.component";
import {TermsConditionsWrapperComponent} from "../components/non-appraisal/home/<USER>/index";
import {PrivacyPolicyWrapperComponent} from "../components/non-appraisal/home/<USER>/index";
import {HomepageComponent} from "../components/non-appraisal/home/<USER>/index";

const routes: Routes = [
  {
    path: '',
    component: NonAppraisalAppComponent,
    children: [
      {path: '', component: HomepageComponent},
      {path: 'contact-us', component: ContactUsWrapperComponent},
      {path: 'login', component: LoginComponent},
      {path: 'terms-conditions', component: TermsConditionsWrapperComponent},
      {path: 'privacy-policy', component: PrivacyPolicyWrapperComponent},
      {path: 'main', loadChildren: () => import('./main.module').then(m => m.MainModule)},
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
})

export class NonAppraisalRoutingModule {
}
