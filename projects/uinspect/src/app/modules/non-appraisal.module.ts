import {NgModule} from '@angular/core';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {
  ButtonsModule,
  DropdownModule, MDBBootstrapModulesPro,
  MDBSpinningPreloader,
} from "ng-uikit-pro-standard";
import {HttpClientJsonpModule, HttpClientModule} from "@angular/common/http";
import {CommonModule} from "@angular/common";
import {ContactUsComponent, GoogleMapsScriptComponent, PrivacyPolicyComponent, TermsConditionsComponent} from '../global/components';
import {NonAppraisalRoutingModule} from "./non-appraisal-routing.module";
import {ContactUsWrapperComponent} from "../components/non-appraisal/home/<USER>/index";
import {AppTopbarComponent} from "../components/non-appraisal/common/app-topbar/index";
import {RouterModule} from "@angular/router";
import {CognitoService, ContactService, UserService} from '../global/services';
import {LoginComponent} from "../components/non-appraisal/home/<USER>/index";
import {AmplifyAuthenticatorModule} from "@aws-amplify/ui-angular";
import {AppFooterComponent} from "../components/non-appraisal/common/app-footer/index";
import {NonAppraisalAppComponent} from "../components/non-appraisal/non-appraisal-app.component";
import {NgsgModule} from "ng-sortgrid";
import {HttpModule, RemarqLoggerModule} from "../global/modules";
import {environment} from '../../environments/environment';
import {TermsConditionsWrapperComponent} from "../components/non-appraisal/home/<USER>/index";
import {PrivacyPolicyWrapperComponent} from "../components/non-appraisal/home/<USER>/index";
import {HomepageComponent} from "../components/non-appraisal/home/<USER>/index";
import {NgxGraphModule} from "@swimlane/ngx-graph";
import {EventService} from "../services/event.service";
import {UInspectService} from "../services/index";

@NgModule({
  declarations: [
    NonAppraisalAppComponent,
    HomepageComponent,
    ContactUsComponent,
    ContactUsWrapperComponent,
    TermsConditionsComponent,
    TermsConditionsWrapperComponent,
    PrivacyPolicyComponent,
    PrivacyPolicyWrapperComponent,
    AppTopbarComponent,
    AppFooterComponent,
    LoginComponent,
    GoogleMapsScriptComponent
  ],
  imports: [
    MDBBootstrapModulesPro.forRoot(),
    HttpModule.forRoot({environment}),
    DropdownModule.forRoot(),
    ButtonsModule.forRoot(),
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    CommonModule,
    NgsgModule,
    RemarqLoggerModule,
    AmplifyAuthenticatorModule,
    NonAppraisalRoutingModule,
    RouterModule,
    NgxGraphModule
  ],
  providers: [
    UserService,
    EventService,
    ContactService,
    CognitoService,
    MDBSpinningPreloader,
  ],
    exports: [
      GoogleMapsScriptComponent
    ]
})
export class NonAppraisalModule {
}
