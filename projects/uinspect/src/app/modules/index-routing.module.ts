import {NgModule} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {Routes, RouterModule} from '@angular/router';

const routes: Routes = [
  { path: '', loadChildren: () => import("./non-appraisal.module").then(m => m.NonAppraisalModule) },
  { path: 'uinspect', loadChildren: () => import('./appraisal.module').then(m => m.AppraisalModule) },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
    scrollPositionRestoration: 'enabled'
})],
  exports: [RouterModule, FormsModule],
  providers: []
})

export class IndexRoutingModule {
}
