import {NgModule} from '@angular/core';

import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {
  ButtonsModule,
  DropdownModule,
  MDBRootModules,
  MDBSpinningPreloader,
  ModalModule,
  TabsModule,
  TooltipModule
} from "ng-uikit-pro-standard";

import {HttpClientJsonpModule} from "@angular/common/http";
import {WebcamModule} from "ngx-webcam";
import {UInspectJourneyComponent} from "../components/appraisal/uinspect-journey/uinspect-journey.component";
import {UInspectStepComponent} from "../components/appraisal/uinspect-step/uinspect-step.component";
import {NetworkStatusComponent} from "../components/appraisal/network-status/network-status.component";
import {AppraisalRoutingModule} from "./appraisal-routing.module";
import {
  UInspectAnswerService,
  UInspectFormatService,
  UInspectMediaService,
  UInspectQuestionOptionService, UInspectQuestionService, UInspectSectionCompleteService, UInspectSectionService,
  UInspectService
} from "../services";
import {CommonModule} from "@angular/common";
import {ApiService, ImageService, LoggerService} from "../global/services";
import {AppraisalAppComponent} from "../components/appraisal/appraisal-app.component";
import {RouterModule} from "@angular/router";
import {NgxImageZoomModule} from "ngx-image-zoom";


@NgModule({
  declarations: [
    AppraisalAppComponent,
    UInspectJourneyComponent,
    UInspectStepComponent,
    NetworkStatusComponent
  ],
  imports: [
    DropdownModule.forRoot(),
    ButtonsModule.forRoot(),
    ModalModule.forRoot(),
    TabsModule.forRoot(),
    TooltipModule.forRoot(),
    ReactiveFormsModule,
    AppraisalRoutingModule,
    HttpClientJsonpModule,
    WebcamModule,
    FormsModule,
    CommonModule,
    RouterModule
  ],
  providers: [
    ApiService,
    LoggerService,
    UInspectService,
    UInspectAnswerService,
    UInspectFormatService,
    UInspectMediaService,
    UInspectQuestionOptionService,
    UInspectQuestionService,
    UInspectSectionService,
    UInspectSectionCompleteService,
    MDBSpinningPreloader,
    ImageService
  ],
  exports: []
})
export class AppraisalModule {
}
