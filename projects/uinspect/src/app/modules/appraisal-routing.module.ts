import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import {UInspectJourneyComponent} from "../components/appraisal/uinspect-journey/uinspect-journey.component";
import {NonAppraisalAppComponent} from "../components/non-appraisal/non-appraisal-app.component";
import {AppraisalAppComponent} from "../components/appraisal/appraisal-app.component";

const routes: Routes = [
  {
    component: AppraisalAppComponent,
    path: 'journey', children: [
      {path: ":uInspectId", component: UInspectJourneyComponent},
    ]
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
})

export class AppraisalRoutingModule {
}
