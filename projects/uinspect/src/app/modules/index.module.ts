import {NgModule} from '@angular/core';
import {RouterModule} from "@angular/router";
import {HTTP_INTERCEPTORS} from "@angular/common/http";
import {HeaderInterceptor} from "../interceptors/config.interceptor";
import {ApiService} from "../global/services";
import {DataService} from "../services/index";
import {CommonModule} from "@angular/common";
import {AppComponent} from "../components/app/app.component";
import {UInspectCommonService} from "../global/services/";

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HeaderInterceptor,
      multi: true
    },
    ApiService,
    DataService,
    UInspectCommonService
  ],
  exports: [
    RouterModule,
  ]
})
export class IndexModule {
}
