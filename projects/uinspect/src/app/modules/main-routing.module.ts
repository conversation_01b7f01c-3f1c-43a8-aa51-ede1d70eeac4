import {NgModule} from '@angular/core';
import {Routes, RouterModule} from '@angular/router';
import {DashboardComponent} from "../components/non-appraisal/main/dashboard/index";
import {MainComponent} from '../components/non-appraisal/main/main.component';
import {RoleGuardService as AuthGuard} from "../services/index";
import {FormatListComponent} from "../components/non-appraisal/main/formats/format-list/format-list.component";
import {FormatViewComponent} from "../components/non-appraisal/main/formats/format-view/format-view.component";
import {InspectionListComponent} from "../components/non-appraisal/main/inspection-list/inspection-list.component";
import {InspectionTestComponent} from "../components/non-appraisal/main/inspection-test/inspection-test.component";
import {InspectionViewComponent} from "../components/non-appraisal/main/inspection-view/inspection-view.component";

const routes: Routes = [
  {
    path: '',
    canActivate: [AuthGuard],
    data: {
      roles: ['ADMIN']
    },
    component: MainComponent,
    children: [
      {path: 'dashboard', component: DashboardComponent},
      {path: 'inspections', component: InspectionListComponent},
      {path: 'inspection/:id', component: InspectionViewComponent},
      {path: 'formats', component: FormatListComponent},
      {path: 'format/:id/test', component: InspectionTestComponent},
      {path: 'format/:id', component: FormatViewComponent},
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
})

export class MainRoutingModule {
}
