import {NgModule} from '@angular/core';
import {DashboardComponent} from "../components/non-appraisal/main/dashboard";
import {MainRoutingModule} from "./main-routing.module";
import {
  RoleGuardService as AuthGuard, UInspectAnswerService,
  UInspectFormatService, UInspectMediaService,
  UInspectQuestionOptionService, UInspectQuestionService,
  UInspectSectionService, UInspectService
} from "../services/index";
import {MainComponent} from "../components/non-appraisal/main/main.component";
import {AppSideMenuComponent, DragdropfilesComponent, LoadingSpinnerComponent} from '../global/components';
import {RouterModule} from "@angular/router";
import {
  MDBRootModules,
  WavesModule
} from "ng-uikit-pro-standard";
import {RemarqLoggerModule} from "../global/modules";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {HttpClientJsonpModule, HttpClientModule} from "@angular/common/http";
import {CommonModule} from "@angular/common";
import {NgsgModule} from "ng-sortgrid";
import {AmplifyAuthenticatorModule} from "@aws-amplify/ui-angular";
import {FormatListComponent} from "../components/non-appraisal/main/formats/format-list/format-list.component";
import {HelpersService, ImageService} from '../global/services';
import {FormatViewComponent} from "../components/non-appraisal/main/formats/format-view/format-view.component";
import {GraphModule} from "@swimlane/ngx-graph";
import {
  FormatEditDialogComponent
} from "../components/non-appraisal/main/formats/format-edit-dialog/format-edit-dialog.component";
import {InspectionListComponent} from "../components/non-appraisal/main/inspection-list/inspection-list.component";
import {InspectionTestComponent} from "../components/non-appraisal/main/inspection-test/inspection-test.component";
import {InspectionViewComponent} from "../components/non-appraisal/main/inspection-view/inspection-view.component";
import {DeleteConfirmModalComponent} from "../global/components";
import {
  EngagementOverTimeComponent
} from '../components/non-appraisal/main/dashboard/engagement-over-time/engagement-over-time.component';
import {
  StatsDateRangeComponent
} from '../components/non-appraisal/main/dashboard/stats-date-range/stats-date-range.component';
import {
  TotalInspectionsComponent
} from '../components/non-appraisal/main/dashboard/total-inspections/total-inspections.component';
import {SalesFunnelComponent} from '../components/non-appraisal/main/dashboard/sales-funnel/sales-funnel.component';
import {NonAppraisalModule} from './non-appraisal.module';
import {
  InspectionsMapComponent
} from '../components/non-appraisal/main/dashboard/inspections-map/inspections-map.component';
import {NgxImageZoomModule} from "ngx-image-zoom";

@NgModule({
  declarations: [
    AppSideMenuComponent,
    DashboardComponent,
    MainComponent,
    FormatListComponent,
    FormatViewComponent,
    FormatEditDialogComponent,
    InspectionListComponent,
    InspectionTestComponent,
    InspectionViewComponent,
    LoadingSpinnerComponent,
    DeleteConfirmModalComponent,
    DragdropfilesComponent,
    EngagementOverTimeComponent,
    StatsDateRangeComponent,
    TotalInspectionsComponent,
    SalesFunnelComponent,
    InspectionsMapComponent
  ],
  imports: [
    MainRoutingModule,
    RouterModule,
    MDBRootModules,
    WavesModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    CommonModule,
    NgsgModule,
    RemarqLoggerModule,
    AmplifyAuthenticatorModule,
    GraphModule,
    NonAppraisalModule,
    NgxImageZoomModule
  ],
  providers: [
    AuthGuard,
    UInspectService,
    UInspectFormatService,
    UInspectSectionService,
    UInspectQuestionOptionService,
    UInspectQuestionService,
    UInspectAnswerService,
    UInspectMediaService,
    ImageService,
    HelpersService
  ],
  exports: []
})

export class MainModule {
}
