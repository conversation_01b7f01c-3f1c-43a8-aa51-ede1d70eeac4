<div mdbModal #formatModal="mdbModal" class="modal fade" tabindex="-1"
     [config]="{backdrop: false, ignoreBackdropClick: true}"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-md modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">

        Appraisal Format

      </div>
      <div class="modal-body">
        <form [formGroup]="formatForm">

          <div class="md-form narrow-input">
            <input formControlName="title" id="title" class="form-control float-label"
                   mdbInput>
            <label for="title">Title</label>
          </div>

          <div class="md-form narrow-input">
            <input formControlName="description" id="description" class="form-control float-label"
                   mdbInput>
            <label for="title">Description</label>
          </div>

          <div class="narrow-select mb-3">

            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text" style="width: 100px;">
                  Start Section
                </div>
              </div>

              <div style="width: calc(100% - 120px)">
                <mdb-select [outline]="true"
                            [style]="{'width':'100%'}"
                            formControlName="startSectionId"
                            [options]="sectionOptions"></mdb-select>
              </div>
            </div>
          </div>

          <div class="d-flex flex-wrap column-gap">

            <div class="flex-shrink-1">

              <div class="switch blue-white-switch mt-2">
                <span class="switch-label">Disabled &nbsp;</span>
                <label>
                  <input type="checkbox" formControlName="disabled">
                  <span class="lever"></span>
                </label>
              </div>
            </div>

            <div class="flex-shrink-1">

              <div class="narrow-select">

                <div class="input-group">
                  <div class="input-group-prepend">
                    <div class="input-group-text" style="width: 70px;">
                      Status
                    </div>
                  </div>

                  <div>
                    <mdb-select [outline]="true"
                                [style]="{'width':'120px'}"
                                formControlName="statusId"
                                [options]="statusOptions"></mdb-select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-3 text-right">
            <button class="btn btn-secondary ml-2" type="button" (click)="hideModal()">Cancel</button>
            <button class="btn btn-primary ml-2" style="min-width: 80px;" (click)="saveFormat()"
                    [disabled]="formatForm.invalid">
              <span *ngIf="isEdit">Update</span>
              <span *ngIf="!isEdit">Add</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
