import {Component, OnInit, ViewChild} from '@angular/core';
import {UInspectFormatDTO, UInspectSectionDTO, User} from "../../../../../global/interfaces";
import {HelpersService, UserService} from "../../../../../global/services";
import {UInspectFormatService, UInspectSectionService, UInspectURLService} from "../../../../../services/index";
import {LeadEventTypeEnum, StatusEnum, UInspectEventTypeEnum, VehicleTypeEnum} from "../../../../../global/enums";
import {ModalDirective} from "ng-uikit-pro-standard";
import {UntypedFormBuilder, UntypedFormControl, Validators} from "@angular/forms";
import {EventService} from "../../../../../services/event.service";
import {Subscription} from "rxjs";

@Component({
  selector: 'app-format-edit-dialog',
  templateUrl: './format-edit-dialog.component.html',
  styleUrls: ['./format-edit-dialog.component.scss'],
})


export class FormatEditDialogComponent implements OnInit {

  @ViewChild('formatModal') formatModal: ModalDirective;

  statusName: {} = {};
  formatForm: any;
  private previousFormat: UInspectFormatDTO;
  private currentUser: User;
  statusOptions: { label: string; value: number }[] = [];
  isEdit: boolean;
  private uInspectEvent: Subscription;
  sectionOptions: { label: string; value: number }[] = [];

  constructor(
    private formatService: UInspectFormatService,
    private sectionService: UInspectSectionService,
    private helperService: HelpersService,
    private fb: UntypedFormBuilder,
    public url: UInspectURLService,
    public userService: UserService,
    public eventService: EventService
  ) {

  }

  async ngOnInit() {

    this.getCurrentUser();
    this.getStatuses();
    this.getSections();
    this.initForm();

    this.uInspectEvent = this.eventService.AdminEvent.subscribe((data) => {

      if (data.eventType === UInspectEventTypeEnum.ShowEditFormatModal) {
        this.isEdit = !!data.object;

        this.formatForm.reset();

        if (this.isEdit) {
          this.formatForm.patchValue(data.object.format);
        }

        this.previousFormat = this.formatForm.value;

        this.formatModal.show();
      }
    });
  }

  getCurrentUser() {

    this.userService.loadCurrentUser().then(() => {
      this.currentUser = this.userService.CurrentUser;
    });

  }

  initForm() {

    this.formatForm = this.fb.group({
      id: new UntypedFormControl(""),
      customerId: new UntypedFormControl(""),
      title: new UntypedFormControl("", [Validators.required]),
      description: new UntypedFormControl("", [Validators.required]),
      startSectionId: new UntypedFormControl(""),
      disabled: new UntypedFormControl(false),
      statusId: new UntypedFormControl(StatusEnum.Active),
    });
  }

  getStatuses() {

    const statuses = this.helperService.getNamesAndValues(StatusEnum);

    this.statusOptions = statuses.map(status => {
      this.statusName[status.value] = status.name;
      return {label: status.name, value: status.value};
    });
  }

  get f() {

    if (this.formatForm) {
      return this.formatForm?.controls;
    }
    return null;
  }

  async saveFormat() {

    // Create format
    if (this.f.id.value == null) {

      await this.formatService.add(this.formatForm.value).then((abc) => {
        this.eventService.AdminEvent.emit({ eventType: UInspectEventTypeEnum.FormatUpdated, object: { format: abc }});
      });

    } else {

      await this.formatService.patch(this.f.id.value, this.formatForm.value, this.previousFormat).then((xyz) => {
        this.eventService.AdminEvent.emit({ eventType: UInspectEventTypeEnum.FormatUpdated, object: { format: xyz }});
      });
    }


    this.formatModal.hide();
  }

  hideModal() {

    this.formatModal.hide();

  }

  /*

  addFormat() {

    this.formatForm.reset();

    this.formatForm.patchValue({
      customerId: this.currentUser.customerId,
      statusId: StatusEnum.Active,
      disabled: false
    });

    this.isEdit = false;
    this.formatModal.show();
  }

  editFormat(e, format) {

    e.stopPropagation();

    this.formatForm.patchValue(format);
    this.previousFormat = this.formatForm.value;

    this.isEdit = true;
    this.formatModal.show();
  }

   */

  private getSections() {

    this.sectionService.search({component: 'format-edit-dialog'}).then((x) => {
      this.sectionOptions = x.results
        .filter(y => y.statusId === StatusEnum.Active)
        .map(y => ({value: y.id, label: y.title}));
    });
  }
}
