import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {UInspectFormatDTO, UInspectQuestionDTO, UInspectQuestionOptionDTO, UInspectSectionDTO} from "../../../../../global/interfaces";
import {HelpersService} from "../../../../../global/services";
import {
  UInspectFormatService,
  UInspectQuestionOptionService,
  UInspectQuestionService,
  UInspectSectionService,
  UInspectURLService
} from "../../../../../services/index";
import {
  MediaTypeEnum,
  StatusEnum,
  UInspectEventTypeEnum,
  UInspectQuestionTypeEnum,
  UInspectSectionTypeEnum,
  VehicleTypeEnum
} from "../../../../../global/enums";
import {ActivatedRoute} from "@angular/router";
import {UntypedFormControl, UntypedFormGroup} from "@angular/forms";
import {compare, deepClone} from "fast-json-patch";
import {Subject, Subscription} from "rxjs";
import {EventService} from "../../../../../services/event.service";
import {FieldMappingEnum} from "../../../../../global/enums";
import {LeadDocumentCategoryDTO} from "../../../../../../../../crm/src/app/interfaces";

@Component({
  templateUrl: './format-view.component.html',
  styleUrls: ['./format-view.component.scss'],
})

export class FormatViewComponent implements OnInit {
  loadingFormat: boolean;
  vehicleTypeOptions: { label: string; value: number }[];
  statusName: {} = {};
  vehicleTypeName: {} = {};
  formatId: number;
  statusOptions: { label: string; value: number }[] = [];
  format: UInspectFormatDTO;
  mediaType: {} = {};
  nextSectionOptions: { label: string; value: number }[] = [];
  mediaTypeName: {} = {};
  sectionTypeOptions: { label: any; value: any }[] = [];
  sectionTypeName: {} = {};
  public mediaCountOptions: { label: number; value: number }[] = [];
  public mediaTypeOptions: { label: string; value: number }[] = [];
  isInteriorOptions: { label: string; value: number }[] = [];
  selectedSectionId: number = null;
  public sectionForm: UntypedFormGroup;
  public questionForm: UntypedFormGroup;
  selectedSection: UInspectSectionDTO;
  showEditSection = false;
  sectionType = UInspectSectionTypeEnum;
  public sectionQuestionForm: UntypedFormGroup;
  private sectionOptions: { label: string; value: number }[] = [];
  questionNextSectionOptions: { label: string; value: number }[] = [];
  selectedQuestion: UInspectQuestionDTO;
  private originalQuestionOptions: UInspectQuestionOptionDTO[];
  MediaTypeEnum = MediaTypeEnum;
  SectionTypeEnum = UInspectSectionTypeEnum;
  questionTypeOptions: { label: any; value: number }[];
  mapsToOptions: { label: any; value: number }[] = [
    { label: 'None', value: null },
    { label: 'Number of Keys', value: FieldMappingEnum.VehicleNumberOfKeys },
    { label: 'MOT Status', value: FieldMappingEnum.VehicleMOTStatus },
    { label: 'LeadCRM - Service History', value: FieldMappingEnum.VehicleServiceHistory },
    { label: 'V5 Status', value: FieldMappingEnum.VehicleV5Status },
    { label: 'Finance House', value: FieldMappingEnum.FinanceHouse },
  ];
  questionTypeName: {} = {};
  QuestionTypeEnum = UInspectQuestionTypeEnum;
  maskTest: string;
  @ViewChild("maskTestControl") maskTestControl: ElementRef;
  private selectedQuestionId: number;
  savingQuestion: boolean;
  savingSection: boolean;
  statusEnum = StatusEnum;
  visualise = false;
  visualiseNodes: { id: string, label: string, icon: string, data?: object }[] = [];
  visualiseLinks: { id: string, source: string, target: string, label: string }[] = [];
  simpleVisual = true;
  visualiseCenter$: Subject<boolean> = new Subject();
  placeholders = [
    {value: '', label: "== None =="},
    {value: 'front-car.svg', label: "Car - Front"},
    {value: 'front-nearside-car.svg', label: "Car - Nearside Front Quarter"},
    {value: 'front-offside-car.svg', label: "Car - Offside Front Quarter"},
    {value: 'nearside-car.svg', label: "Car - Nearside"},
    {value: 'offside-car.svg', label: "Car - Offside"},
    {value: 'rear-car.svg', label: "Car - Rear"},
    {value: 'rear-nearside-car.svg', label: "Car - Nearside Rear Quarter"},
    {value: 'rear-offside-car.svg', label: "Car - Offside Rear Quarter"},
  ];
  private originalSection: UInspectSectionDTO = {};
  private originalQuestion: UInspectQuestionDTO = {};
  rowChanged: {} = {};
  showDeletedOptions = false;
  showDeletedSections = false;
  private uInspectEvent: Subscription;

  documentCategories: any[];

  constructor(
    private formatService: UInspectFormatService,
    private helperService: HelpersService,
    private route: ActivatedRoute,
    private eventService: EventService,
    private sectionService: UInspectSectionService,
    private questionOptionService: UInspectQuestionOptionService,
    private questionService: UInspectQuestionService,
    public url: UInspectURLService
  ) {

    this.formatId = this.route.snapshot.params.id;

  }

  async ngOnInit() {

    this.getFormat(this.formatId).then(() => { });
    this.getStatuses();
    this.getDocumentCategories();
    this.getVehicleTypes();
    this.getQuestionTypes();
    this.getMediaTypes();
    this.getSectionTypes();
    this.getMediaCounts();
    this.getInteriorOptions();
    this.initSectionForm();
    this.initSectionQuestionForm();
    this.initEvents();
  }

  initSectionForm() {

    this.sectionForm = new UntypedFormGroup({
      id: new UntypedFormControl(),
      uInspectFormatId: new UntypedFormControl(),
      title: new UntypedFormControl(),
      explanation: new UntypedFormControl(),
      sectionType: new UntypedFormControl(),
      mediaType: new UntypedFormControl(),
      placeholderURL: new UntypedFormControl(),
      minMedia: new UntypedFormControl(),
      maxMedia: new UntypedFormControl(),
      nextSectionId: new UntypedFormControl(),
      isInterior: new UntypedFormControl(),
      disabled: new UntypedFormControl(),
      statusId: new UntypedFormControl(),
      internalLabel: new UntypedFormControl(),
      mapToDocumentCategoryId: new UntypedFormControl()
    });

  }

  initSectionQuestionForm() {

    this.sectionQuestionForm = new UntypedFormGroup({
      id: new UntypedFormControl(),
      uInspectSectionId: new UntypedFormControl(),
      questionType: new UntypedFormControl(),
      questionText: new UntypedFormControl(),
      questionMask: new UntypedFormControl(),
      disabled: new UntypedFormControl(),
      statusId: new UntypedFormControl(),
      mappedTo: new UntypedFormControl(null),
    });

  }

  getStatuses() {

    const statuses = this.helperService.getNamesAndValues(StatusEnum);

    this.statusOptions = statuses.map(status => {
      this.statusName[status.value] = status.name;
      return {label: status.name, value: status.value};
    });
  }

  getDocumentCategories() {
    this.formatService.getDocumentCategories().then(res => {
      this.documentCategories = res.results.map(x => {
        return { label: x.documentCategoryName, value: x.id }
      });

      // prepend null category?
      this.documentCategories.unshift({ label: '[None]', value: null });
    })
  }

  getInteriorOptions() {

    this.isInteriorOptions = [
      {value: 0, label: "Exterior"},
      {value: 1, label: "Interior"},
    ];
  }

  getVehicleTypes() {

    const vehicleTypes = this.helperService.getNamesAndValues(VehicleTypeEnum);

    this.vehicleTypeOptions = vehicleTypes.map(vehicleType => {
      this.vehicleTypeName[vehicleType.value] = vehicleType.name;
      return {label: vehicleType.name, value: vehicleType.value};
    });
  }

  getSectionTypes() {
    const sectionTypes = this.helperService.getNamesAndValues(UInspectSectionTypeEnum);

    this.sectionTypeOptions = sectionTypes.map(sectionType => {
      this.sectionTypeName[sectionType.value] = sectionType.name;
      return {label: sectionType.name, value: sectionType.value};
    });

  }

  getQuestionTypes() {

    const questionTypes = this.helperService.getNamesAndValues(UInspectQuestionTypeEnum);

    this.questionTypeOptions = questionTypes.map(questionType => {
      this.questionTypeName[questionType.value] = questionType.name;
      return {label: questionType.name, value: questionType.value};
    });

  }

  getMediaTypes() {

    const mediaTypes = this.helperService.getNamesAndValues(MediaTypeEnum);

    this.mediaTypeOptions = mediaTypes.map(mediaType => {
      this.mediaTypeName[mediaType.value] = mediaType.name;
      return {label: mediaType.name, value: mediaType.value};
    });

    this.mediaTypeOptions.push({value: 0, label: "== No Media =="});
  }

  get sf() {

    return this.sectionForm.value;

  }

  get sqf() {

    return this.sectionQuestionForm.value;

  }

  getFormat(id: number) {

    this.loadingFormat = true;

    return this.formatService.get(id, {component: 'format-view'}).then((result) => {
      this.loadingFormat = false;
      if (result.isValid) {
        this.format = result.dto;

        this.format.sections = this.format.sections.sort((a, b) => {
          return a.id - b.id;
        });

        this.sectionOptions = this.format.sections.map((x) => {
          return {label: x.id.toString() + " - " + x.title, value: x.id};
        });

        this.nextSectionOptions = Object.assign(this.sectionOptions);
        this.nextSectionOptions.push({value: null, label: "== NO NEXT SECTION =="});

        this.questionNextSectionOptions = Object.assign(this.nextSectionOptions);
        this.questionNextSectionOptions.push({value: 0, label: "== DEFAULT =="});
      }
    });
  }

  private getMediaCounts() {

    for (let cnt = 0; cnt <= 50; cnt++) {
      this.mediaCountOptions.push({label: cnt, value: cnt});
    }

  }

  filteredSections(sections: UInspectSectionDTO[], selectedSectionId) {

    return sections.filter(
      x => (
          (x.id === selectedSectionId || (selectedSectionId === null && x.statusId === StatusEnum.Active && x.disabled !== true)))
        || this.showDeletedSections
    ).sort((a, b) =>
      (a.id === this.format.startSectionId ? 0 : 1) -  (b.id === this.format.startSectionId ? 0 : 1));
  }

  addSection() {

    this.showEditSection = true;
    this.sectionForm.reset();
    this.sectionForm.patchValue({
      uInspectFormatId: this.formatId,
      disabled: false,
      statusId: StatusEnum.Active
    });
  }

  selectSection(id: number, toggle = true) {

    if (toggle === true) {
      this.selectedSectionId = this.selectedSectionId == null ? id : null;
    }

    if (this.showEditSection == false) {

      this.showEditSection = true;
      this.originalQuestion = {};

      this.selectedSection = this.format.sections.find(x => x.id === this.selectedSectionId);

      this.sectionForm.patchValue(this.selectedSection);

      this.originalSection = this.sf;

      if (this.selectedSection.questions.length === 0) {

        this.sectionQuestionForm.reset();
        this.sectionQuestionForm.patchValue({
          uInspectSectionId: this.selectedSection.id,
          disabled: 0,
          statusId: StatusEnum.Active
        });

        this.selectedQuestion = {
          questionText: "Question Text",
          options: [],
        };

      } else {

        this.selectedQuestion = deepClone(this.selectedSection.questions[0]);

        this.saveOriginalQuestionOptions();

        this.sectionQuestionForm.patchValue(this.selectedQuestion);
        this.originalQuestion = this.sqf;

        this.selectedQuestion.options.sort((a, b) => {
          return a.sequence - b.sequence;
        });
      }
    } else {

      this.hideEditSection();
    }
  }

  saveOriginalQuestionOptions() {
    this.originalQuestionOptions = deepClone(this.selectedQuestion.options);
  }

  async saveSection() {

    this.savingSection = true;

    if (this.sf.id) {

      await this.sectionService.patch(this.selectedSectionId, this.sf, this.originalSection);
    } else {
      await this.sectionService.post(this.sectionForm.value).then((x) => {
        this.selectedSectionId = x.dto.id;
      });
    }

    await this.getFormat(this.formatId);
    await this.calculateVisualise();
    this.originalSection = this.sf;
    this.savingSection = false;
  }

  hideEditSection() {

    this.showEditSection = false;
    this.selectedSectionId = null;
    this.selectedSection = null;
    this.selectedQuestion = null;

  }

  nextSectionName(nextSectionId: number) {

    return this.format.sections.find(x => x.id === nextSectionId).title;
  }

  addRow() {

    const maxSequence = (this.selectedQuestion?.options.length > 0) ? Math.max(...this.selectedQuestion.options.map(o => o.sequence)) : 0;

    this.selectedQuestion.options.push({
      id: null,
      sequence: maxSequence + 1,
      statusId: StatusEnum.Active,
      nextSectionId: 0,
    });
  }

  async deleteOption(option: UInspectQuestionOptionDTO) {

    if (option.id) {

      this.selectedQuestion.options = this.selectedQuestion.options.filter(x => x.id !== option.id);

      await this.questionOptionService.patch(option.id, {sequence: 999, statusId: StatusEnum.Deleted});

    } else {

      this.selectedQuestion.options = this.selectedQuestion.options.filter(x => x.sequence !== option.sequence);
    }
  }

  async saveOptionRow(option: UInspectQuestionOptionDTO) {

    if (option.id) {

      await this.questionOptionService.patch(option.id,
        this.selectedQuestion.options.find(x => x.id === option.id),
        this.originalQuestionOptions.find(x => x.id === option.id)
      );

    } else {

      const newOption: UInspectQuestionOptionDTO = Object.assign(this.selectedQuestion.options.find(x => x.sequence === option.sequence));

      newOption.uInspectQuestionId = this.selectedQuestion.id;

      await this.questionOptionService.add(newOption).then((x) => {

        if (x.isValid) {
          option.id = x.dto.id;
          this.saveOriginalQuestionOptions();
        }

      });
    }

    this.rowChanged[option.id] = false;

  }

  sortedOptions(selectedQuestion: UInspectQuestionDTO) {

    if (selectedQuestion?.options.length > 0) {

      return selectedQuestion.options.sort((a, b) => a.sequence - b.sequence);

    }

    return [];
  }

  async saveQuestionSection() {

    this.savingQuestion = true;

    if (this.selectedQuestion?.id) {

      await this.questionService.patch(this.selectedQuestion.id, this.sectionQuestionForm.value, this.originalQuestion);

      await this.getFormat(this.formatId);

    } else {

      console.log("Saving new question: ", this.sectionQuestionForm.value)
      await this.questionService.post(this.sectionQuestionForm.value);
    }

    this.originalQuestion = this.sqf;

    this.savingQuestion = false;
  }

  showAddSection() {

  }

  toggleVisualise() {
    this.visualise = !this.visualise;
    if (this.visualise) {
      this.calculateVisualise();
    }
  }

  calculateVisualise() {

    const nodeBackgroundColor = "#007bff";
    const qNodeBackgroundColor = "#00aeff";
    const nodeTextColor = "#ffffff";
    const nodeIconColor = "#ffffff";

    this.visualiseNodes = [];
    this.visualiseLinks = [];

    const icons = {
      0: "fa fa-file",
      1: "fa fa-camera",
      2: "fa fa-question",
    };

    this.format.sections
      .filter(x => !x.disabled)
      .forEach((section) => {

        let nodeIcon = icons[section.sectionType];

        if (section.sectionType === UInspectSectionTypeEnum.Media) {
          if (section.mediaType === MediaTypeEnum.Video) {
            nodeIcon = "fa fa-video";
          }
          if (section.mediaType === MediaTypeEnum.Audio) {
            nodeIcon = "fa fa-music";
          }
        }

        if (section.nextSectionId == 0 && section.sectionType === UInspectSectionTypeEnum.Page) {
          nodeIcon = "fa fa-flag-checkered";
        }

        let id = null;
        let hideDefaultRoute = false;

        if (section.questions.length > 0) {

          const question = section.questions[0];

          id = section.id.toString();
          this.visualiseNodes.push({
            label: question.questionText,
            icon: nodeIcon,
            data: {customColor: qNodeBackgroundColor, textColor: nodeTextColor},
            id
          });

          if (question.questionType === UInspectQuestionTypeEnum.Option) {

            question.options.filter(x => x.statusId === StatusEnum.Active).forEach((option) => {

              let linkTarget = null;

              if (option.nextSectionId && option.nextSectionId !== section.nextSectionId) {
                linkTarget = option.nextSectionId.toString();
              } else {
                linkTarget = section.nextSectionId.toString();
              }

              if (this.simpleVisual === false || (option.nextSectionId !== 0 && option.nextSectionId != section.nextSectionId)) {

                if (this.simpleVisual === false) {
                  hideDefaultRoute = true;
                }

                this.visualiseLinks.push({
                  id: "option" + question.id + option.id,
                  label: option.optionLabel,
                  source: section.id.toString(),
                  target: linkTarget,
                });
              }
            });
          }
        } else {
          id = section.id.toString();
          this.visualiseNodes.push({
            label: section.internalLabel || section.title,
            data: {customColor: nodeBackgroundColor, textColor: nodeTextColor},
            id,
            icon: nodeIcon
          });
        }

        const nextSectionId = section.nextSectionId;

        if (nextSectionId != null && nextSectionId !== 0 && !hideDefaultRoute) {

          this.visualiseLinks.push({
            id: "link" + id + nextSectionId,
            label: "Next",
            source: id.toString(),
            target: nextSectionId.toString(),
          });
        }
      });
    this.visualiseCenter$.next(true);
  }

  sfChanged() {
    return compare(this.sf, this.originalSection).length > 0;
  }

  sqfChanged() {
    return compare(this.sqf, this.originalQuestion).length > 0;
  }

  rowChangeEvent(id: number) {
    this.rowChanged[id] = true;
  }

  showOptions(options: UInspectQuestionOptionDTO[] | undefined) {

    if (options === undefined) {
      return [];
    }

    return options.filter(x => x.statusId === StatusEnum.Active || this.showDeletedOptions);

  }

  editFormat() {

    this.eventService.AdminEvent.emit({eventType: UInspectEventTypeEnum.ShowEditFormatModal, object: {format: this.format}});


  }

  initEvents() {

    this.uInspectEvent = this.eventService.AdminEvent.subscribe(async (data) => {

      if (data.eventType === UInspectEventTypeEnum.FormatUpdated) {

        await this.getFormat(this.formatId);
      }
    });
  }

  visualNodeClicked(node: any) {

  }
}
