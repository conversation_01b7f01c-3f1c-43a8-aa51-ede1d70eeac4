.title-prefix {
  max-width: 120px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.column-gap {
  column-gap: 0.875rem;
}

.placeholderURL {
  width: 60px;
  height: 50px;
}

.input-prepend-width {
  width: 120px;
}

.input-postpend-width {
  width: 250px;
}

td.md-form.narrow-input .form-control {

  margin-bottom: 0px !important;

}

tr.unsaved td {
  background-color: #fee !important;
}

.media-icon {
  width: 20px;
  display: inline-block;
  text-align: center;
}

#maskTestControl.ng-invalid {
  outline: 2px solid #c00;
  outline-offset: -1px
}

#maskTestControl.ng-invalid + label {
  color: #c00 !important;
}

#maskTestControl.ng-valid {
  outline: 2px solid var(--successColour);
  outline-offset: -1px
}

#maskTestControl.ng-valid + label {
  color: forestgreen !important;
}

.table-striped .deletedOption {
  background-color: #fee !important;
}

.format-status {
  color: #fff;
}

.format-status-1 {
  background-color: forestgreen
}

.format-status-2 {
  background-color: #c00
}

.format-status-3 {
  background-color: #c00
}

.start-button {
  background-color: forestgreen;
  color: #fff;
}

.end-button {
  background-color: #c00;
  color: #fff;
}
