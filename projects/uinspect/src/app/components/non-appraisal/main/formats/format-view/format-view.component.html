<div class="d-flex flex-wrap">
  <div class="flex-grow-1">
    <h1 class="page-header">{{ format?.title }}</h1>
  </div>
  <div class="flex-shrink-1">
    <div class="btn btn-xs btn-outline-primary mr-2" (click)="url.formatList()"><i class="fa fa-caret-left"></i> Format
      List
    </div>
    <a class="btn btn-xs btn-outline-primary mr-2" href="#" [routerLink]="url.startTestInspection(this.formatId, true)" target="_new"><i class="fa fa-flask"></i>
      Test Inspection
    </a>
    <div class="btn btn-xs btn-outline-primary mr-2" (click)="toggleVisualise()"><i class="fas fa-network-wired"></i>
      Visualise
    </div>
    <div class="btn btn-xs btn-primary" (click)="editFormat()"><i class="fa fa-edit"></i> Edit</div>
  </div>
</div>

<div *ngIf="!loadingFormat; else showLoadingFormat">

  <div class="widget padding mt-2 mb-2">
    <div class="d-flex flex-wrap">
      <div class="flex-grow-1" style="font-size: 0.875rem;">Description: <span style="font-weight: 600">{{ format.description }}</span></div>
      <div class="flex-shrink-1">
        <div class="mr-1">
          <div (click)="editFormat()" class="btn btn-xs format-status"
               [class]="'format-status-' + format.statusId">{{ statusName[format.statusId] }}</div>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="visualise">

    <div class="d-flex flex-wrap column-gap">
      <div class="flex-grow-1">
        <h2 class="widget-h2">Visualise</h2>
      </div>
      <div class="flex-shrink-1">
        <div class="switch blue-white-switch">
          <span class="switch-label" style="font-size: 0.8rem; margin-right: 0.5px;">Simple Mode &nbsp;</span>
          <label>
            <input type="checkbox" [(ngModel)]="simpleVisual" (ngModelChange)="calculateVisualise()">
            <span class="lever"></span>
          </label>
        </div>
      </div>
    </div>

    <div class="widget mb-2">

      <div style="width: 100%; height: 400px;">

        <ngx-graph
          class="stats-container"
          [links]="visualiseLinks"
          [autoZoom]="true"
          [autoCenter]="true"
          [draggingEnabled]="false"
          [center$]="visualiseCenter$"
          [nodes]="visualiseNodes">
          <ng-template #defsTemplate>
            <svg:marker id="arrow" viewBox="0 -5 10 10" refX="8" refY="0" markerWidth="4" markerHeight="4"
                        orient="auto">
              <svg:path d="M0,-5L10,0L0,5" class="arrow-head"/>
            </svg:marker>
          </ng-template>

          <ng-template #clusterTemplate let-cluster>
            <svg:g class="node cluster">
              <svg:rect rx="5" ry="5" [attr.width]="cluster.dimension.width" [attr.height]="cluster.dimension.height"
                        [attr.fill]="cluster.data.color"/>
            </svg:g>
          </ng-template>

          <ng-template #nodeTemplate let-node>
            <svg:g class="node" style="cursor: pointer" (click)="visualNodeClicked(node)">
              <svg:rect [attr.width]="node.dimension.width + 15" [attr.height]="node.dimension.height"
                        [attr.rx]="3"
                        [attr.fill]="node.data.customColor || node.data.color"/>
              <svg:foreignObject width="22" height="20" [attr.x]="5" [attr.y]="6"
              >
                <xhtml:div class="cardContainer" xmlns="http://www.w3.org/1999/xhtml"
                           style="text-align: center; font-size: 12px">
                  <i [class]="node.icon" [style]="{'color': node.data.textColor || '#000000'}"></i>
                </xhtml:div>
              </svg:foreignObject>
              <svg:text alignment-baseline="central" [attr.x]="27"
                        style="font-size: 12px;"
                        [attr.fill]="node.data.textColor || '#000000'"
                        [attr.y]="node.dimension.height / 2">
                {{node.label}}
              </svg:text>
            </svg:g>
          </ng-template>

          <ng-template #linkTemplate let-link>
            <svg:g class="edge">
              <svg:path class="line" stroke-width="2" marker-end="url(#arrow)">
              </svg:path>
              <svg:text class="edge-label" text-anchor="middle">
                <textPath class="text-path" [attr.href]="'#' + link.id"
                          [style.dominant-baseline]="link.dominantBaseline"
                          startOffset="50%">
                  {{link.label}}
                </textPath>
              </svg:text>
            </svg:g>
          </ng-template>

        </ngx-graph>
      </div>
    </div>
  </div>

  <div class="d-flex flex-wrap">
    <div class="flex-grow-1">
      <h2 class="widget-h2">Sections</h2>
    </div>

    <div class="flex-shrink-1">
      <div class="switch blue-white-switch">
        <span class="switch-label" style="font-size: 0.8rem;">Show Disabled&nbsp;</span>
        <label class="ml-2">
          <input type="checkbox" [(ngModel)]="showDeletedSections">
          <span class="lever"></span>
        </label>
      </div>
    </div>
    <div class="flex-shrink-1">
      <span class="btn btn-xs btn-primary" (click)="addSection()"><i class="fa fa-plus-circle"></i> Section</span>
    </div>
  </div>

  <div class="widget padding">

    <table class="table table-striped w-100 table-narrow">
      <thead>
      <tr>
        <th>ID</th>
        <th></th>
        <th>Title/Label</th>
        <th>Type</th>
        <th>Min/Max Media</th>
        <th>Ext/Int</th>
        <th>Next ID</th>
        <th>Enabled</th>
        <th>Status</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let section of filteredSections(format.sections, selectedSectionId)"
          (click)="selectSection(section.id)">
        <td>{{ section.id }}</td>
        <td>
          <span *ngIf="section.id == format.startSectionId" class="btn btn-xs start-button">Start <i
            class="fa fa-caret-right"></i></span>
        </td>
        <td>
          <div class="table-line-1">{{ section.title }}</div>
          <div class="table-line-2">{{ section.internalLabel }}</div>
        </td>
        <td>
          <span class="media-icon">
          <i class="fa fa-camera" *ngIf="section.mediaType == MediaTypeEnum.Image"></i>
          <i class="fa fa-file" *ngIf="section.mediaType == MediaTypeEnum.Document"></i>
          <i class="fa fa-video" *ngIf="section.mediaType == MediaTypeEnum.Video"></i>
          <i class="fa fa-music" *ngIf="section.mediaType == MediaTypeEnum.Audio"></i>
          <i class="fa fa-question-circle" *ngIf="section.sectionType == SectionTypeEnum.Question"></i>
          </span>
          {{ sectionTypeName[section.sectionType] }}
        </td>
        <td>
          <span *ngIf="section.mediaType != null && section.mediaType != 0 &&
            (section.mediaType == MediaTypeEnum.Image || section.mediaType == MediaTypeEnum.Video)">
            {{ section.minMedia }} - {{ section.maxMedia }}
          </span>
        </td>
        <td>{{ section.isInterior ? 'Interior' : 'Exterior' }}</td>
        <td (click)="selectSection(section.nextSectionId)">
          <i class="fa fa-arrow-right"></i> &nbsp;
          <span *ngIf="section.nextSectionId > 0"><b>{{ section.nextSectionId }}</b>
            - <span class="title-prefix"
                    [mdbTooltip]="nextSectionName(section.nextSectionId)"> {{ nextSectionName(section.nextSectionId) }}</span>
          </span>

          <span *ngIf="section.nextSectionId == 0">
            <span *ngIf="section.sectionType === sectionType.Question && section.questions?.length > 0
              && section.questions[0].options?.length > 0;">
              <span *ngFor="let o of section.questions[0].options">
                <span *ngIf="o.nextSectionId > 0" style="display: block">
                  Answer: {{ o.optionLabel }} <b>{{ o.nextSectionId }}</b> - <span class="title-prefix"
                    [mdbTooltip]="nextSectionName(o.nextSectionId)"> {{ nextSectionName(o.nextSectionId) }}</span>
                </span>
              </span>
            </span>

            <span class="btn btn-xs end-button">
              <i class="fas fa-flag-checkered"></i> Ends
            </span>
          </span>
        </td>
        <td>
          <span *ngIf="section.disabled"><i class="fa fa-times text-danger"></i></span>
          <span *ngIf="!section.disabled"><i class="fa fa-check text-success"></i></span>
        </td>
        <td>{{ statusName[section.statusId] }}</td>
      </tr>
      </tbody>
    </table>

  </div>

</div>

<div *ngIf="showEditSection" class="mt-2 mb-2">

  <div class="flex-wrap d-flex">
    <div class="flex-grow-1">
      <h2 class="widget-h2">Section: {{ sf.title }}</h2>
    </div>
    <div class="flex-shrink-1">
      <div class="btn btn-xs btn-outline-primary" (click)="hideEditSection()"><i class="fa fa-caret-left"></i> Back to
        Sections
      </div>
    </div>
  </div>

  <div class="widget padding">

    <form [formGroup]="sectionForm">

      <div class="d-flex flex-wrap column-gap pt-2">

        <div class="flex-shrink-1">

          <div class="narrow-select mb-3">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text input-prepend-width">
                  Section Type
                </div>
              </div>
              <div>
                <mdb-select [outline]="true"
                            class="input-postpend-width"
                            formControlName="sectionType" [options]="sectionTypeOptions"></mdb-select>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-shrink-1">

          <div class="md-form mt-3 narrow-input">
            <input mdbInput name="title" id="title" class="form-control float-label" formControlName="title">
            <label for="title">Section Title</label>
          </div>
        </div>

        <div class="flex-shrink-1">

          <div class="md-form mt-3 narrow-input">
            <input mdbInput name="internalLabel" id="internalLabel" class="form-control float-label" formControlName="internalLabel">
            <label for="internalLabel">Section Label (Internal)</label>
          </div>
        </div>

      </div>

      <div class="flex-wrap d-flex column-gap">
        <div class="flex-shrink-1">

          <div class="narrow-select mb-3">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text input-prepend-width">
                  Location
                </div>
              </div>
              <div>
                <mdb-select
                  [style]="{'display':'inline-block;'}"
                  class="input-postpend-width"
                  formControlName="isInterior" [options]="isInteriorOptions"></mdb-select>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-grow-1">
          <div class="md-form mt-3 narrow-input">
            <textarea mdbInput name="explanation" id="explanation" formControlName="explanation"
                      class="form-control float-label"></textarea>
            <label for="title">Section Explanation</label>
          </div>
        </div>
      </div>

      <div class="d-flex flex-wrap column-gap mb-3" *ngIf="sf.sectionType == sectionType.Media">
        <div class="flex-shrink-1">

          <div class="narrow-select">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text input-prepend-width">
                  Media Type
                </div>
              </div>
              <div>
                <mdb-select [outline]="true"
                            [style]="{'display':'inline-block;'}"
                            class="input-postpend-width"
                            formControlName="mediaType" [options]="mediaTypeOptions"></mdb-select>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-shrink-1" *ngIf="sf.mediaType != 0">
          <div class="narrow-select">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text" style="width: 100px;">
                  Min Media
                </div>
              </div>
              <div>
                <mdb-select [outline]="true"
                            [style]="{'display':'inline-block;'}"
                            style="width: 80px"
                            formControlName="minMedia" [options]="mediaCountOptions"></mdb-select>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-shrink-1" *ngIf="sf.mediaType != 0">

          <div class="narrow-select">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text" style="width: 100px;">
                  Max Media
                </div>
              </div>
              <div>
                <mdb-select [outline]="true"
                            [style]="{'display':'inline-block;'}"
                            style="width: 80px"
                            formControlName="maxMedia" [options]="mediaCountOptions"></mdb-select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="d-flex flex-wrap column-gap w-100">



        <div class="flex-shrink-1">
          <div class="narrow-select mb-3">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text input-prepend-width">
                  Overlay Image
                </div>
              </div>
              <div>
                <mdb-select [outline]="true"
                            class="input-postpend-width"
                            [style]="{'display':'inline-block;'}"
                            formControlName="placeholderURL" [options]="placeholders"></mdb-select>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-shrink-1">
          <img src="assets/images/{{ sf.placeholderURL }}" class="placeholderURL" *ngIf="sf.placeholderURL && sf.placeholderURL != ''"/>
        </div>

      </div>

      <div class="d-flex flex-wrap column-gap">

        <div class="flex-shrink-1">
          <div class="narrow-select mb-3">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text input-prepend-width">
                  Next Section
                </div>
              </div>
              <div>
                <mdb-select [outline]="true"
                            class="input-postpend-width"
                            [style]="{'display':'inline-block;'}"
                            formControlName="nextSectionId" [options]="nextSectionOptions"></mdb-select>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-shrink-1" *ngIf="sf.mediaType == 1">

          <div class="narrow-select">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text">
                  Map to document type
                </div>
              </div>
              <div>
                <mdb-select [outline]="true"
                            [style]="{'display':'inline-block;'}"
                            style="width: 160px"
                            formControlName="mapToDocumentCategoryId" [options]="documentCategories"></mdb-select>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-shrink-1">

          <div class="switch blue-white-switch mt-2">
            <span class="switch-label">Disabled &nbsp;</span>
            <label>
              <input type="checkbox" formControlName="disabled">
              <span class="lever"></span>
            </label>
          </div>
        </div>

        <div class="flex-shrink-1">
          <div class="narrow-select">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text">
                  Status
                </div>
              </div>
              <div>
                <mdb-select [outline]="true"
                            [style]="{'display':'inline-block;'}"
                            style="width: 160px"
                            formControlName="statusId" [options]="statusOptions"></mdb-select>
              </div>
            </div>
          </div>
        </div>

        <div class="flex-grow-1" *ngIf="sfChanged()">
          <div class="text-right">
            <div class="btn btn-sm btn-outline-primary mr-2" (click)="hideEditSection()">Cancel</div>
            <div class="btn btn-sm btn-primary" (click)="saveSection()">
              <span *ngIf="!savingSection">
                <span *ngIf="sf.id">Save</span>
                <span *ngIf="!sf.id">Add</span>
              </span>
              <span *ngIf="savingSection"><i class="fa fa-spin fa-spinner"></i></span>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

<div *ngIf="selectedSection != null && sf.sectionType == sectionType.Question">

  <div class="flex-wrap d-flex">
    <div class="flex-grow-1">
      <h2 class="widget-h2">Question</h2>
    </div>
  </div>

  <form [formGroup]="sectionQuestionForm">

    <div class="widget padding">
      <div class="pt-1">

        <div class="md-form narrow-input">
          <input formControlName="questionText" id="questionText" class="form-control float-label"
                 mdbInput>
          <label for="questionText">Question Text</label>
        </div>

        <div class="d-flex flex-wrap column-gap">

          <div class="flex-shrink-1">
            <div class="narrow-select">
              <div class="input-group">
                <div class="input-group-prepend">
                  <div class="input-group-text">
                    Question Type
                  </div>
                </div>
                <div>
                  <mdb-select [outline]="true"
                              [style]="{'display':'inline-block;'}"
                              style="width: 160px"
                              formControlName="questionType" [options]="questionTypeOptions"></mdb-select>
                </div>
              </div>
            </div>
          </div>

          <div class="flex-shrink-1" *ngIf="selectedQuestion.questionType != QuestionTypeEnum.Option">
            <div class="md-form mt-3 narrow-input">
              <input mdbInput name="questionMask" id="questionMask" class="form-control float-label"
                     formControlName="questionMask">
              <label for="questionMask">Input Mask</label>
            </div>
          </div>

          <div class="flex-shrink-1">
            <div class="narrow-select">
              <div class="input-group">
                <div class="input-group-prepend">
                  <div class="input-group-text">
                    <i class="fas fa-exchange-alt"></i>&nbsp; Maps to Field
                  </div>
                </div>
                <div>
                  <mdb-select [outline]="true"
                              [style]="{'display':'inline-block;'}"
                              style="width: 250px"
                              formControlName="mappedTo" [options]="mapsToOptions"></mdb-select>
                </div>
              </div>
            </div>
          </div>

          <div class="flex-shrink-1" *ngIf="selectedQuestion.questionType != QuestionTypeEnum.Option">
            <div class="md-form mt-3 narrow-input">
              <input mdbInput name="maskTest" id="maskTestControl" class="form-control float-label"
                     [pattern]="sqf.questionMask">
              <label for="maskTestControl">Mask Test</label>
            </div>
          </div>
          <div class="flex-grow-1">
            <div class="text-right" *ngIf="sqfChanged()">
              <div class="btn btn-sm btn-outline-primary mr-2" (click)="hideEditSection()">Cancel</div>
              <div class="btn btn-sm btn-primary" (click)="saveQuestionSection()">
                <span *ngIf="!savingQuestion">Save</span>
                <span *ngIf="savingQuestion"><i class="fa fa-spin fa-spinner"></i></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </form>

  <div *ngIf="sqf.questionType === QuestionTypeEnum.Option" class="pb-3">

    <div class="flex-wrap d-flex mt-2">
      <div class="flex-grow-1">
        <h2 class="widget-h2">Options</h2>
      </div>
      <div class="flex-shrink-1">

        <div class="switch blue-white-switch">
          <span class="switch-label" style="font-size: 0.8rem;">Show Deleted&nbsp;</span>
          <label class="ml-2">
            <input type="checkbox" [(ngModel)]="showDeletedOptions">
            <span class="lever"></span>
          </label>
        </div>
      </div>

      <div class="flex-shrink-1">
        <div class="btn btn-xs btn-primary" (click)="addRow()"><i class="fa fa-plus-circle"></i> Option</div>
      </div>

    </div>

    <div class="widget padding">

      <table class="w-100 table-compressed table table-striped table-narrow">
        <thead>
        <tr>
          <th style="width: 20px;"></th>
          <th style="width: 55px;">Seq</th>
          <th>Label</th>
          <th style="width: 120px">Value</th>
          <th style="width: 120px" *ngIf="sqf.mappedTo != null">MappedValue</th>
          <th>Next Section</th>
          <th>Status</th>
          <th style="width: 40px"></th>
        </tr>

        </thead>
        <tbody>
        <tr *ngFor="let option of showOptions(selectedQuestion?.options)" [class.unsaved]="option.id == null"
            [class.deletedOption]="option.statusId == statusEnum.Deleted"
        >
          <td>
            <div class="pt-2"><i class="fa fa-trash-alt fa-15x" (click)="deleteOption(option)"></i></div>
          </td>
          <td class="md-form narrow-input">
            <input class="form-control" [id]="'sequence-' + option.id"
                   style="width:50px"
                   (ngModelChange)="rowChangeEvent(option.id)"
                   [(ngModel)]="option.sequence">
          </td>
          <td class="md-form narrow-input">
            <input class="form-control" [id]="'optionLabel-' + option.id"
                   (ngModelChange)="rowChangeEvent(option.id)"
                   [(ngModel)]="option.optionLabel">
          </td>
          <td class="md-form narrow-input">
            <input class="form-control" [id]="'optionValue-' + option.id"
                   (ngModelChange)="rowChangeEvent(option.id)"
                   [(ngModel)]="option.optionValue"></td>
          <td class="md-form narrow-input" *ngIf="sqf.mappedTo != null">
            <input class="form-control" [id]="'mappedToValue-' + option.id"
                   (ngModelChange)="rowChangeEvent(option.id)"
                   [(ngModel)]="option.mappedToValue"></td>
          <td class="narrow-select">
            <mdb-select [outline]="true"
                        [style]="{'display':'inline-block;'}"
                        [(ngModel)]="option.nextSectionId"
                        (ngModelChange)="rowChangeEvent(option.id)"
                        [options]="questionNextSectionOptions"></mdb-select>
          </td>
          <td>
            <div class="narrow-select">

              <mdb-select [outline]="true"
                          [style]="{'display':'inline-block;'}"
                          style="width: 160px"
                          [id]="'statusId-' + option.id"
                          [(ngModel)]="option.statusId"
                          (ngModelChange)="rowChangeEvent(option.id)"
                          [options]="statusOptions"></mdb-select>
            </div>
          </td>
          <td>
            <div class="pt-2" *ngIf="rowChanged[option.id]">
              <div class="btn btn-xs btn-primary" (click)="saveOptionRow(option)">Save</div>
            </div>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<ng-template #showLoadingFormat>

  <div class="mt-5 pb-5">
    <app-loading-spinner></app-loading-spinner>
  </div>

</ng-template>

<app-format-edit-dialog></app-format-edit-dialog>
