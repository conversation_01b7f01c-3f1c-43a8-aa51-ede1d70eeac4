import {Component, OnInit, ViewChild} from '@angular/core';
import {UInspectFormatDTO, User} from "../../../../../global/interfaces";
import {HelpersService, UserService} from "../../../../../global/services";
import {UInspectFormatService, UInspectURLService} from "../../../../../services/index";
import {LeadEventTypeEnum, StatusEnum, UInspectEventTypeEnum, VehicleTypeEnum} from "../../../../../global/enums";
import {ModalDirective} from "ng-uikit-pro-standard";
import {UntypedFormBuilder, UntypedFormControl, Validators} from "@angular/forms";
import {EventService} from "../../../../../services/event.service";
import {Subscription} from "rxjs";

@Component({
  templateUrl: './format-list.component.html',
  styleUrls: ['./format-list.component.scss'],
})


export class FormatListComponent implements OnInit {

  @ViewChild('formatModal') formatModal: ModalDirective;

  loadingFormats: boolean;
  formats: UInspectFormatDTO[] = [];
  statusOptions: { label: string; value: number }[];
  vehicleTypeOptions: { label: string; value: number }[];
  statusName: {} = {};
  vehicleTypeName: {} = {};
  isEdit: any;
  formatForm: any;
  private previousFormat: UInspectFormatDTO;
  private currentUser: User;
  activeOnly = true;
  private uInspectEvent: Subscription;

  constructor(
    private formatService: UInspectFormatService,
    private helperService: HelpersService,
    private fb: UntypedFormBuilder,
    public url: UInspectURLService,
    public userService: UserService,
    public eventService: EventService
  ) {

  }

  async ngOnInit() {

    this.getCurrentUser();
    this.getFormats();
    this.getStatuses();
    this.getVehicleTypes();
    this.initForm();
    this.initEvents();
  }

  initEvents() {

    this.uInspectEvent = this.eventService.AdminEvent.subscribe((data) => {

      if (data.eventType === UInspectEventTypeEnum.FormatUpdated) {
        this.getFormats();
      }
    });
  }

  getCurrentUser() {

    this.userService.loadCurrentUser().then(() => {
      this.currentUser = this.userService.CurrentUser;
    });

  }

  initForm() {

    this.formatForm = this.fb.group({
      id: new UntypedFormControl(""),
      customerId: new UntypedFormControl(""),
      title: new UntypedFormControl("", [Validators.required]),
      description: new UntypedFormControl("", [Validators.required]),
      disabled: new UntypedFormControl(false),
      statusId: new UntypedFormControl(StatusEnum.Active),
    });
  }

  getStatuses() {

    const statuses = this.helperService.getNamesAndValues(StatusEnum);

    this.statusOptions = statuses.map(status => {
      this.statusName[status.value] = status.name;
      return {label: status.name, value: status.value};
    });
  }

  getVehicleTypes() {

    const vehicleTypes = this.helperService.getNamesAndValues(VehicleTypeEnum);

    this.vehicleTypeOptions = vehicleTypes.map(vehicleType => {
      this.vehicleTypeName[vehicleType.value] = vehicleType.name;
      return {label: vehicleType.name, value: vehicleType.value};
    });
  }

  getFormats() {

    this.loadingFormats = true;

    this.formatService.search({component: 'format-list'}).then((result) => {
      this.loadingFormats = false;
      this.formats = result.results;
    });
  }

  get f() {

    if (this.formatForm) {
      return this.formatForm?.controls;
    }
    return null;
  }

  async saveFormat() {

    // Create format
    if (this.f.id.value == null) {

      await this.formatService.add(this.formatForm.value);

    } else {

      await this.formatService.patch(this.f.id.value, this.formatForm.value, this.previousFormat);
    }

    this.formatModal.hide();
    this.getFormats();
  }

  hideModal() {

    this.formatModal.hide();

  }

  addFormat() {

    this.formatForm.reset();

    this.formatForm.patchValue({
      customerId: this.currentUser.customerId,
      statusId: StatusEnum.Active,
      disabled: false
    });

    this.isEdit = false;
    // this.formatModal.show();

    const format = this.formatForm.value;

    this.eventService.AdminEvent.emit({eventType: UInspectEventTypeEnum.ShowEditFormatModal,
      object: { format }});
  }

  editFormat(e, format) {

    e.stopPropagation();

    this.eventService.AdminEvent.emit({eventType: UInspectEventTypeEnum.ShowEditFormatModal, object: {format}});
  }

  filterFormats(formats: UInspectFormatDTO[], activeOnly: boolean) {

    if (activeOnly === false) {
      return formats;
    }

    return formats.filter(x => x.disabled === false && x.statusId === StatusEnum.Active);
  }
}
