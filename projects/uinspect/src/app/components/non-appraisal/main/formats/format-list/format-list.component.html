<div class="d-flex flex-wrap">
  <div class="flex-grow-1">
    <h1 class="page-header">Appraisal Formats</h1>
  </div>
  <div class="flex-shrink-1">
    <div class="btn btn-xs btn-primary" (click)="addFormat()"><i class="fa fa-plus-circle"></i> Add</div>
  </div>
</div>
<div class="widget padding mt-2" *ngIf="!loadingFormats; else showLoadingFormats">

  <div class="text-right">
    <div class="switch blue-white-switch">
      <span class="switch-label switch-label-small">Active Only &nbsp;</span>
      <label class="mb-0">
        <input type="checkbox" [(ngModel)]="activeOnly">
        <span class="lever"></span>
      </label>
    </div>
  </div>

  <table class="table table-narrow table-hover table-striped">
    <thead>
    <tr>
      <th>Name</th>
      <th>Appraisals</th>
      <th>VehicleType</th>
      <th>Enabled</th>
      <th>Status</th>
      <th>Added</th>
      <th>&nbsp;</th>
      <th>&nbsp;</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let format of filterFormats(formats, activeOnly)" (click)="url.formatView(format.id)">
      <td><span class="table-line-1">{{ format.title }}</span></td>
      <td><span class="btn btn-xs btn-outline-primary">View</span></td>
      <td>
        {{ vehicleTypeName[format.vehicleTypeId] }}
      </td>
      <td><i class="fa" [class.fa-check]="!format.disabled" [class.fa-times]="format.disabled"></i></td>
      <td>{{ statusName[format.statusId] }}</td>
      <td>{{ format.added | date: 'dd/MM/YY'}}</td>
      <td class="shrink-cell"><span class="btn btn-xs btn-outline-primary"><i
        class="fa fa-list-ol"></i> Sections</span></td>
      <td class="shrink-cell" (click)="editFormat($event, format)"><span class="btn btn-xs btn-outline-primary"><i
        class="fa fa-edit"></i> Edit</span></td>
    </tr>
    </tbody>
  </table>
</div>

<ng-template #showLoadingFormats>

  <div class="mt-5 pb-5">
    <app-loading-spinner></app-loading-spinner>
  </div>

</ng-template>

<app-format-edit-dialog></app-format-edit-dialog>
