<!-- radio buttons for range type -->
<div class="d-flex flex-wrap mt-3">

  <div class="flex-shrink-1">

    <span style="font-size: 13px;">Date Range: </span>
    <div class="btn-group">
      <label *ngFor="let option of statOptions; let i=index;"
             class="btn btn-sm"
             style="margin-bottom: 0"
             mdbRadio="{{ option.value }}"
             [(ngModel)]="statRange"
             (click)="rangeTypeSelected(option)">
        {{option.label}}
      </label>
    </div>
  </div>

  <div class="flex-shrink-1 pr-2">

    <div *ngIf="statRequest.statRange == RangeType.Custom" class="d-flex custom-date">
      <div class="flex-shrink-1 custom-date-text ml-3">
        <label for="fromDate">Date Range: </label>
      </div>
      <div class="flex-shrink-1 md-form narrow-input" style="margin-bottom: 0 !important">
        <input type="date" id="fromDate" class="form-control"
               [ngModel]="statRequest.fromDate" [ngModelOptions]="{standalone: true}"
               (ngModelChange)="fromDateChanged($event)">
      </div>

      <div class="flex-shrink-1 custom-date-text pl-2">
        <label for="toDate">To:</label>
      </div>
      <div class="flex-shrink-1 md-form narrow-input" style="margin-bottom: 0 !important">
        <input type="date" id="toDate" class="form-control"
               [ngModel]="statRequest.toDate" [ngModelOptions]="{standalone: true}"
               (ngModelChange)="toDateChanged($event)">
      </div>
    </div>
  </div>
  <div>
    <span style="font-size: 13px;">Group By: </span>
    <div class="btn-group">
      <label *ngFor="let option of groupingOptions; let i=index;"
             style="margin-bottom: 0"
             class="btn btn-sm"
             mdbRadio="{{ option.value }}"
             [(ngModel)]="groupType"
             (click)="groupingTypeSelected(option)">
        {{option.label}}
      </label>
    </div>
  </div>
</div>


