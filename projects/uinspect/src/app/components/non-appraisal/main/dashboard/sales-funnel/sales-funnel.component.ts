import {Component, Input, OnInit} from '@angular/core';
import {UInspectService} from '../../../../../services';
import {LoggerService} from '../../../../../global/services';
import {Observable, Subscription} from 'rxjs';
import {UInspectStatRequest} from '../../../../../global/interfaces';
import {UInspectStatData} from '../../../../../global/interfaces';
import {format, parseISO} from 'date-fns';
import * as ChartDataLabels from 'chartjs-plugin-datalabels';
import {UInspectChartTypeEnum} from '../../../../../enums/uinspect-chart-type.enum';

@Component({
  selector: 'app-sales-funnel',
  templateUrl: './sales-funnel.component.html',
  styleUrls: ['./sales-funnel.component.scss', '../dashboard.component.scss']
})
export class SalesFunnelComponent implements OnInit {

  constructor(private uinspectService: UInspectService,
              private logService: LoggerService) {
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  private requestChangeSubscription: Subscription;
  @Input() requestChanged: Observable<UInspectStatRequest>;

  statData: UInspectStatData[];

  datasets: any;
  labels: any;
  //colors: any;
  public plugin: any;

  public chartOptions: any = {
    responsive: true,
    indexAxis: 'x',
    tooltips: {
      callbacks: {
        label: function(tooltipItem, data) {
          const label = data.datasets[tooltipItem.datasetIndex].label;
          const value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];

          return `${label}  [${value[1]}]`;
        }
      }
    },
    scales: {
      xAxes: [{
        gridLines: {
          offsetGridLines: true,
          display: true
        },
        ticks: {
          display: true
        },
      }],
      yAxes: [{
        gridLines: {
          offsetGridLines: false,
          display: false
        },
        ticks: {
          display: false
        },
        scaleLabel: {
          display: false
        }
      }]
    },
    plugins: {
      legend: {
        display: false,
      },
      datalabels: {
        formatter: (value: number) => {
          if (value[1] > 0)
            return value[1];
          else
            return null;
        },
        color: '#4f4f4f',
        labels: {
          title: {
            font: {
              size: '13',
            },
            anchor: 'right',
            align: 'center',
          },
        },
      },
    },
  };

  ngOnInit(){
    this.plugin = ChartDataLabels;
    this.requestChangeSubscription = this.requestChanged.subscribe((request) => this.loadStatData(request));
  }

  ngOnDestroy() {
    this.requestChangeSubscription.unsubscribe();
  }

  loadStatData(request: UInspectStatRequest) {
    console.log("Request: ", request);
    request.chartType = UInspectChartTypeEnum.Funnel;

    this.uinspectService.getFunnel(request).then(result => {
      this.statData = result;

      this.logger.info("Loaded stat data: ", result);

      // extract the date labels for the chart
      // .filter((v,i,a)=>a.indexOf(v)==i) // remove duplicates
      this.labels = this.statData.map(x => format(parseISO(x.date.toString()), 'd MMM yy'));

      this.logger.log("LABELS: ", this.labels);

      // convert data to chart format

      // extract requests
      let requests = this.statData.flatMap(x => x.requested
        .filter(y => y.formatName)
        .map(y => y.formatName)).filter((v,i,a)=>a.indexOf(v)==i);

      // extract opened
      let opened = this.statData.flatMap(x => x.opened
        .filter(y => y.formatName)
        .map(y => y.formatName)).filter((v,i,a)=>a.indexOf(v)==i);

      // extract completed
      let completed = this.statData.flatMap(x => x.completed
        .filter(y => y.formatName)
        .map(y => y.formatName)).filter((v,i,a)=>a.indexOf(v)==i);

      const rcd = requests.map(x => {
          return {
            label: `Requested (${x})`,
            data: this.statData.flatMap(y => {
              return y.requested.map(z => [-z.statCount, z.statCount])
            }),
            borderWidth: 1,
            lineTension: 0
          }
        }
      );

      const ocd = opened.map(x => {
          return {
            label: `Opened (${x})`,
            data: this.statData.flatMap(y => {
              return y.opened.map(z => [-z.statCount, z.statCount])
            }),
            borderWidth: 1,
            lineTension: 0
          }
        }
      );

      const ccd = completed.map(x => {
          return {
            label: `Completed (${x})`,
            data: this.statData.flatMap(y => {
              return y.completed.map(z => [-z.statCount, z.statCount])
            }),
            borderWidth: 1,
            lineTension: 0
          }
        }
      );

      this.datasets = rcd.concat(ocd, ccd);

      //console.log("RCD: ", rcd.reduce((a, b) => Math.max(...a.data, ...b.data))

      //let maxA = Math.max(...rcd.flatMap(x => x.data));
      //let maxB = Math.max(...ocd.flatMap(x => x.data));
      //let maxC = Math.max(...ccd.flatMap(x => x.data));

      //let max = Math.max(maxA, maxB, maxC);
      //this.chartOptions.scales.yAxes[0].ticks.suggestedMax = max + (max * 0.1);

      this.logger.debug("datasets: ", this.datasets);
    });
  }

  chartHovered($event: any) {

  }

  chartClicked($event: any) {

  }
}
