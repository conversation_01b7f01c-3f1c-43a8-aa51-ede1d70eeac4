<h1 class="page-header">Dashboard</h1>

<div class="mt-1">
  <app-stats-date-range (statRequestChanged)="onStatRequestChanged($event)"></app-stats-date-range>
</div>

<div class="row mt-3">
  <div class="col-md-6 mb-3">
    <div class="widget padding">
      <div class="chart-title">User Engagement</div>
      <app-engagement-over-time [requestChanged]="requestChangedSubject.asObservable()"></app-engagement-over-time>
    </div>
  </div>
  <div class="col-md-6 mb-3">
    <div class="widget padding">
      <div class="chart-title">Total Inspections</div>
      <app-total-inspections [requestChanged]="requestChangedSubject.asObservable()"></app-total-inspections>
    </div>
  </div>
  <div class="col-md-6 mb-3">
    <div class="widget padding">
      <div class="chart-title">Sales Funnel</div>
      <app-sales-funnel [requestChanged]="requestChangedSubject.asObservable()"></app-sales-funnel>
    </div>
  </div>
  <div class="col-md-6 mb-3">
    <div class="widget padding">
      <div class="chart-title">Inspections Map</div>
      <app-inspections-map [requestChanged]="requestChangedSubject.asObservable()"></app-inspections-map>
    </div>
  </div>
</div>
