import {Component, Input, OnDestroy, OnInit} from '@angular/core';
import {DomainData} from "../../../../../global/shared";
import {DataServiceInterface} from "../../../../../global/services/interfaces";
import {UInspectService} from '../../../../../services';
import {LoggerService} from "../../../../../global/services";
import {Observable, Subscription} from 'rxjs';
import {UInspectStatRequest} from '../../../../../global/interfaces';

@Component({
  selector: 'app-inspections-map',
  templateUrl: './inspections-map.component.html',
  styleUrls: ['./inspections-map.component.scss']
})
export class InspectionsMapComponent implements OnInit, OnDestroy {

  constructor(private data: DataServiceInterface,
              private uInspectService: UInspectService,
              private logService: LoggerService) {
    this.globals = data.globals;
    this.map = data.globals?.mapLocation;
  }

  logger = this.logService.taggedLogger(this.constructor?.name);

  private requestChangeSubscription: Subscription;
  @Input() requestChanged: Observable<UInspectStatRequest>;

  globals: DomainData;
  map: { lat?: number, lng?: number, label?: string };

  markers;

  ngOnInit(): void {
    this.requestChangeSubscription = this.requestChanged.subscribe((request) => this.loadInspectionMarkers(request));
  }

  ngOnDestroy() {
    if (this.requestChangeSubscription) {
      this.requestChangeSubscription.unsubscribe();
    }
  }

  loadInspectionMarkers(request: UInspectStatRequest) {
    const dto = {
      component: 'inspection-map',
      filters: {
        fromDate: request.fromDate,
        toDate: request.toDate
      }
    };

    this.uInspectService.search(dto).then((x) => {
      this.markers = [];
      this.markers = x.results.map(y => {
        return {
          position: { lat: y.latitude, lng: y.longitude },
          // label: {
          //   color: 'red',
          //   text: 'Marker label ' + (this.markers.length + 1),
          // },
          // title: 'Marker title ' + (this.markers.length + 1)
        };
      });

      // this.markers = [{ position: { lat: 53.1678, lng: -2.57179 } }, { position: { lat: 52.9963, lng:	-2.54353 } }];

      this.logger.info("Inspections loaded for map markers: ", this.markers);
    });
  }
}
