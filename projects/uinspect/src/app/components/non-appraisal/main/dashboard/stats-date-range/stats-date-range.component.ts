import {AfterViewInit, Component, EventEmitter, OnInit, Output} from '@angular/core';
import {UInspectStatRangeEnum} from '../../../../../enums/uinspect-stat-range.enum';
import {UInspectStatRequest} from '../../../../../global/interfaces';
import {subDays} from 'date-fns';
import {UInspectStatGroupingEnum} from '../../../../../enums/uinspect-stat-grouping-enum';
import {HelpersService} from '../../../../../global/services';
import {UInspectChartTypeEnum} from '../../../../../enums/uinspect-chart-type.enum';

@Component({
  selector: 'app-stats-date-range',
  templateUrl: './stats-date-range.component.html',
  styleUrls: ['./stats-date-range.component.scss']
})
export class StatsDateRangeComponent implements OnInit, AfterViewInit {

  constructor(private helperService: HelpersService) { }

  @Output() statRequestChanged: EventEmitter<UInspectStatRequest> = new EventEmitter<UInspectStatRequest>();

  public RangeType = UInspectStatRangeEnum;
  public GroupingType = UInspectStatGroupingEnum;

  public statRequest: UInspectStatRequest = {
    statRange: UInspectStatRangeEnum.PastWeek,
    statGrouping: UInspectStatGroupingEnum.Day,
    chartType: UInspectChartTypeEnum.Standard,
    fromDate: subDays(new Date(), 28),
    toDate: new Date()
  };

  statRange: string;
  groupType: string;

  public statOptions: any;
  public groupingOptions: any;

  ngOnInit(): void {

    // convert range type enum to options for radio buttons
    this.statOptions = this.helperService.getLabelsAndValues(this.RangeType, true);
    this.groupingOptions = this.helperService.getLabelsAndValues(this.GroupingType, true);

    this.statRange = UInspectStatRangeEnum.PastWeek.toString();
    this.groupType = UInspectStatGroupingEnum.Day.toString();

    //   Object.keys(this.RangeType).filter(x => !isNaN(+x)).map(s => {
    //   return {label: this.RangeType[s].replace(/([A-Z])/g, ' $1').trim(), value: s};
    // });

    // this.groupingOptions = Object.keys(this.GroupingType).filter(x => !isNaN(+x)).map(s => {
    //   return {label: this.GroupingType[s].replace(/([A-Z])/g, ' $1').trim(), value: s};
    // });
  }

  ngAfterViewInit() {
    this.statRequestChanged.emit(this.statRequest);
  }

  rangeTypeSelected(option: any) {
    this.statRequest.statRange = option.value;
    this.statRequestChanged.emit(this.statRequest);
  }

  groupingTypeSelected(option: any) {
    this.statRequest.statGrouping = option.value;
    this.statRequestChanged.emit(this.statRequest);
  }

  fromDateChanged(event: any) {
    this.statRequest.fromDate = event;
    this.statRequestChanged.emit(this.statRequest);
  }

  toDateChanged(event: any) {
    this.statRequest.toDate = event;
    this.statRequestChanged.emit(this.statRequest);
  }

}
