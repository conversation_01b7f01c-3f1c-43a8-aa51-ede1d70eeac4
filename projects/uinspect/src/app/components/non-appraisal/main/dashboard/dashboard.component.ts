import {Component} from '@angular/core';
import {NonAppraisalAppComponent} from "../../non-appraisal-app.component";
import {DataService} from "../../../../services/index";
import {Subject} from 'rxjs';
import {UInspectStatRequest} from '../../../../global/interfaces';

@Component({
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})

export class DashboardComponent {

  constructor(
    data: DataService
  ) {
    data.globals = this.globals;
  }

  globals = NonAppraisalAppComponent.globals;

  requestChangedSubject: Subject<UInspectStatRequest> = new Subject<UInspectStatRequest>();

  ngOnInit() {

  }

  onStatRequestChanged(event: UInspectStatRequest) {
    this.requestChangedSubject.next(event);
  }
}
