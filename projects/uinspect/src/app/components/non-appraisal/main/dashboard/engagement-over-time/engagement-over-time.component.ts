import {Component, Input, OnInit} from '@angular/core';
import {Observable, Subscription} from 'rxjs';
import {UInspectStatData, UInspectStatRequest} from '../../../../../global/interfaces';
import {UInspectService} from '../../../../../services';
import {LoggerService} from "../../../../../global/services";
import {format, parseISO} from 'date-fns';
import {Chart} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels'

@Component({
  selector: 'app-engagement-over-time',
  templateUrl: './engagement-over-time.component.html',
  styleUrls: ['./engagement-over-time.component.scss', '../dashboard.component.scss']
})
export class EngagementOverTimeComponent implements OnInit {

  constructor(private uinspectService: UInspectService,
              private logService: LoggerService) { }

  logger = this.logService.taggedLogger(this.constructor?.name);

  private requestChangeSubscription: Subscription;
  @Input() requestChanged: Observable<UInspectStatRequest>;

  statData: UInspectStatData[];

  datasets: any;
  labels: any;
  //colors: any;
  //public plugin: any;

  public chartOptions: any = {
    responsive: true,
    scales: {
      xAxes: [{
        ticks: {
          padding: 10,
          fontSize: 10,
          fontStyle: 'bold'
        }}],
      yAxes: [{
        ticks: {
          padding: 10,
          suggestedMax: 200,
        }
      }],
    },
    plugins: {
      datalabels: {
        anchor: 'end',
        align: 'end',
        font: {
          size: 12,
        },
        //backgroundColor: 'rgba(51, 51, 51, 0.1)',
        //borderRadius: 1,
        color: 'rgba(51, 51, 51, 1)',
        padding: 1,
        formatter: (value, context) => {
          if (value > 0) {
            return context.chart.data[context.dataIndex];
          } else {
            return null;
          }
        }
      },
    },
    legend: {
      display: true,
      position: 'bottom',
      labels: {
        color: 'rgb(255, 99, 132)'
      },
      title: {
        padding: 20
      }
    }
  };


  ngOnInit(){
    //this.plugin = ChartDataLabels;
    // Chart.register(ChartDataLabels);
    // Chart.defaults.set('plugins.datalabels', {
    //   color: '#FE777B'
    // });
    this.requestChangeSubscription = this.requestChanged.subscribe((request) => this.loadStatData(request));
  }

  ngOnDestroy() {
    this.requestChangeSubscription.unsubscribe();
  }

  loadStatData(request: UInspectStatRequest) {
    console.log("Request: ", request);
    //return;

    this.uinspectService.getStats(request).then(result => {
      this.statData = result;

      this.logger.info("Loaded stat data: ", result);

      // extract the date labels for the chart
      // .filter((v,i,a)=>a.indexOf(v)==i) // remove duplicates
      this.labels = this.statData.map(x => format(parseISO(x.date.toString()), 'd MMM yy'));

      this.logger.log("LABELS: ", this.labels);

      // convert data to chart format

      // extract requests
      let requests = this.statData.flatMap(x => x.requested
        .filter(y => y.formatName)
        .map(y => y.formatName)).filter((v,i,a)=>a.indexOf(v)==i);

      // extract opened
      let opened = this.statData.flatMap(x => x.opened
        .filter(y => y.formatName)
        .map(y => y.formatName)).filter((v,i,a)=>a.indexOf(v)==i);

      // extract completed
      let completed = this.statData.flatMap(x => x.completed
        .filter(y => y.formatName)
        .map(y => y.formatName)).filter((v,i,a)=>a.indexOf(v)==i);

      const rcd = requests.map(x => {
          return {
            label: `Requested (${x})`,
            data: this.statData.flatMap(y => y.requested.map(z => z.statCount)),
            borderWidth: 1,
            lineTension: 0.1,
            fill: true
          }
        }
      );

      const ocd = opened.map(x => {
          return {
            label: `Opened (${x})`,
            data: this.statData.flatMap(y => y.opened.map(z => z.statCount)),
            borderWidth: 1,
            lineTension: 0.1,
            fill: true
          }
        }
      );

      const ccd = completed.map(x => {
          return {
            label: `Completed (${x})`,
            data: this.statData.flatMap(y => y.completed.map(z => z.statCount)),
            borderWidth: 1,
            lineTension: 0.1,
            fill: true
          }
        }
      );

      this.datasets = rcd.concat(ocd, ccd);

      //console.log("RCD: ", rcd.reduce((a, b) => Math.max(...a.data, ...b.data))

      let maxA = Math.max(...rcd.flatMap(x => x.data));
      let maxB = Math.max(...ocd.flatMap(x => x.data));
      let maxC = Math.max(...ccd.flatMap(x => x.data));

      let max = Math.max(maxA, maxB, maxC);
      this.chartOptions.scales.yAxes[0].ticks.suggestedMax = max + (max * 0.1);

      this.logger.debug("datasets: ", this.datasets);

      // array: 0 = { label: 'Completed (format 1)', data: [0, 0, 1, 2], borderWidth: 1 } // count per date
      // array: 1 = { label: 'Completed (format 2)', data: [], borderWidth: 1 } // count per date
      // array: 2 = { label: 'Opened (format 1)', data: [], borderWidth: 1 } // count per date
      // array: 3 = { label: 'Opened (format 2)', data: [], borderWidth: 1 } // count per date
      // array: 4 = { label: 'Requested (format 1)', data: [], borderWidth: 1 } // count per date

    });
  }

  chartHovered($event: any) {

  }

  chartClicked($event: any) {

  }
}
