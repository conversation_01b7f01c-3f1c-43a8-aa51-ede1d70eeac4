import {Component, OnInit} from '@angular/core';
import {UInspectDTO} from "../../../../global/interfaces";
import {UInspectService, URLService} from "../../../../services/index";
import {ActivatedRoute} from "@angular/router";
import * as moment from "moment";

@Component({
  templateUrl: './inspection-test.component.html',
  styleUrls: ['./inspection-test.component.scss'],
})

export class InspectionTestComponent implements OnInit {
  inspections: UInspectDTO[] = [];
  formatId: number;

  constructor(
    private uInspectService: UInspectService,
    private activeRoute: ActivatedRoute,
    private url: URLService
  ) {

    this.formatId = this.activeRoute.snapshot.params.id;

  }

  ngOnInit() {

    this.inspectionTest();
  }

  inspectionTest() {

    this.uInspectService.create({
      uInspectFormatId: this.formatId,
      customerName: "Test Name " + moment().format("DDMMhhmm"),
      email: "TestEmail@" + moment().format("DDMMhhmm") + ".com",
      mobileNumber: moment().format("DDMMhhmm"),
      vrm: "AB12CDE",
      vehicleDesc: "Test Make - Test Model"
    }).then((x) => {


      this.url.doInspection(x.dto.id);

    });
  }
}
