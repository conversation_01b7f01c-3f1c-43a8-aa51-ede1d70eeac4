import {AfterViewInit, Component, Host<PERSON>ist<PERSON>, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {AuthenticatorService} from "@aws-amplify/ui-angular";
import {User} from "../../../global/interfaces";
import {LoggerService, UserService} from "../../../global/services";
import {RoleGuardService, URLService} from "../../../services";

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrls: ['./main.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class MainComponent implements OnInit, OnDestroy, AfterViewInit {

  isMobile = false;
  isDevice = false;
  public user: User;
  menuItems: any;
  private defaultMenuItems: { itemRoute?: string; itemLabel?: string, subMenu?: any[], roles?: string[] }[] = [];

  constructor(
    private route: ActivatedRoute,
    private redirect: URLService,
    private router: Router,
    private userService: UserService,
    private authService: RoleGuardService,
    public authenticated: AuthenticatorService,
    private logService: LoggerService) {

    this.defaultMenuItems = [
      {itemRoute: this.redirect.dashboard(true), itemLabel: "Dashboard"},
      {itemRoute: this.redirect.inspections(true), itemLabel: "Inspections"},
      {itemRoute: "", itemLabel: "Settings", roles: ["GOD"], subMenu: [
        {itemRoute: this.redirect.formats(true), itemLabel: "Formats"},
      ]}
    ];
  }


  async ngOnInit() {

    await this.userService.loadCurrentUser().then(() => {
      this.user = this.userService.CurrentUser;
    });

    this.createNavs(this.router.url);
  }

  async ngAfterViewInit() {

  }

  @HostListener('window:beforeunload')
  ngOnDestroy() {
  }

  private createNavs(url: string) {

    this.menuItems = this.defaultMenuItems;

    // filter out menu items based on user roles
    const items = [];
    this.menuItems.flatMap(x => ({menu: x, roles: x.roles})).forEach(x => {
      if (!x.roles || this.authService.HasRole(this.user, x.roles)) {
        items.push(x.menu);
      }
    });

    this.menuItems = items;
  }
}
