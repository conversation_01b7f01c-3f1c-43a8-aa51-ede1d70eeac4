import {Component, OnInit, ViewChild} from '@angular/core';
import {
  NgxGalleryImageWithId, UInspectMediaSyncDTO,
  UInspectQuestionSyncDTO,
  UInspectSectionSyncDTO,
  UInspectSectionWithAnswersDTO,
  UInspectSyncDTO, VehicleMediaDTO,
} from "../../../../global/interfaces";
import {UInspectMediaService, UInspectService, URLService} from "../../../../services/index";
import {ActivatedRoute} from "@angular/router";
import {
  StatusEnum,
  UInspectQuestionTypeEnum,
  UInspectSectionTypeEnum,
} from "../../../../global/enums";
import {UInspectCommonService} from "../../../../global/services";
import {ModalDirective} from "ng-uikit-pro-standard";
import {MDBModalService} from "ng-uikit-pro-standard";

@Component({
  templateUrl: './inspection-view.component.html',
  styleUrls: ['./inspection-view.component.scss'],
})

export class InspectionViewComponent implements OnInit {
  inspection: UInspectSyncDTO = {};
  sections: UInspectSectionSyncDTO[] = [];
  inspectionId: string;
  loading: boolean;
  questionSections: UInspectSectionSyncDTO[] = [];
  mediaSections: UInspectSectionSyncDTO[] = [];

  uInspectQuestionTypeEnum = UInspectQuestionTypeEnum;
  orderedSections: UInspectSectionWithAnswersDTO[] = [];
  imageGallery: NgxGalleryImageWithId[] = [];
  @ViewChild("galleryModal") galleryModal: ModalDirective;
  @ViewChild("uploadMediaModal") uploadMediaModal: ModalDirective;
  zoomImageUrl: string;
  selectedImage: any = null;
  private mediaToDelete: NgxGalleryImageWithId;
  showDeleteConfirmMedia: boolean;
  filesUploading: any;
  private replaceMedia: any;
  filteredVideos: [] = [];

  constructor(
    private uInspectService: UInspectService,
    private uInspectCommon: UInspectCommonService,
    private uInspectMediaService: UInspectMediaService,
    private modalService: MDBModalService,
    private router: ActivatedRoute,
    public url: URLService
  ) {

    this.inspectionId = this.router.snapshot.params.id;
  }

  ngOnInit() {

    this.getInspection(this.inspectionId);

  }

  getInspection(inspectionId) {

    this.loading = true;

    this.uInspectService.get(inspectionId, {component: 'inspection-view'}).then((x) => {
      this.inspection = x.dto;
      this.sections = this.inspection.uInspectFormat.sections.filter(z => z.statusId === StatusEnum.Active);
      this.loading = false;
      this.orderedSections = this.uInspectCommon.fetchOrderedInspectSections(this.inspection);
      this.questionSections = this.getQuestionSections();
      this.mediaSections = this.getMediaSections();
      this.imageGallery = this.createImageGallery(this.orderedSections);
    });

  }

  getQuestionSections() {
    return this.orderedSections.filter(x => x.sectionType === UInspectSectionTypeEnum.Question && x.statusId === StatusEnum.Active);
  }

  getMediaSections() {
    return this.orderedSections
      .filter(x => x.sectionType === UInspectSectionTypeEnum.Question && x.statusId === StatusEnum.Active);
  }

  answerOption(question: UInspectQuestionSyncDTO) {

    return question.options.find(x => x.id === question?.answer?.answerOptionId)?.optionLabel;
  }

  private createImageGallery(orderedSections: UInspectSectionSyncDTO[]) {

    const gallery: NgxGalleryImageWithId[] = [];

    for (const section of orderedSections) {
      for (const image of section.medias) {
        gallery.push({
          mediaInfo: image,
          small: image.mediaURL + "?tr=h-100",
          medium: image.mediaURL + "?tr=h-400",
          big: image.mediaURL,
          description: section.internalLabel || section.title
        });
      }

      for (const question of section.questions) {
        for (const image of question.medias) {
          gallery.push({
            mediaInfo: image,
            small: image.mediaURL + "?tr=h-100",
            medium: image.mediaURL + "?tr=h-400",
            big: image.mediaURL,
            description: section.internalLabel || section.title
          });
        }
      }
    }

    return gallery;
  }

  showFullGallery(image) {

    this.selectedImage = image;
    this.zoomImageUrl = image.big;
    this.galleryModal.show();

  }

  removeMedia(image: any) {

    console.log("REMOVE MEDIA");
    this.mediaToDelete = image;
    this.showDeleteConfirmMedia = true;
  }

  confirmDeleteMedia() {

    this.uInspectMediaService.delete(this.mediaToDelete.mediaInfo.id)
      .then((x) => {

        this.mediaToDelete.mediaInfo.statusId = StatusEnum.Deleted;

      })
      .catch(() => {


      })
      .finally(() => {
        this.showDeleteConfirmMedia = false;
      });
  }

  cancelDeleteMedia() {

    this.mediaToDelete = null;
    this.showDeleteConfirmMedia = false;
  }

  async showUploadMedia(media) {

    this.uploadMediaModal.show();
    this.replaceMedia = media.mediaInfo;

  }

  async onFileDropped(fileList: FileList) {
    this.filesUploading = true;
    const files = Array.from(fileList);
    this.uploadFiles(files);
  }

  // NOTE: iOS cannot provide a file[] only a dataUrl
  // Which is why we have this method
  async onSingleFileDropped(dataUrl: string) {

    this.filesUploading = true;
    await this.uploadFiles([], [dataUrl]);
  }

  uploadFiles(files: File[] = [], dataUrls: string[] = []) {

    const uploadDTO: UInspectMediaSyncDTO = {
      id: this.replaceMedia.id,
      uInspectId: this.replaceMedia.uInspectId,
    };

    this.uInspectMediaService.uploadMedia(uploadDTO, files, dataUrls).then((results: VehicleMediaDTO[]) => {

    }).catch((error) => {

    }).finally(() => {
      this.filesUploading = false;
      this.getInspection(this.inspectionId);
      this.uploadMediaModal.hide();
    });
  }

  dragDropClosed() {

  }

  filteredGallery(imageGallery: NgxGalleryImageWithId[]) {

    return imageGallery.filter(x => x.mediaInfo.statusId === StatusEnum.Active);

  }
}
