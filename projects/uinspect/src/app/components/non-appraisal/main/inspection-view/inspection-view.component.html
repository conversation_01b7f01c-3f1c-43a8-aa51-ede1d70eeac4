<div class="d-flex flex-wrap">
  <div class="flex-grow-1">
    <h1 class="page-header">Inspection</h1>
  </div>
  <div class="flex-shrink-1">
    <a class="btn btn-xs btn-primary-outline" [routerLink]="url.doInspection(inspection.id, true)" target="_new">Simulator</a>
  </div>
</div>


<div class="widget padding pb-0 mt-1">
  <div class="top-grid">
    <div>
      <div class="grid-label">Customer Name</div>
      <div class="grid-value">{{ inspection.customerName || "N/A" }}</div>
      <div class="grid-label">Customer E-mail</div>
      <div class="grid-value">{{ inspection.email || "N/A" }}</div>
      <div class="grid-label">Customer Phone</div>
      <div class="grid-value">{{ inspection.mobileNumber || "N/A" }}</div>
    </div>
    <div>
      <div class="grid-label">Registration Number</div>
      <div class="grid-value">{{ inspection.vrm || "N/A" }}</div>
      <div class="grid-label">Vehicle</div>
      <div class="grid-value">{{ inspection.vehicleDesc || "N/A" }}</div>
      <div class="grid-label">External Ref</div>
      <div class="grid-value">{{ inspection.externalRef || "N/A" }}</div>
    </div>
    <div>
      <div class="grid-label">Created</div>
      <div class="grid-value">{{ inspection.added | date: "HH:mm dd/MM/YYYY"}}</div>
      <div class="grid-label">Opened</div>
      <div class="grid-value">{{ inspection.opened | date: "HH:mm dd/MM/YYYY"}}</div>
      <div class="grid-label">Completed</div>
      <div class="grid-value">{{ inspection.completed | date: "HH:mm dd/MM/YYYY"}}</div>

    </div>
  </div>
</div>

<div class="mt-3">

  <h2 class="page-header">Answers</h2>
  <div class="widget padding pb-0 mt-1">

    <div class="top-grid-2">

      <div *ngFor="let section of questionSections">

        <div class="grid-label">
          {{ section.internalLabel || section.title }}
        </div>
        <div class="grid-value">
          <div *ngIf="section.questions[0].questionType == uInspectQuestionTypeEnum.Option">
            {{ answerOption(section.questions[0]) }}
          </div>
        </div>

      </div>

    </div>
  </div>
</div>

<div class="widget padding mt-2" *ngIf="filteredVideos.length > 0">
  <!-- Show video -->
</div>

<div class="mt-3">

  <h2 class="page-header">Photos</h2>
  <div class="image-grid mt-1">
    <div class="image-cell" *ngFor="let image of filteredGallery(imageGallery)">
      <div class="d-flex flex-wrap">
        <div class="flex-shrink-1 image-label">{{ image.description }}</div>
        <div class="flex-grow-1 text-right">
          <i class="fa fa-cloud-upload-alt cursor-pointer" (click)="showUploadMedia(image)"></i>
          &nbsp;
          <i class="fa fa-trash-alt cursor-pointer" (click)="removeMedia(image)"></i>
        </div>
      </div>
      <div (click)="showFullGallery(image)" class="image-item cursor-pointer"
           style="background-image: url({{ image.medium }})"></div>
    </div>
  </div>
</div>

<div mdbModal #galleryModal="mdbModal" class="modal fade"
     style="height: 100%;"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{keyboard: false}">
  <div class="modal-dialog modal-near-full-size modal-dialog-centered" role="document">
    <div class="modal-content" style="background-color: #ccc;">
      <div class="modal-header">
        <div class="flex-shrink-1">&nbsp; &nbsp;</div>
        <div class="flex-grow-1 text-center">
          {{ selectedImage?.description }}
        </div>
        <div class="flex-shrink-1">
          <button type="button" class="close" aria-label="Close" (click)="galleryModal.hide()"><span
            aria-hidden="true">×</span></button>
        </div>
      </div>
      <div class="modal-body" style="background-color: transparent;">

        <lib-ngx-image-zoom
          [thumbImage]="zoomImageUrl"
          [fullImage]="zoomImageUrl"
          [enableScrollZoom]="true"
          [maxZoomRatio]="3"
        ></lib-ngx-image-zoom>

      </div>
      <div class="modal-footer justify-content-center" (click)="galleryModal.hide()" style="background-color: #fff;">
        <div class="zoom-info"><i class="fa fa-info-circle"></i> Use mouse-wheel to zoom in/out</div>
        <button class="btn btn-block btn-outline-primary"><i class="fa fa-times"></i> Close</button>
      </div>
    </div>
  </div>
</div>

<div mdbModal #uploadMediaModal="mdbModal" class="modal fade" style="height: 100%;"
     role="dialog" aria-labelledby="myBasicModalLabel" aria-hidden="true" [config]="{keyboard: false}">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <div class="flex-shrink-1">Media Upload</div>
        <div class="flex-grow-1 text-center">
          {{ selectedImage?.description }}
        </div>
        <div class="flex-shrink-1">
          <button type="button" class="close" aria-label="Close" (click)="uploadMediaModal.hide()"><span
            aria-hidden="true">×</span></button>
        </div>
      </div>
      <div class="modal-body" style="background-color: transparent;">

        <app-dragdropfiles
          [filesUploading]="filesUploading"
          (fileDropped)="onFileDropped($event)"
          (singleFileDropped)="onSingleFileDropped($event)"
          (dragDropClosed)="dragDropClosed()">
        </app-dragdropfiles>

      </div>
    </div>
  </div>
</div>

<app-delete-confirm-modal
  [showDialog]="showDeleteConfirmMedia"
  (modalClosed)="showDeleteConfirmMedia = false"
  (confirmDelete)="confirmDeleteMedia()"
  (cancelDelete)="cancelDeleteMedia()"></app-delete-confirm-modal>
