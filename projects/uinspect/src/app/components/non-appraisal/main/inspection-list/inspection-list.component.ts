import {Component, OnInit} from '@angular/core';
import {UInspectDTO} from "../../../../global/interfaces";
import {UInspectService, URLService} from "../../../../services/index";

@Component({
  templateUrl: './inspection-list.component.html',
  styleUrls: ['./inspection-list.component.scss'],
})

export class InspectionListComponent implements OnInit {
  inspections: UInspectDTO[] = [];

  constructor(
    private uInspectService: UInspectService,
    public url: URLService
  ) {

  }

  ngOnInit() {

    this.getInspections();
  }

  getInspections() {

    this.uInspectService.search({component: 'main-inspections'}).then((x) => {

      this.inspections = x.results;

    });
  }

  sortedInspections(inspections: UInspectDTO[]) {

    return inspections.sort((a, b) => b.added.toString().localeCompare(a.added.toString()));
  }


}
