<h1 class="page-header">Inspections</h1>

<div class="widget padding mt-1">
  <table class="table table-striped table-narrow">
    <thead>
    <tr>
      <th>VRM</th>
      <th>Vehicle</th>
      <th>Customer</th>
      <th>E-mail</th>
      <th>Phone</th>
      <th>Added</th>
      <th>Opened</th>
      <th>Completed</th>
      <th></th>
    </thead>
    <tbody>
    <tr *ngFor="let inspection of sortedInspections(inspections)" (click)="url.viewInspection(inspection.id)" class="inspection-row">
      <td class="vrm">{{ inspection.vrm }}</td>
      <td class="vehicle-desc">{{ inspection.vehicleDesc }}</td>
      <td>{{ inspection.customerName }}</td>
      <td>{{ inspection.email }}</td>
      <td>{{ inspection.mobileNumber }}</td>
      <td>{{ inspection.added | date: "HH:mm dd/MM/YY"}}</td>
      <td>{{ inspection.opened | date: "HH:mm dd/MM/YY"}}</td>
      <td>{{ inspection.completed | date: "HH:mm dd/MM/YY"}}</td>
      <td><a class="btn btn-xs btn-primary-outline" [routerLink]="url.doInspection(inspection.id, true)" target="_new">Simulator</a></td>
    </tr>
    </tbody>
  </table>
</div>
