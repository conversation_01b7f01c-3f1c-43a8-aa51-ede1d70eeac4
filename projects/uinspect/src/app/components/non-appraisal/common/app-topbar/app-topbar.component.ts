import {DOCUMENT} from '@angular/common';
import {AfterViewInit, Component, HostListener, Inject, Input, OnD<PERSON>roy, OnInit} from '@angular/core';
import {NavigationEnd, Router} from '@angular/router';
import {Subscription} from 'rxjs';
import {User} from '../../../../global/interfaces/index';
import {ApiService, ContactService, LoggerService, UserService} from '../../../../global/services/index';
import {UInspectURLService} from "../../../../services/index";
import {signInWithRedirect, signOut} from "aws-amplify/auth";

@Component({
  selector: 'app-topbar',
  templateUrl: './app-topbar.component.html',
  styleUrls: ['./app-topbar.component.scss']
})
export class AppTopbarComponent implements OnInit, OnDestroy, AfterViewInit {

  @Input() hideSearchBar: boolean;

  public dropDownMenu = [];
  public navbarCollapsed = true;
  public vehicleSearchInput: string;
  public user: User;
  public isLoggedIn = false;

  messageEventSub: Subscription;
  folderCounts: any;

  logger = this.logService.taggedLogger(this.constructor?.name);

  constructor(@Inject(DOCUMENT)
              public document: Document,
              private userService: UserService,
              public url: UInspectURLService,
              public router: Router,
              private logService: LoggerService,
              private apiClient: ApiService,
              private contactService: ContactService
  ) {
    this.router = router;
  }

  async ngOnInit() {
    this.areWeLoggedin();
  }

  async ngAfterViewInit() {

    this.router.events.subscribe((val) => {
      if (val instanceof NavigationEnd) {
        this.areWeLoggedin();
      }
    });
  }

  async areWeLoggedin() {

    this.userService.loadCurrentUser().then((x) => {

      if (x == null) {
        this.isLoggedIn = false;
        return;
      }

      this.user = this.userService.CurrentUser;
      this.isLoggedIn = this.user.isLoggedIn;

      console.log("LOGGED IN ", this.isLoggedIn);

      this.dropDownMenu = [
        {itemRoute: this.url.dashboard(true), itemLabel: 'Dashboard'},
      ];

    }).catch(() => {

      this.logger.error("TOPBAR: CAUGHT: USER NOT LOGGED IN");

    });
  }


  @HostListener('window:beforeunload')
  ngOnDestroy() {
    if (this.messageEventSub) {
      this.messageEventSub.unsubscribe();
    }
  }

  public login() {
    this.logger.info("Logging in using federated signin");

    signInWithRedirect({
      provider: "Google",
    })
      .then(() => {
      });
  }

  logout() {

    this.user.isLoggedIn = false;
    this.contactService.notifyLogout(this.user.contactId).then(async (x) => {

      await this.userService.logout();

    }).catch(async () => {

      await this.userService.logout();

    });
  }

//  installPwa() {
//    this.Pwa.promptEvent.prompt();
//  }
}
