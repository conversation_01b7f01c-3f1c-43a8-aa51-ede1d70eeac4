
.menu-logo-text {
  font-size: 48px;
}

.menu-logo-text-loggedin {
  font-size: 32px;
}

.user-name {
  font-size: 0.75rem;
  line-height: 20px;
  vertical-align: text-top;
}

.side-nav {

  background-color: #0d181c !important;

}

.dropdown-line {

  border-top: 1px solid #ddd;

}

#vehicle-search {
  cursor: pointer;

  &:hover {
    color: rgba(0, 0, 0, 0.3);
  }
}


a.dropdown-item, a.dropdown-item:visited {

  color: var(--textColour);

}

.search-submit {

  background-color: rgba(255, 255, 255, 0.1);
  border: 0 solid transparent !important;
  color: var(--colour3);
  cursor: pointer;

}


nav {
  background-color: var(--navbarBgColour) !important;

  .login-link, .signup-link {
    .nav-link {
      font-weight: 900 !important;
    }
  }
}

nav .nav-link, nav {
  color: var(--navbarTextColour) !important;
  font-size: 16px;
  font-weight: 500;

  &:hover {
    color: var(--navbarTextHoverColour);
  }

}

.navbar {
  padding-top: 0.5rem;
  box-shadow: 0 0 5px #00000029 !important;
}

nav .nav-link:hover, .navbar-light .navbar-nav .show > .nav-link, nav {
  color: var(--navbarTextHoverColour);
}

.navbar-nav.inline {
  flex-direction: row !important;
}

nav .navbar-brand, nav .navbar-brand a, .navbar-brand a:visited {
  cursor: pointer;
}

nav .navbar-brand a {
  color: inherit;
  text-decoration: inherit;
}

.navbar-light .navbar-toggler {

  color: var(--navbarTextHoverColour);

  .toggler-text {

    font-size: 1rem;
    font-weight: bold;
    vertical-align: top;
    line-height: 1.2rem;
    padding-right: 5px;
    display: inline-block;
  }

  padding-bottom: 0 !important;
  border: 0;
}

.search-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

.search-box {

  max-width: 752px;

  .search-text-input {

    background-color: var(--navSearchBgColour);
    color: var(--navSearchTextColour) !important;
    border: 0 !important;
    font-size: 0.85rem;
    font-weight: 500;

    &::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
      color: var(--colour10);
      opacity: 1; /* Firefox */
    }

    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
      color: var(--colour10);
    }

    &::-ms-input-placeholder { /* Microsoft Edge */
      color: var(--colour10);
    }
  }

  .input-group-append {
    border-radius: 0 5px 5px 0;
  }
}

:focus {
  outline-width: 0 !important;
}


.user-dropdown {

  .fa-user {
    color: var(--topbarIconColour) !important;
  }

  font-size: 0.9rem;
  font-weight: 500;

  span {
    color: var(--linkColour);
  }
}

.contact-message {
  display: inline-block;
  width: 32px;
  height: 32px;
}

.search-link {

  .fa-search {
    font-size: 25px;
    vertical-align: middle;
    margin-left: -4px;
    color: var(--topbarIconColour);
  }
}

.navbar-brand {

  font-family: "MS Sans Serif", Geneva, "Sans-Serif";
  padding-top: 0 !important;
  padding-bottom: 0 !important;

  .u-text {
    padding-left: 7px;
    line-height: 46px;
    height: 46px;
    color: #046B8D;
    vertical-align: top;
    font-size: 27px;
  }

  .u-icon {
    width: 46px;
    height: 46px;
    background-image: url(/assets/sites/toyota-inspect/images/sitelogo.png);
    background-size: contain;
  }
}
