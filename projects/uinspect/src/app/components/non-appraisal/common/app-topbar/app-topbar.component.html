<nav class="navbar navbar-expand-md navbar-light bg-light sticky-top">
  <div class="container header-size">

    <div class="navbar-brand">
      <div class="d-flex">
        <div class="flex-shrink-1">
          <div class="sitelogo"></div>
        </div>
      </div>
    </div>

    <button *ngIf="!isLoggedIn" class="navbar-toggler" type="button" aria-controls="navbarContent"
            [attr.aria-expanded]="!navbarCollapsed"
            aria-label="Toggle navigation" (click)="navbarCollapsed = !navbarCollapsed">
      <span class="toggler-text"><i class="fa fa-bars"></i></span>
    </button>


    <div *ngIf="!isLoggedIn" class="navbar-collapse" [class.collapse]="navbarCollapsed" id="navbarContent">
      <ul class="navbar-nav ml-auto text-right">

        <li class="nav-item order-3 order-md-3">
          <a class="nav-link" [routerLink]="url.contact(true)">Contact</a>
        </li>

        <li class="nav-item login-link order-1 order-md-4">
          <a class="nav-link" [href]="url.login(true)">Log in</a>
        </li>
      </ul>
    </div>

    <div *ngIf="isLoggedIn" style="display: flex; flex-grow: 1; flex-basis: auto;">

      <ul class="navbar-nav inline ml-auto order-1">
        <li class="nav-item dropdown remarq-dropdown" dropdown *ngIf="isLoggedIn">

          <a dropdownToggle class="nav-link dropdown-toggle waves-light user-dropdown">
            <i class="fas fa-user"></i>
            <span class="d-none d-md-inline user-name">
              {{ user.name }}
            </span>
          </a>

          <div *dropdownMenu class="dropdown-menu dropdown-menu-right dropdown dropdown-primary" role="menu">
            <div *ngFor="let dropDownMenuItem of dropDownMenu">
              <div [ngClass]="(dropDownMenuItem.itemLine)?'dropdown-line':''">
                <a class="dropdown-item waves-light"
                   routerLink="{{ dropDownMenuItem.itemRoute }}">{{ dropDownMenuItem.itemLabel }}</a>
              </div>
            </div>
            <div>
              <a class="dropdown-item waves-light" (click)="logout()">Logout</a>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</nav>
