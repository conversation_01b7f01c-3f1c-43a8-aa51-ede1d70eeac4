html, body, app-root {
  height: 100%;
  width: 100%;
  margin: 0;
}

#homepage {
  color: var(--colour5);
}

.w-50 {
  width: 50%;
}

.attention-box {
  background-color: var(--attentionBoxBackgroundColour) !important;
  box-shadow: 2px 2px 5px 0px rgb(50 50 50 / 5%);
  border-radius: 5px;
  cursor: pointer;
}


.text-danger-alt {
  color: var(--altDangerColour);
}

.text-success {
  color: var(--successColour) !important;
}

.accordion .card .card-header {
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-top: 5px !important;
  padding-bottom: 5px !important;

  .mdb-accordion-indicator {
    top: 5px;
  }

  a {

    &:hover {
      text-decoration: none !important;
    }

  }
}

.menu-topbar {
  background-color: #f35e48;
  height: 50px;
  padding: 20px;
  color: white;
}

.menu-topbar-left {
  float: left;
}

.menu-topbar-right {
  display: flex;
  float: right;
}

.shrink-cell {
  width: 0.1%;
  white-space: nowrap;
}

.fa-15x {
  font-size: 1.5em;
}

.fa-12x {
  font-size: 1.2em;
}


.table {
  margin-bottom: 0 !important;

  &.table-narrow {

    td, th {
      padding: 0.375rem;
    }

  }
}

/***** GLOBAL STYLES FOR THIRD PARTY COMPONENTS ****/

/* BOOTSTRAP 4 */

.popover {
  width: 440px;
  max-width: 440px !important;
}

/* NGX PAGINATION */

.pagination {

  a {
    color: #0a0a0a;
    display: block;
    padding: 0.1875rem 0.625rem;
    border-radius: 0;
  }

  a.disabled {
    pointer-events: none;
    cursor: default;
    color: #cacaca !important;
  }

  .current a {
    color: #fff !important;
  }
}


.ngx-pagination .current, .pagination .current {

  background-color: var(--linkColour) !important;
  border-radius: 50% !important;
  width: 30px;
  height: 30px;
  text-align: center;
  font-weight: bold;
}

.picker {

  .md-form {

    .form-control {

      padding: 0.375rem 0.75rem !important;
    }
  }
}

.toggle-label {
  display: inline-block;
  line-height: 25px;
  height: 25px;
  vertical-align: top;
  padding-left: 10px;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--floatLabelColour);
}

.cellpadding-0 {

  /* cellpadding */
  th, td {
    padding: 0;
  }

  /* cellspacing */
  table {
    border-collapse: separate;
    border-spacing: 0;
  }

  table {
    border-collapse: collapse;
    border-spacing: 0;
  }

  /* valign */
  th, td {
    vertical-align: top;
  }

  /* align (center) */
  table {
    margin: 0 auto;
  }

}


// We do this for 3rd party components we have no control over  to align the input group
.input-group-prepend .md-form {

  margin-bottom: 0 !important;

  label.active {

    top: -9px;

  }

  label {

    left: 36px !important;

  }
}

/* MDB FORM FLOAT LABELS */

h1.inline-input {
  margin-bottom: 0;
  line-height: 34px;
}

/* This won't have a float label */

.md-form.micro-select {

}

.md-form.narrowest {

  margin-bottom: 0 !important;

  label {
    font-size: 0.9rem;
    transform: translateY(9px);
  }

  .float-label.form-control:focus, .float-label.form-control:not(:placeholder-shown) {
    padding: 5px 12px 2px 12px !important;
  }

  .form-control {

    line-height: 1.7rem;
    margin-bottom: 0;
    font-size: 0.9rem;
  }
}

.inline-error-message {

  color: var(--errorColour);
  font-size: 0.75rem;
  font-weight: 400;

}

.md-form {

  position: relative;
  margin-top: 0 !important;
  margin-bottom: 1.2rem !important;

  &.on-background {
    .form-control {
      border: 1px solid #ced4da !important;
    }

    input[type=date].float-label.form-control + label,
    label.active {
      background-color: var(--bgColour) !important;
    }
  }

  input[type=date].float-label.form-control + label {

    &.focus {
      color: var(--floatInputFocusLabelColour) !important;
    }

    top: var(--floatLabelTop) !important;
    font-size: var(--floatLabelFontSize) !important;
    transform: none !important;
    color: #757575 !important;
    left: var(--floatLabelLeft) !important;
    background-color: #fff;
    padding-left: 5px !important;
    padding-right: 5px !important;
    margin-left: 0 !important;
    width: auto !important;
  }

  .currencyPrepend {

    left: 36px !important;

  }

  label.active {

    transform: none !important;

  }

  &.non-fixed-height {

    margin-bottom: 0.1rem !important;
    margin-top: 0.9rem !important;

    .error-message {
      margin-left: 5px;
      position: relative;
      top: -8px !important;
      height: 5px !important;
      margin-bottom: 25px;
      font-size: 0.75rem;
      font-weight: 400;
    }
  }


  .form-control {

    /* Prevents the padding from enlarging the input */
    -webkit-box-sizing: border-box !important;
    -moz-box-sizing: border-box !important;
    box-sizing: border-box !important;
  }

  /* Standard Float Label */
  .float-label.form-control {

    padding-left: 10px !important;

    &.ng-invalid.ng-dirty:not(:focus) {
      border-color: var(--errorColour) !important;
    }

    &.ng-invalid.ng-dirty:not(:focus) + label {

      color: var(--errorColour) !important;

    }

    &:focus {
      padding-top: 10px !important;
      border-color: var(--floatInputFocusBorderColour) !important;
    }

    &:focus, &:not(:placeholder-shown) {
      padding: 0.7rem 0 0.5rem 12px !important;
    }

    & + label {
      top: -1px !important;
      padding-left: 12px !important;
      color: var(--placeholderColour) !important;
      font-weight: var(--placeholderWeight) !important;
    }

    & + label.active {
      transform: none !important;
      position: absolute !important;
      font-size: var(--floatLabelFontSize) !important;
      left: var(--floatLabelLeft) !important;
      top: var(--floatLabelTop) !important;
      background-color: #fff;
      padding-left: var(--floatLabelPadding) !important;
      padding-right: var(--floatLabelPadding) !important;
      color: var(--floatLabelColour) !important;
      font-weight: var(--floatLabelWeight) !important;
      z-index: 3;
    }

    & + label.currencyPrepend.active, .currencyPrepend {

      left: 42px !important;
    }

    &:focus + label {
      color: var(--floatInputFocusLabelColour) !important;
      font-weight: var(--floatInputFocusLabelWeight) !important;
    }
  }

  /* For inline float labels search forms -- no space allocated for errors */
  &.inline {
    margin-top: 0.0rem !important;
    margin-bottom: 0.0rem !important;

    .float-label.form-control {

      padding-top: 0.375rem !important;
      padding-bottom: 0.375rem !important;
      margin-bottom: 0;

      & + label {
        top: -4px !important;
      }

      & + label.active {
        position: relative;
        top: var(--floatLabelTop);
        left: var(--floatLabelLeft);
        font-weight: var(--floatLabelWeight);
      }
    }
  }

  /* Narrow, non float inputs */
  &.narrow {
    margin-top: 0.4rem !important;
    margin-bottom: 0.4rem !important;

    .form-control {
      margin-bottom: 0;
    }
  }

  &.prepend-spacer {

    margin-top: 0.4rem !important;
    margin-bottom: 1.7rem !important;

    .form-control {
      margin-bottom: 0;
    }

  }

  select.form-control {
    padding-left: 7px !important;
  }

}

.md-form, .md-select {

  .error-message {
    top: 46px !important;
    left: 9px !important; // 28/07 moved from 9px to 0px for the register page errors
    color: var(--errorColour);
    font-weight: 400;
    position: absolute;
    font-size: 0.65rem;
    line-height: 0.65rem;
    padding-left: var(--floatLabelPadding);
    padding-right: var(--floatLabelPadding);
    background-color: var(--floatLabelBackgroundColour);

    &.no-float {
      top: 0 !important;
      position: relative !important;
    }
  }
}


.btn-group {

  .btn {
    border-color: var(--linkColour);
    font-weight: 400;
  }

  .btn.active {
    background-color: var(--linkColour);
    color: #fff;
  }
}


/* DELETE MODAL */

.modal-danger {

  a.btn {
    color: var(--colour3) !important
  }

}

/* MDB SWITCH */

.switch.round label .lever {
  width: 47px;
  height: 25px;
  border-radius: 10em;
}

.switch.round label .lever:after {
  width: 21px;
  height: 21px;
  border-radius: 50%;
  left: 2px;
  top: 2px;
  box-shadow: none !important;
}

.switch {

  &.blue-white-switch label {
    input[type=checkbox]:checked + .lever {
      background-color: var(--switchColour);
    }

    input[type=checkbox]:checked + .lever:after {
      background-color: #fff;
    }

    .lever {
      background-color: #ccc;
      margin-left: 0;
    }

    .lever:after {
      background-color: #fff;
    }
  }

  &.green-white-switch label {
    input[type=checkbox]:checked + .lever {
      background-color: forestgreen;
    }

    input[type=checkbox]:checked + .lever:after {
      background-color: #fff;
    }

    .lever {
      background-color: #ccc;
      margin-left: 0;
    }

    .lever:after {
      background-color: #fff;
    }
  }

  &.red-white-switch label {
    input[type=checkbox]:checked + .lever {
      background-color: var(--errorColour);
    }

    input[type=checkbox]:checked + .lever:after {
      background-color: #fff;
    }

    .lever {
      background-color: #ccc;
      margin-left: 0;
    }

    .lever:after {
      background-color: #fff;
    }
  }
}

.fa-times-circle {
  color: #c00 !important;

}

/* MDB SELECT */


.micro-select {

  &.on-background mdb-select .form-control {
    border: 1px solid #ced4da !important;
  }

  .mdb-select-toggle {
    height: 29px;
    line-height: 29px;
    top: calc(50% + 8px);
  }

  mdb-select:not(.inline) .single, mdb-select:not(.inline) .mdb-select-value, mdb-select-2:not(.inline) .single, mdb-select-2:not(.inline) .mdb-select-value, mdb-date-picker:not(.inline) .single, mdb-date-picker:not(.inline) .mdb-select-value {

    padding-bottom: 0.15rem !important;
    padding-top: 0.15rem !important;

    .mdb-select-arrow {
      height: 30px !important;
      line-height: 30px !important;
      width: 30px !important;
    }
  }

  .single .value {
    font-size: 0.9rem !important;
  }
}

.narrow-select {

  .input-group-text {

    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;

  }

  mdb-select:not(.inline) .single, mdb-select:not(.inline) .mdb-select-value, mdb-select-2:not(.inline) .single, mdb-select-2:not(.inline) .mdb-select-value, mdb-date-picker:not(.inline) .single, mdb-date-picker:not(.inline) .mdb-select-value {

    padding-bottom: 0.25rem !important;
    padding-top: 0.275rem !important;

    .mdb-select-toggle {
      top: calc(50% + 8.5px);
    }

    .mdb-select-arrow {
      height: 32px !important;
      line-height: 32px !important;
      width: 32px !important;
    }
  }

  .single .value {
    font-size: 0.9rem !important;
  }

  label:not(.active) {
    font-weight: 500;
    color: #bbb;
    font-size: 0.9rem;
  }
}

.md-form {
  &.narrow-input {

    .input-group-text {
      padding: 0.1rem 0.5rem !important;
      font-size: 0.875rem !important;
      height: 26px;
    }

    .form-control {
      padding: 0.2rem 12px !important;
      line-height: 26px !important;
      font-size: 0.875rem !important;
    }

    .float-label.form-control {

      & + label.active {
        font-size: 11px !important;
      }

      & + label:not(.active) {
        top: -8px !important;
        padding-left: 14px !important;
      }

      &:not(:placeholder-shown) {
        padding: 0.2rem 12px !important;
      }
    }
  }
}

.widget-h2 {

  line-height: 30px;
  margin-bottom: 0 !important;
  padding-left: 5px;

}

.narrowest-select {

  mdb-select:not(.inline) .single, mdb-select:not(.inline) .mdb-select-value, mdb-select-2:not(.inline) .single, mdb-select-2:not(.inline) .mdb-select-value, mdb-date-picker:not(.inline) .single, mdb-date-picker:not(.inline) .mdb-select-value {

    padding-bottom: 0.15rem !important;
    padding-top: 0.15rem !important;
    font-size: 0.85rem !important;

    .mdb-select-arrow {
      height: 30px !important;
      line-height: 30px !important;
      width: 30px !important;
    }
  }

  .single .value {
    font-size: 0.75rem !important;
  }


  label:not(.active) {
    font-weight: 500;
    color: #bbb;
    font-size: 0.75rem !important;
  }

}

mdb-select-dropdown .dropdown-content li > a, mdb-select-dropdown .dropdown-content li > span {

  color: var(--inputTextColour) !important;
  height: 20px;
  line-height: 20px;
  font-weight: 400;

}

.on-background {
  .md-form input[type=text]:focus:not([readonly]) + label {
    background-color: var(--bgColour) !important;
  }

  mdb-select.mdb-select-outline > label.active {
    background-color: var(--bgColour) !important;
  }
}

mdb-select.mdb-select-outline > label.active {

  top: var(--floatLabelTop) !important;
  left: var(--floatLabelLeft) !important;
  font-size: var(--floatLabelFontSize) !important;
  position: absolute !important;
  transform: none !important;

}

.heavy-rain-gradient {

  background-image: none;

}

mdb-select, mdb-select-2, mdb-date-picker {

  position: relative;

  .md-form {

    .form-control {

      padding: 0.7rem 0 0.5rem 12px !important;

    }

    .datepicker-icon {
      padding: 6px 12px;
      background-color: #eee;
      right: 1px;
      top: 1px;
      line-height: 31px;
      border-left: 1px solid var(--softInputBorderColour);
    }


    label:not(.form-check-label) {
      top: var(--floatLabelTop) !important;
      left: var(--floatLabelLeft) !important;
      font-size: var(--floatLabelFontSize) !important;
      font-weight: var(--floatLabelWeight) !important;
      color: var(--floatLabelColour) !important;
      background-color: var(--floatLabelBackgroundColour) !important;
      padding-left: var(--floatLabelPadding) !important;
      padding-right: var(--floatLabelPadding) !important;
    }
  }

  &.ng-invalid, &.ng-invalid .md-form {

    input:not(:focus) {

      border: 1px solid var(--errorColour) !important;

      & + label {

        color: var(--errorColour) !important;

      }

    }
  }


  &:focus-within {
    label {
      color: var(--floatInputFocusLabelColour) !important;
    }

    .below > .form-control {
      /*
      box-shadow: inset 0 0 0 1px var(--floatInputFocusBorderColour) !important;
      border: none !important;
       */
      box-shadow: none !important;
      border-color: var(--floatInputFocusBorderColour) !important;
    }
  }

  & .mdb-select-label {

    top: 13px;

    &.focused {
      color: var(--floatInputFocusLabelColour) !important;
    }
  }

  & .mdb-select-value.focused {
    box-shadow: inset 0 0 0 1px var(--floatInputFocusBorderColour) !important;
  }


  & .placeholder, .mdb-select-placeholder {

    color: #bbb !important;
    font-weight: 500 !important;

  }

  & > label.active {

    font-size: 0.8rem !important;
    font-weight: 500 !important;
    background-color: #fff !important;
  }

  .single .value, .mdb-select-value {
    padding-left: 12px !important;
    line-height: 1.5rem !important;
    font-size: 1rem !important;
  }

  &.inline {

    .mdb-select-value {
      padding-top: 0.375rem !important;
      padding-bottom: 0.375rem !important;
    }
  }

  &:not(.inline) {

    .single, .mdb-select-value {

      padding-top: 0.7rem !important;
      padding-bottom: 0.5rem !important;
    }

    .mdb-select-arrow {
      height: 45px !important;
      line-height: 45px !important;
      width: 36px !important;
    }
  }

  & *:not(.fas) {
    font-family: var(--font1) !important;
  }

  & > div > div.single {

    padding-top: 0.375rem !important;
    padding-bottom: 0.35rem !important;
    line-height: 1.5rem !important;
    font-size: 1rem !important;

    & > div.value {
      color: var(--inputTextColour) !important;
    }
  }
}

.mdb-select-arrow, .mdb-select-toggle {

  border-left: 1px solid var(--softInputBorderColour) !important;
  width: 36px !important;

  &::before {
    content: '\f107' !important;
    font-weight: 900;
    font-family: var(--fontAwesome), serif !important;
    display: inline-block;
    color: var(--softInputBorderColour) !important;
    width: 30px;
    height: 30px;
    font-size: 16px;
  }
}

/* Multi-select side arrow */
.mdb-select-arrow {

  right: 0 !important;
  top: 0 !important;
  line-height: 30px;

}

/* Single-select side arrow */
.mdb-select-toggle {

}


/* MDB CARD */

mdb-card-title {

  background-color: var(--colour1);
  color: var(--colour3);

}

.select-form {

  .mdb-select-outline {
    margin-bottom: 1.7rem;
  }

}

/* MDB CHECKBOX */

.tick-cross {

  .form-check-input[type="checkbox"]:checked + label:before, label.btn input[type="checkbox"]:checked + label:before {

    /* Tick */
    content: "\f00c" !important;
    margin-top: 0 !important;
    left: 0 !important;
    top: 0 !important;
    transform-origin: 50% 50% !important;
    transform: rotate(0deg) !important;
    color: var(--successColour) !important;
  }

  .form-check-input[type=checkbox] + label:before, .form-check-input[type=checkbox]:not(.filled-in) + label:after, label.btn input[type=checkbox] + label:before, label.btn input[type=checkbox]:not(.filled-in) + label:after {
    /* Cross */
    content: "\f00d" !important;
    font-weight: var(--fas);
    font-family: var(--fontAwesome);
    border: 1px solid var(--inputBorderColour) !important;
    width: 38px !important;
    height: 38px !important;
    margin-top: 0 !important;
    text-align: center;
    line-height: 36px;
    font-size: 26px;
    border-radius: 3px !important;
    display: inline-block !important;
    color: var(--dangerColour) !important;
  }

}

/* MDB MODAL */

.modal-header {

  background-color: var(--colour1);
  color: var(--colour3);

  &.narrow {

    padding: 0.5rem 0.5rem !important;

    h5 {
      font-size: 1rem;
    }

  }

}

.x-point .tooltip-inner {
  max-width: 300px !important;
  white-space: nowrap;
  font-size: 0.875rem;
}

.tooltip-smaller {
  .tooltip-inner {
    font-size: 0.75rem !important;
  }
}

.side-nav {

  .slim.side-nav {
    width: 3rem !important;
  }

  z-index: 1000 !important;

  margin-top: var(--totalHeaderHeight) !important;
  width: var(--searchMenuWidth) !important;
  box-shadow: 0 2px 5px 0 rgb(0 0 0 / 16%) !important;
  background-color: var(--sideNavBackgroundColour) !important;

  .nav-link {
    padding-bottom: 4px;
    padding-top: 4px;
    height: 34px !important;
    line-height: 24px !important;
  }

  a, a:visited {
    color: var(--sideNavTextColour) !important;

    &:hover {
      background-color: var(--sideNavHoverBackgroundColour) !important;
      color: var(--sideNavHoverTextColour) !important;
    }
  }
}

.nav-link {

  .fa-user {
    color: var(--topbarIconColour);
    font-size: 20px;
    margin-top: 1px;
  }
}

.dropdown-menu {

  background-color: var(--dropdownItemBackgroundColour);
  color: var(--dropdownItemTextColour) !important;


  & a:hover {
    background-color: var(--dropdownHoverItemBackgroundColour);
    color: var(--dropdownHoverItemTextColour) !important;
  }
}

.tiny-input {

  .background-input.form-control {
    border-color: var(--softInputBorderColour) !important;
  }

  .input-group-prepend {
    .input-group-text {
      min-width: 60px;
      text-align: center;
      display: inline-block;
      font-size: 0.875rem !important;
    }
  }

  .input-group-text, .form-control, .input-group-append .btn {
    padding: 2px 5px !important;
    height: inherit;
    font-size: 0.875rem;
  }
}

/* MDB TABS */

.classic-tabs {


  .container-fluid {

    padding-left: 0 !important;
    padding-right: 0 !important;

  }

  .tab-content {
    margin: 0 !important;
    box-shadow: none;
    padding: 0 !important;
  }

  .nav.classic-tabs {
    margin: 0 0 7px 0 !important;
  }

  .nav {

    li {
      a {
        color: var(--colour5) !important;
        padding: 5px 15px 3px 5px !important;
        text-transform: none !important;
        font-size: 15px !important;
        border-bottom-color: rgba(0, 0, 0, 0.1) !important;
        border-bottom-width: 2px !important;
        border-bottom-style: solid;
        font-weight: 600;
        line-height: 23px;

        .badge {
          font-size: 100% !important;
        }
      }

      a.active {

        border-bottom-width: 2px !important;
        border-bottom-color: var(--tabFocusColour) !important;
        box-sizing: border-box !important;
        -moz-box-sizing: border-box !important;
        -webkit-box-sizing: border-box !important;
        font-weight: 600;
      }
    }

    li:first-child {
      margin-left: 0 !important;
    }
  }
}

.dropdown.remarq-dropdown .dropdown-menu {

  background-color: var(--dropdownBackgroundColour) !important;
  padding: 0 0 0 0 !important;
  position: absolute;
  box-shadow: 0 8px 8px 0 rgba(0, 0, 0, 0.3);

  .dropdown-item {

    padding-left: 1rem !important;
    padding-right: 1rem !important;
    font-size: 0.9rem;
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;

    margin-top: 0px !important;
    margin-bottom: 0px !important;

    background-color: var(--dropdownItemBackgroundColour) !important;
    color: var(--dropdownItemTextColour) !important;

    &:hover {
      background-color: var(--dropdownHoverItemBackgroundColour) !important;
      color: var(--dropdownHoverItemTextColour) !important;
      text-decoration: none !important;
      box-shadow: none !important;
    }
  }
}

.dropdown, .dropleft, .dropright, .dropup-material {

  &.remarq-dropdown .dropdown-menu.dropdown-primary .dropdown-item {

    &.active, &:hover, &:active {
      color: var(--dropdownHoverItemTextColour) !important;
      background-color: var(--dropdownHoverItemBackgroundColour) !important;
      box-shadow: none !important;
    }
  }
}

/* GHOST LOADING */

@import '../../../assets/utils/ghosts';

/*
.ghost {

  overflow: hidden;

  .pair-value, .ghost-waiting {

    display: inline-block;
    background-color: rgba(240, 240, 240, 0.714);
    border-radius: 4px 4px 4px 4px;
    @include animate-ghost-line;
    width: 99%;
  }

  .pair-value {

    margin-top: 0;
    overflow-y: hidden;
    min-height: 16px;
    padding-left: 5px;

  }
}

 */

.close {
  color: var(--dialogCloseIconColour) !important;
}

.btn-primary, .bg-primary, .btn.btn-primary {

  background-color: var(--primaryButtonColour) !important;
  border-color: var(--primaryButtonColour) !important;
  color: var(--colour3);
  font-weight: 700 !important;

  &:focus {
    background-color: var(--primaryButtonColour) !important;
  }
}

.btn-success {
  &:focus {
    background-color: var(--successColour) !important;
  }
}

.btn-primary-outline, .btn-outline-primary, .bg-primary-outline {

  border: 1px solid var(--primaryColour) !important;
  border-radius: 3px;
  color: var(--primaryColour) !important;

  &:hover, &:active, &:focus {

    background-color: var(--primaryColour) !important;
    color: var(--colour3) !important;

  }
}

.btn-secondary, .bg-secondary, .badge.bg-secondary {
  background-color: var(--secondaryButtonColour) !important;
  border-color: var(--secondaryButtonColour) !important;
  color: var(--colour3);
}

.btn-link, .btn-tertiary {
  background-color: var(--linkColour) !important;
  border-color: var(--linkColour) !important;
  color: var(--colour3);

  &:hover, &:visited, &:active {
    color: var(--colour3) !important;
    opacity: 0.9 !important;
  }
}

.btn-secondary-outline, .btn-outline-secondary, .bg-secondary-outline {
  border: 1px solid var(--secondaryColour) !important;
  border-radius: 3px;
  color: var(--secondaryColour) !important;

  &:hover, &:active, &:focus {
    background-color: var(--secondaryColour) !important;
    opacity: 0.9 !important;
    color: var(--colour3) !important;
  }
}

.btn-tertiary-outline, .btn-outline-tertiary, .bg-tertiary-outline {
  border: 1px solid var(--linkColour) !important;
  color: var(--linkColour) !important;
  border-radius: 3px;

  &:hover, &:active, &:focus {
    background-color: var(--linkColour) !important;
    color: var(--colour3) !important;
  }
}

// Allows badges in mdb-tabs to be vertically aligned
.badger {

  &.bg-badge-color {
    background-color: var(--primaryButtonColour);
    color: #fff;

    &.active {
      background-color: var(--secondaryButtonColour);
    }
  }

  margin-top: 1px !important;
  line-height: 22px;
  border-radius: 11px !important;
  min-width: 22px !important;
  min-height: 22px !important;
  padding: 0 7px !important;
  text-align: center !important;
  vertical-align: top !important;


}

.hideZero0 {
  display: none !important;
}


.btn-danger {
  background-color: var(--danger) !important;
  border-color: var(--danger) !important;
}

.btn-danger-outline, .btn-outline-danger {
  border: 1px solid var(--danger) !important;
  color: var(--danger);
  border-radius: 2px;

  &:hover, &:active, &:focus {

    background-color: var(--danger) !important;
    color: var(--colour3) !important;

  }
}

.btn-inline-colour {

  background-color: #e9ecef;
  border: 1px solid var(--softInputBorderColour);
  color: var(--textColour);

  &:hover {
    background-color: rgba(0, 0, 64, 0.1);

  }

}

.action-fa-colour {

  color: var(--secondaryButtonColour);
}

.form-control.input-feint {
  border: 1px solid #ced4da !important;
}

.btn-feint {
  background-color: var(--feintColour);
  border-color: var(--feintColour);
  color: var(--colour23);
}

.btn-xs {
  padding: 0.1rem 0.25rem !important;
  font-size: 0.8rem;
}

/* TABLES */

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(240, 241, 250, 0.5) !important;
}

.table-striped tbody tr:hover {
  background-color: rgba(230, 231, 240, 0.5) !important;
}

table td, table.table-compressed td {

  font-size: 0.8rem;
  line-height: 1.5rem;
  cursor: pointer;
  font-weight: 400;

  .table-line-1 {
    font-weight: 600;
    line-height: 0.9rem;
    font-size: 0.8rem;
  }

  .table-line-2 {
    font-weight: 300;
    line-height: 0.9rem;
    font-size: 0.75rem;
    color: #666;
  }
}

h1 {
  letter-spacing: -1px;
  font-size: 18px;
  font-weight: 600;
  color: var(--headerColour);
}

h2, .sub-header {
  font-size: 14px;
  font-weight: 500;
}

h3 {
  font-size: 14px;
  font-weight: 400;
}

.round-button {

  color: var(--colour7);
  cursor: pointer;

  &:hover {
    opacity: 0.9;
  }
}

.modal-background {
  background-color: #f8f8f8;
}

.modal-body .form-control {
  background-color: var(--colour3) !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-text-fill-color: var(--inputTextColour) !important;
  -webkit-box-shadow: 0 0 0 1000px var(--inputBackgroundColour) inset !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
  border-width: 0 !important;
  border-color: transparent !important;
}

input.form-control::placeholder {

  color: var(--placeholderColour) !important;
  font-weight: var(--placeholderWeight) !important;

}


body, table, .table {
  color: var(--textColour);
}

.vrm-style {
  background-color: var(--vrmBackgroundColour) !important;
  font-weight: 700;
}

.table th {
  border-top: 0 !important;
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.6rem;
  color: var(--textLabelColour);
}

html, body, app-root {
  height: 100%;
  width: 100%;
  margin: 0;
}

body {
  background-color: var(--bgColour) !important;
  font-family: var(--defaultFont);
  font-weight: 300;
}

a, a:visited, .link-style {
  color: var(--linkColour) !important;
  cursor: pointer;

  &.link-style-danger {
    color: var(--dangerColour) !important;
  }

  &.link-style-basic {
    color: var(--textColour) !important;
  }

  &.link-style-action {
    color: var(--actionColour) !important;
  }
}

.form-control {

  border: 1px solid var(--inputBorderColour) !important;
  border-radius: 3px !important;

  &:focus {
    border: 1px solid var(--colour7) !important;
    box-shadow: none !important;
    -webkit-box-shadow: none;
  }
}


/* FOR AUTO FILL FUNNY BUSINESS */
.md-form > input[type]:-webkit-autofill:not(.browser-default):not([type=search]) + label, .md-form > input[type=time]:not(.browser-default) + label {
  display: inline-block !important;
  width: auto !important;
  //transform: translateY(-7px) translateX(6px) scale(0.8) !important;
  position: relative;
  top: var(--floatLabelTop);
  left: var(--floatLabelleft);
  font-size: var(--floatLabelFontSize);
  font-weight: var(--floatLabelWeight);
}


.pair-label {

  text-transform: uppercase;
  font-weight: 500;
  font-size: 0.7rem;
  color: var(--textLabelColour);
}

app-vehicle-valuation {

  .pair-value {

    font-size: 0.75rem;

  }

}

.ticklist {
  &:before {
    content: "\f00c"; /* FontAwesome Unicode */
    font-family: var(--fontAwesome), serif;
    display: inline-block;
    margin-left: -1.3em; /* same as padding-left set on li */
    width: 1.3em; /* same as padding-left set on li */
  }
}

.cursor-pointer {
  cursor: pointer;
}

.widget {


  background-color: var(--widgetBgColour);
  border-radius: var(--widgetBorderRadius);
  border: 1px solid var(--widgetBorderColour);

  &.widget-no-border {

    border: 0;
  }

  &.inline {
    display: inline-block;
  }

  &.padding {
    padding: 0.5rem 0.5rem;
  }

  &.form-padding {
    padding: 1.5rem 1rem;
  }

  &.widget-padding {
    padding: 1.5rem 1.5rem;
  }

  &.side-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .small-header {

    font-size: 0.9rem;
    font-weight: 400;
  }

  .header {

    font-size: 1.1rem;
    font-weight: 600;

    &.padding-top {

      padding-top: 0.50rem;
    }

    &.padding {

      padding-bottom: 0.75rem;

    }
  }

  &.margin {
    margin-bottom: 1rem;
  }
}

.widget-label {
  font-size: 1rem;
  font-weight: 500;
}

.page-header, h1.page-header {
  line-height: 26px;
  margin-bottom: 0 !important;
}

.header-margin {

  margin-bottom: 10px;

}


/* Style "standard" bootstrap checkboxes */
.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  border-color: var(--smallCheckboxColour) !important;
  background-color: var(--smallCheckboxColour) !important;
  box-shadow: none !important;
}

.btn:focus {
  box-shadow: none !important;
}

/* BOOTSTRAP 4 MDB */

.form-check .form-check-input {
  width: 1rem;
  height: 1rem;
}

.table .no-top-line th {

  border-top: 0 !important;

}


/* END GHOST */

.badgeCount0 {
  display: none;
}


/* Google Address Auto Complete */
.pac-container {
  z-index: 1051 !important;
}

/* MDB-OPTION */

.mdb-option {
  font-size: 0.9rem !important;
  padding-left: 13px !important;
  height: 38px !important;
}

/* Hide Chat on Mobile */
@media screen and (max-width: 768px) {
  div#hubspot-messages-iframe-container {
    visibility: hidden;
  }
}

@media only screen and (min-width: 1200px) {
  .container.footer-size, .container.header-size {
    max-width: 1440px;
  }
}
