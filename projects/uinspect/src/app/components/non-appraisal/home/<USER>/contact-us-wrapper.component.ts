import {Component, ViewEncapsulation} from '@angular/core';
import {NonAppraisalAppComponent} from "../../non-appraisal-app.component";
import {DataService} from "../../../../services/index";

@Component({
  templateUrl: './contact-us-wrapper.component.html',
  styleUrls: ['./contact-us-wrapper.component.scss'],
})

export class ContactUsWrapperComponent {

  globals = NonAppraisalAppComponent.globals;

  constructor(
    data: DataService
  ) {

    data.globals = this.globals;

  }
}
