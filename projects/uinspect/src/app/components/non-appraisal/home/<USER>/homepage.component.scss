#homepage {
  background-color: #fff;
}

h2 {
  font-family: var(--font2);
}

h2.why-us {

  font-size: 34px;
  text-align: center;

}

.box-shadow {

  box-shadow: 0 12px 32px 5px rgb(1 13 130 / 12%);

}

.block-1 {
  background-color: #eee;
  height: 100%;
}

.block-2 {
  background-color: var(--colour3);
}

.block-3 {
  background-color: var(--colour18);
}

.inner-block-1 {
  height: 100%;
  width: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center right;
}

.header-block {

  display: block;
  color: var(--colour5);

  .header-line-1 {
    font-size: 48px;
    line-height: 50px;
    letter-spacing: -1px;
    font-weight: 700;
    font-family: var(--font2);
  }

  .header-line-2 {
    font-size: 62px;
    line-height: 62px;
    font-weight: 700;
    letter-spacing: -1px;
    font-family: var(--font2);
  }

  .header-line-3 {
    font-size: 48px;
    line-height: 50px;
    font-weight: 600;
    letter-spacing: -1px;
    color: var(--primary);
    font-family: var(--font2);
  }
}

.btn-sign-up {
  font-weight: 600;
  background-color: var(--primary);
  color: #fff;
  border-radius: 25px !important;
  height: 50px;
  border: none !important;

  &:hover {
    background-color: var(--colour7);
  }
}

.btn-login {
  font-weight: 600;
  height: 50px;
  border-radius: 25px !important;
  padding: 0 25px;
  background-color: #333;
  color: #fff;
  border: 1px solid #d6e9ed !important;

  &:hover {
    background-color: #0f8;
    color: #333;
  }
}

.more-info-block {

  display: block;
  height: 100%;
  background-color: #fff;
  padding: 20px 40px 0 40px;
  border-radius: 8px;
  box-shadow: 0px 12px 32px 5px rgb(1 13 130 / 12%);
  margin-bottom: 1rem;
  text-align: center;

  .info-text {
    color: #84879E;
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
  }

  div {
    padding-bottom: 6px;
  }

  h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-top: 20px;
  }
}

.upcoming-sales {

  color: var(--linkColour);
  display: inline-block;
  font-weight: 600;
}

.sales-calendar-button {

  display: inline-block;
  color: #fff;
  padding: 5px 7px;
  border-radius: 5px;
  background-color: rgba(42, 157, 143, 0.8);
  color: #fff;
}

.sale-table-wrapper {

  background-color: var(--homepageCalendarBackgroundColour);
  color: #fff;
  border-radius: 8px;
  padding: 10px 20px 10px 20px;
  box-shadow: 0px 12px 32px 5px rgb(1 13 130 / 12%);

  .sale-table {
    width: 100%;
    color: #fff;
    font-weight: 500;
    border-collapse: collapse;
    border-style: hidden;

    td {

      border-top: 1px solid rgba(255, 255, 255, 0.1);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      font-weight: 600;

    }
  }
}

.sub-header h3 {
  font-weight: 700;
}

#home-box1-widget {
  min-width: 300px;
}

@media only screen and (min-width: 1200px) {
  .container {
    max-width: 1440px;
  }
}

@media (max-width: 390px) {

  .header-line-1 {
    font-size: 55px !important;
  }
  .header-line-2 {
    font-size: 45px !important;
  }
  .header-line-3 {
    font-size: 35px !important;
  }
  .video-style {
    display: none;
  }
  .container {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

}

.col-33 {
  width: 100%;
  margin-bottom: 40px;
}

.vehicle-table {
  display: table;
  height: 100%;
  width: 100%;
  margin: 0 auto;
}

@media (min-width: 768px) {

  .inner-block-1 {
  }
}

@media (max-width: 768px) {
  .header-line-1 {
    font-size: 38px !important;
  }
  .header-line-2 {
    font-size: 50px !important;
  }
  .header-line-3 {
    font-size: 43px !important;
  }
  h2.why-us {
    font-size: 24px;
  }
}


@media (min-width: 992px) {
  .col-33 {
    width: 33.33%;
    padding: 20px;
    box-sizing: border-box;
  }
}

@media (min-width: 1200px) {
  .vehicle-table {
    width: 70%;
  }

}


@media (min-width: 768px) and (max-width: 992px) {
  .header-line-1 {
    font-size: 38px !important;
  }
  .header-line-2 {
    font-size: 50px !important;
  }
  .header-line-3 {
    font-size: 38px !important;
  }
}

@media (min-width: 992px) and (max-width: 1200px) {

  .header-line-1 {
    font-size: 40px !important;
  }
  .header-line-2 {
    font-size: 50px !important;
  }
  .header-line-3 {
    font-size: 34px !important;
  }
}

