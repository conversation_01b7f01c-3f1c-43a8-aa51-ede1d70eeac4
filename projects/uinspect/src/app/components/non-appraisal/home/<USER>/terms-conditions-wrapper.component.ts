import {Component} from '@angular/core';
import {NonAppraisalAppComponent} from "../../non-appraisal-app.component";
import {DataService} from "../../../../services/index";

@Component({
  templateUrl: './terms-conditions-wrapper.component.html',
  styleUrls: ['./terms-conditions-wrapper.component.scss'],
})

export class TermsConditionsWrapperComponent {

  globals = NonAppraisalAppComponent.globals;

  constructor(
    data: DataService
  ) {

    data.globals = this.globals;

  }
}
