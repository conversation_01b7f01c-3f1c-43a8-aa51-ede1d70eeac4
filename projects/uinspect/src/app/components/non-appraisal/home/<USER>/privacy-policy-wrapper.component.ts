import {Component} from '@angular/core';
import {NonAppraisalAppComponent} from "../../non-appraisal-app.component";
import {DataService} from "../../../../services/index";

@Component({
  templateUrl: './privacy-policy-wrapper.component.html',
  styleUrls: ['./privacy-policy-wrapper.component.scss'],
})

export class PrivacyPolicyWrapperComponent {

  globals = NonAppraisalAppComponent.globals;

  constructor(
    data: DataService
  ) {

    data.globals = this.globals;

  }
}
