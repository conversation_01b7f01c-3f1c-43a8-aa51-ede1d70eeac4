import {Component, Inject, NgZone, ViewEncapsulation} from "@angular/core";
import {DomainData, GlobalConstants} from "../../global/shared";
import {Router} from "@angular/router";
import {DOCUMENT} from "@angular/common";
import {Meta, Title} from "@angular/platform-browser";
import {LoggerService} from "../../global/services";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  encapsulation: ViewEncapsulation.None
})

export class AppComponent {

  public static globals: DomainData;

  constructor(private router: Router,
              @Inject(DOCUMENT) private document: Document,
              private titleService: Title,
              private metaService: Meta,
              private zone: NgZone,
              private logService: LoggerService
  ) {

    AppComponent.globals = GlobalConstants.getPlatformDetails(GlobalConstants.CompanyProductCode.UInspect, window.location.host);

    if (AppComponent.globals == null) {
      console.log("NAVIGATING TO NOT CONFIGURED " + window.location.host);
      this.router.navigate(["/assets/html/notConfigured.html"]);
    }

  }
}
