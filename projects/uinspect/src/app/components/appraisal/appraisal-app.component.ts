import {Component, Inject, OnInit, ViewEncapsulation, NgZone, Renderer2} from '@angular/core';
import {Router} from "@angular/router";
import {Meta, Title} from "@angular/platform-browser";
import {GlobalConstants} from "../../global/shared";
import {DomainData} from "../../global/shared/index";
import {DOCUMENT} from '@angular/common';

@Component({
  selector: 'app-root',
  templateUrl: './appraisal-app.component.html',
  styleUrls: [
    './appraisal-app.component.scss',
  ],
})

export class AppraisalAppComponent implements OnInit {

  public static globals: DomainData;

  constructor(
    private router: Router,
    private titleService: Title,
    private metaService: Meta,
    private zone: NgZone,
    @Inject(DOCUMENT) private document: Document
    //             private logService: LoggerService
  ) {

    AppraisalAppComponent.globals = GlobalConstants.getPlatformDetails(GlobalConstants.CompanyProductCode.UInspect, window.location.host);

    if (AppraisalAppComponent.globals == null) {
      this.router.navigate(["/assets/html/notConfigured.html"]);
    }

    this.theme = AppraisalAppComponent.globals.theme;
    this.platformName = AppraisalAppComponent.globals.platformName;
    this.loadCSSForTheme();
    this.setTitle();
    this.setMeta();
    this.setFavicon();

    this.document.body.classList.add("no-page-scroll");
  }

//  logger = this.logService.taggedLogger(this.constructor?.name);

  public showChatWidget = false;
  public theme = "";
  platformName: string;
  isMobile: any;
  isDevice: any;

  async ngOnInit() {
  }

  loadCSSForTheme() {

    this.loadCSS("main-variables", "main", "variables");
    this.loadCSS("client-variables", AppraisalAppComponent.globals.theme, "variables");
  }

  setTitle() {
    this.titleService.setTitle(this.platformName + " - Trading Platform");
  }

  setMeta() {
    this.metaService.addTags([
      {name: 'keywords', content: this.platformName + ",Trading,Vehicle Remarketing,Car Auction"},
      {name: 'description', content: this.platformName + " Online Vehicle Remarketing - Trading Platform"}
    ]);
  }

  setFavicon() {
    const faviconId = this.document.getElementById("favicon") as HTMLLinkElement;
    if (faviconId != null) {
      faviconId.href = "/assets/sites/" + this.theme + "/images/favicon.ico";
    }

    const appleTouchIcon = this.document.getElementById("apple-touch-icon") as HTMLLinkElement;
    if (appleTouchIcon != null) {
      appleTouchIcon.href = "/assets/sites/" + this.theme + "/images/apple-touch-icon.png";
    }

    const icon16 = this.document.getElementById("png16") as HTMLLinkElement;
    if (icon16 != null) {
      icon16.href = "/assets/sites/" + this.theme + "/images/favicon-16x16.png";
    }

    const icon32 = this.document.getElementById("png32") as HTMLLinkElement;
    if (icon32 != null) {
      icon32.href = "/assets/sites/" + this.theme + "/images/favicon-32x32.png";
    }
  }

  loadCSS(id, theme, cssType) {

    const themeLink = this.document.getElementById(id) as HTMLLinkElement;

    if (themeLink) {
      themeLink.href = (id === "main-variables" ? `../../assets/css/${cssType}.css` : `../../assets/sites/${theme}/css/${cssType}.css`);
    }
  }
}
