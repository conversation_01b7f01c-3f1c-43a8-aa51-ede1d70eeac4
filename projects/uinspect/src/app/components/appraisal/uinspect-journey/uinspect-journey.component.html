<app-network-status (statusChanged)="networkStatusChanged($event)"></app-network-status>
<div *ngIf="orientation == 'landscape'; else turnCamera">

  <div *ngIf="!loading; else spinner">

    <div *ngIf="statusCode == 0">

      <h1>Error: Could not connect to server</h1>

    </div>

    <div *ngIf="statusCode == 404">

      <h1>Error: No such Appraisal</h1>

    </div>

    <div *ngIf="statusCode == 200">

      <div style="height: 100vh; width: 100vw;" *ngIf="sectionData">

        <app-uinspect-step
          [section]="sectionData[currentSectionId]"
          [isOnline]="isOnline"
          (stepEvent)="stepEvent($event)"
          (imageCaptured)="imageCaptured($event)">
        </app-uinspect-step>

        <div class="bottom-left-buttons pl-3">
          <button class="btn btn-dark mr-2" (click)="goBack()" [disabled]="!canGoBack()">&laquo; Back</button>
          <button class="btn btn-dark" (click)="goForward()" [disabled]="!canGoForward()">Next &raquo;</button>
        </div>

        <div class="bottom-right-buttons pr-3" *ngIf="canContinue()">
          <div class="btn continue-btn" (click)="continue()">Continue &raquo;</div>
        </div>


      </div>
    </div>
  </div>
</div>

<ng-template #spinner>
  <div class="mt-5 mb-5 text-center">
    <i class="fa fa-spin fa-spinner fa-2x"></i>
  </div>
</ng-template>

<ng-template #turnCamera>
  <div style="height: 100%; align-items: center; display: flex; width: 100%;">
    <div class="text-center" style="width: 100%;">
      <div class="mb-4">
        <i class="fa fa-undo fa-2x fa-rotate-90"></i>
      </div>
      <h1>Please turn your<br/>camera to landscape</h1>
      <div class="mt-4">
        <i class="fa fa-camera fa-2x fa-rotate-90"></i>
      </div>
    </div>
  </div>
</ng-template>


