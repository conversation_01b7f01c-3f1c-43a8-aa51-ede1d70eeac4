import {ChangeDetectorRef, Component, HostListener, OnInit} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {
  UInspectAnswerService,
  UInspectMediaService,
  UInspectSectionCompleteService,
  UInspectService
} from "../../../services/index";
import {
  UInspectAnswerSyncDTO,
  UInspectMediaDTO,
  UInspectMediaSyncDTO,
  UInspectSectionCompleteSyncDTO,
  UInspectSectionSyncDTO,
} from "../../../global/interfaces";
import {UInspectSyncDTO} from '../../../global/interfaces/index';
import {MediaTypeEnum, UInspectSectionTypeEnum} from '../../../global/enums/index';
import {UInspectCommonService} from "../../../global/services";

@Component({
  selector: 'app-uinspect-journey',
  templateUrl: './uinspect-journey.component.html',
  styleUrls: ['./uinspect-journey.component.scss']
})

export class UInspectJourneyComponent implements OnInit {

//   logger = this.logService.taggedLogger(this.constructor?.name);
  statusCode: number;
  uInspectId: string;
  uInspect: UInspectSyncDTO;
  currentSectionId = 0;
  totalSteps = 0;
  loading: boolean;
  isOnline = true;
  orientation: string;

  sectionData: { [key: number]: UInspectSectionSyncDTO } = {};

  merged = false;
  private startSectionId: number;

  @HostListener('window:orientationchange', ['$event'])
  onOrientationChange(event) {
    this.checkOrientation(event);
  }

  constructor(
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
    private uInspectService: UInspectService,
    private uInspectAnswerService: UInspectAnswerService,
    private uInspectCommonService: UInspectCommonService,
    private uInspectMediaService: UInspectMediaService,
    private uInspectSectionCompleteService: UInspectSectionCompleteService,
    //    private logService: LoggerService
  ) {
  }

  async ngOnInit() {

    this.uInspectId = this.route.snapshot.params.uInspectId;

    await this.fetchUInspect(this.uInspectId);

    this.currentSectionId = this.nextSection(true);
    this.checkOrientation(event);
  }

  async fetchUInspect(uInspectId) {

    this.loading = true;

    await this.uInspectService.get(uInspectId, {component: "uinspect-journey"})
      .then((x) => {

        this.statusCode = 200;
        this.uInspect = x.dto;
        this.sectionData = {};
        this.startSectionId = this.uInspect.uInspectFormat.startSectionId;

        this.uInspect.uInspectFormat.sections.forEach((z) => {
          this.sectionData[z.id] = z;
        });

      })
      .catch((err) => {
        this.statusCode = err.status;
      })
      .finally(() => {
        this.loading = false;
      });
  }


  networkStatusChanged(status: boolean) {
    this.isOnline = status;
  }

  private checkOrientation(event) {

    // TEMPORARY FIX.. REMOVE AFTER INCHCAPE DEMO
    this.orientation = "landscape";

    return;

    // Chrome/Edge
    let orientation = screen.orientation.type.split("-")[0];

    // Required for Safari
    if (orientation == null) {
      orientation = window.matchMedia("(orientation: portrait)") ? "portrait" : "landscape";
    }

    this.orientation = orientation;
  }

  async imageCaptured(syncData: UInspectMediaSyncDTO) {

    console.log("IMAGE CAPTURED");

    // UPLOADED SECTION MEDIA:
    if (syncData.uInspectSectionId != null) {

      let currentSection = this.sectionData[syncData.uInspectSectionId];

      let objectToReplace = currentSection
        .medias.find(x => x.position === syncData.position);

      if (objectToReplace) {
        console.log("Replacing Section Media with syncData", syncData);
        Object.assign(objectToReplace, syncData);
      } else {
        console.log("Pushing Section Media", syncData);
        currentSection.medias.push(syncData);
      }
    }

    // UPLOADED QUESTION MEDIA
    if (syncData.uInspectQuestionId != null) {

      const currentSection = this.sectionData[syncData.uInspectSectionId];

      const currentQuestion = currentSection.questions.find(x => x.id === syncData.uInspectQuestionId);

      const objectToReplace = currentQuestion
        .medias.find(x => x.position === syncData.position);

      if (objectToReplace) {
        console.log("Replacing Question Media");
        Object.assign(objectToReplace, syncData);
      } else {
        console.log("Pushing Question Media");
        currentQuestion.medias.push(syncData);
      }
    }

    this.syncData();

    this.nextSection(true);
  }

  goBack() {
    this.currentSectionId = this.previousSection();
  }

  goForward() {
    this.currentSectionId = this.nextSection(false);
  }

  continue() {

    this.setSectionComplete(this.currentSectionId);

    this.currentSectionId = this.nextSection(true);

    console.log("CURRENT SECTION ID ", this.currentSectionId);
  }

  previousSection() {

    const [nextSectionId, seenSections] = this.uInspectCommonService.walkTree(this.startSectionId, this.sectionData, [], true);

    const currentOffset = seenSections.indexOf(this.currentSectionId);

    return seenSections[currentOffset - 1];
  }

  nextSection(stopAtUnanswered: boolean = true): number {

    const [nextSectionId, seenSections] = this.uInspectCommonService.walkTree(this.startSectionId, this.sectionData, [], stopAtUnanswered);

    if (stopAtUnanswered) {

      // console.log("STOPPED AT " + nextSectionId);
      return nextSectionId;
    }

    const currentOffset = seenSections.indexOf(this.currentSectionId);


    return seenSections[currentOffset + 1];
  }

  setSectionComplete(sectionId) {

    let section = this.sectionData[sectionId];

    if (section != null && section?.sectionComplete?.complete !== true) {
      section.sectionComplete = {complete: true, synced: false, uInspectSectionId: section.id, uInspectId: this.uInspectId};

      // EMIT A REFRESH OF THE SeenSections
    }

    // SAVE SECTION DATA TO LOCAL STORAGE + THEN RESTORE ON REFRESH

    this.syncData();
  }

  syncData() {

    if (!this.isOnline) {
      return;
    }

    console.log("SYNC ", this.sectionData);

    for (const [key, section] of Object.entries(this.sectionData)) {

      // SYNC Section
      if (section.synced === false) {
        console.log("HAVE SECTION UN-SYNCED");
      }

      if (section.sectionComplete?.synced === false) {
        // Why are we setting the sync to complete if we aren't sure it is complete ?

        this.syncComplete(section.sectionComplete);
      }

      // SYNC SECTION MEDIA
      section?.medias?.forEach(async (media) => {

        if (media.synced === false) {

          this.syncMedia({
            uInspectId: this.uInspectId,
            id: media.id,
            uInspectSectionId: section.id,
            position: media.position,
            image: media.image
          }).then((dto) => {

            if (dto != null) {

              media.synced = true;
              media.id = dto.id;

              if (section.mapToDocumentCategoryId && section.mediaType == MediaTypeEnum.Image) {
                // call API to link to create a document linked to the mediaId
                this.uInspectMediaService.createLeadDocument( {
                  uInspectId: this.uInspectId,
                  leadDocumentCategoryId: section.mapToDocumentCategoryId,
                  uInspectMediaId: media.id
                });
              }

              /*
              const x = this.currentSectionId;
              this.cd.detectChanges();
              this.currentSectionId = null;
              this.cd.detectChanges();
              this.currentSectionId = x;
              console.log("SD ", this.sectionData);
               */
            }
          });
        }
      });

      // ITERATE QUESTIONS
      section?.questions?.forEach(async (question) => {

        // SYNC ANSWER
        if (question?.answer?.synced === false) {

          const synced = await this.syncAnswer({
            uInspectId: this.uInspectId,
            uInspectQuestionId: question.id,
            answer: question.answer.answer,
            answerOptionId: question.answer.answerOptionId,
          });

          if (synced) {
            question.answer.synced = true;
            section.synced = true;
          }
        }

        question?.medias?.forEach((media) => {

          if (media.synced === false) {
            // Emit update ??
          }
        });
      });
    }
  }

  syncMedia(syncDTO: UInspectMediaSyncDTO): Promise<UInspectMediaSyncDTO> {

    if (this.isOnline) {

      const dto = {
        uInspectId: this.uInspectId,
        id: syncDTO.id,
        uInspectSectionId: syncDTO.uInspectSectionId,
        uInspectQuestionId: syncDTO.uInspectQuestionId,
        position: syncDTO.position,
      } as UInspectMediaDTO;

      // TODO: IMPLEMENT BACK-OFF

      return this.uInspectMediaService.uploadMedia(dto, [], [syncDTO.image]).then(res => {

        if (res != null) {

          const uploadResult = res[0];

          if (uploadResult.isValid) {

            return Promise.resolve(uploadResult.dto);

          } else {
            // FLAG AN ISSUE
          }
        }
      }).catch(err => {
        // FLAG SYNC ISSUE
//        this.logger.error(err);
      });
    }

    return Promise.resolve(null);
  }

  syncComplete(syncDTO: UInspectSectionCompleteSyncDTO) {

    if (this.isOnline) {

      return this.uInspectSectionCompleteService.put(syncDTO).then(res => {

        if (res.isValid) {
          // SET TO SYNCED
          syncDTO.synced = true;
        } else {
          // FLAG AN ISSUE
        }
      }).catch(err => {
        // FLAG SYNC ISSUE
//        this.logger.error(err);
      });
    }
  }

  async syncAnswer(syncDTO: UInspectAnswerSyncDTO): Promise<boolean> {

    let success = false;

    if (this.isOnline) {

      await this.uInspectAnswerService.put(syncDTO).then(res => {

        success = res.isValid;

      }).catch(err => {


      });
    }

    return success;
  }

  stepEvent(event: { action?: string, questionId?: number, value?: string }) {

    if (event.action === "previous") {
      this.previousSection();
    }

    // THIS IS NOT THE SAME AS FORWARD
    if (event.action === "next") {
      this.nextSection();
    }

    // THIS IS NOT THE SAME AS NEXT
    if (event.action === "continue") {
      this.nextSection(true);
    }

    if (event.action === "setAnswer") {
      this.setAnswer(event);
    }
  }

  private setAnswer(event) {

    let question = this.sectionData[event.sectionId].questions.find(x => x.id === event.questionId);

    if (question != null) {

      if (question.answer == null) {
        question.answer = {};
      }

      if (question.options.length > 0) {
        question.answer.answerOptionId = event.option.id;
      } else {
        question.answer.answer = event.value;
      }
      question.answer.synced = event.synced;
    }

    this.syncData();
  }

  canContinue() {

    const section = this.sectionData[this.currentSectionId];

    if (section == null) {
      return false;
    }

    if (section.sectionType == UInspectSectionTypeEnum.Question) {
      if (
        section.questions[0].answer?.answerInt ||
        section.questions[0].answer?.answerBool ||
        section.questions[0].answer?.answerDate ||
        section.questions[0].answer?.answer ||
        section.questions[0].answer?.answerOptionId) {
        return true;
      }
    }

    if (section.sectionType == UInspectSectionTypeEnum.Page && section.nextSectionId > 0) {
      return true;
    }

    if (section.sectionType == UInspectSectionTypeEnum.Media) {

      if (section.medias.length >= section.minMedia) {
        return true;
      }
    }

    return false;

  }

  canGoBack() {
    const previousSection = this.previousSection();
    return previousSection != null;
  }

  canGoForward() {
    const nextSection = this.nextSection(true);
    return (nextSection != null && nextSection !== this.currentSectionId);
  }
}
