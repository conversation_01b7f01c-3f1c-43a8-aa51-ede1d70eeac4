import {Component, EventEmitter, <PERSON><PERSON><PERSON>roy, OnInit, Output} from '@angular/core';
import {fromEvent, merge, of, Subscription} from 'rxjs';
import {map} from 'rxjs/operators';

@Component({
  selector: 'app-network-status',
  templateUrl: './network-status.component.html'
})
export class NetworkStatusComponent implements OnInit, OnDestroy {

  constructor() {
  }

  @Output() statusChanged: EventEmitter<boolean> = new EventEmitter<boolean>();

  networkStatus = false;
  networkStatus$: Subscription = Subscription.EMPTY;

  ngOnInit(): void {
    this.checkNetworkStatus();
  }

  ngOnDestroy(): void {
    this.networkStatus$.unsubscribe();
  }

  checkNetworkStatus() {
    this.networkStatus = navigator.onLine;
    this.networkStatus$ = merge(
      of(null),
      fromEvent(window, 'online'),
      fromEvent(window, 'offline')
    )
      .pipe(map(() => navigator.onLine))
      .subscribe(status => {
        this.networkStatus = status;

        // raise event to signal status change
        this.statusChanged.emit(this.networkStatus);
      });
  }

}
