<div style="height: 100%; width: 100%;" *ngIf="section">
  <ng-container [ngTemplateOutlet]="image" *ngIf="section.sectionType == sectionTypeEnum.Media"></ng-container>
  <ng-container [ngTemplateOutlet]="page" *ngIf="section.sectionType == sectionTypeEnum.Page"></ng-container>
  <ng-container [ngTemplateOutlet]="question" *ngIf="section.sectionType == sectionTypeEnum.Question"></ng-container>
</div>

<ng-template #page>

  <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%;">
    <div class="page page-{{ section.id }}">
      <h1>{{ section.title}}</h1>
      <h3>{{ section.explanation}}</h3>
    </div>
  </div>

</ng-template>

<ng-template #question>

  <div class="question-parent">
    <div class="question-inner">

      <div class="section-title">{{ section.title }}</div>
      <div class="question-text" *ngIf="section?.questions.length == 0">{{ section.explanation }}</div>
      <div class="question-text" >{{ section?.questions[0]?.questionText }}</div>

      <div *ngIf="currentQuestion?.options?.length > 0; else noOptions" class="options">

        <div class="px-3">

          <div class="btn btn-block question-btn"
               *ngFor="let option of sortedOptions(currentQuestion.options)"
               [class.selected]="option.id == currentQuestion.answer?.answerOptionId"
               (click)="setOption(currentQuestion, option)">{{ option.optionLabel }}</div>
        </div>

      </div>

      <ng-template #noOptions>
        <input [(ngModel)]="currentQuestion.answer.answer"
                class="text-input mt-3"
               (ngModelChange)="setFreeText(currentQuestion, currentQuestion.answer?.answer)">
      </ng-template>

    </div>
  </div>

</ng-template>

<ng-template #image>
  <div style="height: 100%; width: 100%; display: grid; background-color: #000 " *ngIf="section">


    <div style="
      position: relative;
      height: 100vh;
      aspect-ratio: 4/3; background-color: #000;
      align-self: center;
      justify-self: center;">

      <div class="section-title" [mdbTooltip]="section.explanation">
        {{ section.title }} : {{ section.explanation }}
      </div>

      <div *ngIf="!takePhoto">

        <img src="{{ currentPhoto }}" alt="" style="width: 100%; height: 100%;"/>

        <div class="retake-photo">
          <button class="btn btn-danger mr-2" (click)="retakePhoto()">Re-Take Photo</button>
        </div>

      </div>

      <div *ngIf="takePhoto" style="height: 100%;">

        <div style="position: relative; width: 100%; height: 100%;">
          <div class="overlay-image" style="background-image: url({{ uInspectURL.placeHolderURL(section.placeholderURL, true) }});"
               *ngIf="section.placeholderURL != ''"></div>

          <webcam (initError)="handleInitError($event)"
                  [trigger]="triggerObservable" [style]="{'width':'100%','height':'100%'}"
                  (imageCapture)="captureImg($event)">
          </webcam>


        </div>

        <div class="take-photo" style="position: absolute; bottom: 15px; right: 10px; z-index: 99999999">
          <div class="take-photo-button" (click)="getSnapshot()" style="display: flex; align-content: center; align-items: center;">
            <div>Take Photo</div>
          </div>
        </div>


        <div class="upload-photo" style="position: absolute; bottom: 15px; right: 100px; z-index: 99999999;">
          <div class="upload-photo-button" (click)="fileUpload.click()" style="display: flex; align-content: center; align-items: center;">
            <div>Upload Photo</div>
          </div>
        </div>
        <input type="file" class="file-input" (change)="onFileSelected($event)" #fileUpload style="display: none;"/>

      </div>
    </div>
  </div>
</ng-template>
