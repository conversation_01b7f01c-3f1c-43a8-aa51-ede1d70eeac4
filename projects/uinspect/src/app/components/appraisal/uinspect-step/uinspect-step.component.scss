.img {
  border-radius: 3px;
  width: 60px;
  height: 40px;
  background-size: cover;
  background-position: center center;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  font-family: arial;
  position: absolute;
  top: 0px;
  padding-top: 5px;
  padding-left: 10px;
  padding-right: 10px;
  left: 0px;
  text-align: center;
  width: 100%;
  color: #fff;
  z-index: 9999;
  background-color: rgba(0,0,0,0.3);
}

.text-input {
  font-size: 23px;
  border: 1px solid #333;
  border-radius: 5px;
  padding: 2px 10px;
}

.retake-photo {
  position: absolute;
  bottom: 10px;
  z-index: 999999;
  width: 100%;
  text-align: center;
}

.overlay-image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
  background-position: center center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99999;
  background-size: contain;
  background-repeat: no-repeat;
}

.question-parent {

  text-align: center;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .question-inner {
    padding-bottom: 45px;
    width: 100%;
  }

  .question-text {
    font-weight: 600;
    font-size: 21px;
    color: #666;
  }

  .options {
    padding-top: 10px;

    .selected {
      background-color: #E27D5F;
    }
  }

}

.question-btn {
  color: #fff;
  background-color: #7DC2AF
}

.page {

  text-align: center;
  padding: 10px;

  background-color: #3AA99E;
  border-radius: 3px;

  h1, h3 {
    color: #fff;
  }

}

.take-photo-button {

  background-color: #fff;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: inline-block;
  border: 2px solid rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 0 2px white;
  font-size: 12px;
  line-height: 13px;
  text-align: center;
}

.upload-photo-button {

  background-color: #888;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: inline-block;
  border: 2px solid rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 0 2px #888;
  color: #ccc;
  font-size: 12px;
  line-height: 13px;
  text-align: center;

}
