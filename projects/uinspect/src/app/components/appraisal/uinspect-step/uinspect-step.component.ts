import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {WebcamImage, WebcamInitError} from 'ngx-webcam';
import {Observable, Subject} from 'rxjs';
import {UInspectURLService} from "../../../services/index";
import {
  UInspectMediaSyncDTO,
  UInspectQuestionOptionDTO,
  UInspectQuestionSyncDTO,
  UInspectSectionSyncDTO
} from "../../../global/interfaces";
import {StatusEnum, UInspectSectionTypeEnum} from '../../../global/enums/index';

@Component({
  selector: 'app-uinspect-step',
  templateUrl: './uinspect-step.component.html',
  styleUrls: ['./uinspect-step.component.scss']
})
export class UInspectStepComponent implements OnInit {

  currentPhoto: string;
  private currentMedia: { image: string; id: string };
  private replaceMediaId: string;

  constructor(
    // private logService: LoggerService,
    public uInspectURL: UInspectURLService) {
  }

  // tslint:disable-next-line:variable-name
  _section: UInspectSectionSyncDTO;
  uInspectMedias: UInspectMediaSyncDTO[];
  takePhoto: boolean;

  get section() {
    return this._section;
  }

  @Input('section') set setSection(value: UInspectSectionSyncDTO) {
    this._section = value;
    this.replaceMediaId = null;
    this.showSection(value);
  }

  @Input() isOnline: boolean;
  @Output() imageCaptured: EventEmitter<UInspectMediaSyncDTO> =
    new EventEmitter<UInspectMediaSyncDTO>();

  @Output() stepEvent: EventEmitter<object> = new EventEmitter<object>();

  // logger = this.logService.taggedLogger(this.constructor?.name);

  // image capture vars
  private trigger: Subject<any> = new Subject();
  public webcamImage!: WebcamImage;
  private nextWebcam: Subject<any> = new Subject();
  sectionTypeEnum = UInspectSectionTypeEnum;
  currentQuestion: UInspectQuestionSyncDTO;
  reviewMode = false;

  ngOnInit(): void {
  }

  public getSnapshot(): void {
    this.trigger.next(void 0);
    this.takePhoto = false;
  }

  public get triggerObservable(): Observable<void> {
    return this.trigger.asObservable();
  }

  public get nextWebcamObservable(): Observable<any> {
    return this.nextWebcam.asObservable();
  }

  public handleInitError(error: WebcamInitError): void {
    if (error.mediaStreamError && error.mediaStreamError.name === "NotAllowedError") {
      console.warn("Camera access was not allowed by user!");
    }
  }

  public showSection(section: UInspectSectionSyncDTO) {

    if (section != null) {
      this.uInspectMedias = section?.medias;

      if (section.sectionType === UInspectSectionTypeEnum.Media) {
        this.takePhoto = (section?.medias?.length < section.minMedia);

        this.currentMedia = {
          id: section.medias[0]?.id,
          image: section?.medias[0]?.image || section?.medias[0]?.mediaURL
        };

        this.currentPhoto = section?.medias[0]?.image || section?.medias[0]?.mediaURL;
      }

      if (section.sectionType === UInspectSectionTypeEnum.Question) {
        this.currentQuestion = section?.questions[0];

        if (this.currentQuestion.answer == null) {
          this.currentQuestion.answer = {};
        }
      }
    }
  }

  public captureImg(webcamImage: WebcamImage): void {

    this.webcamImage = webcamImage;
    const sysImage = webcamImage?.imageAsDataUrl;
    this.currentPhoto = sysImage;
    this.takePhoto = false;
    this.uploadImg(sysImage);
  }

  public uploadImg(sysImage) {

    console.log("REPLACE MEDIA ID ", this.replaceMediaId);

    this.imageCaptured.emit({image: sysImage, uInspectSectionId: this.section.id, id: this.replaceMediaId, synced: false});
  }

  setOption(currentQuestion: UInspectQuestionSyncDTO, option: UInspectQuestionOptionDTO) {
    this.stepEvent.emit({action: "setAnswer", sectionId: this.section.id, questionId: currentQuestion.id, option, synced: false});
    this.currentQuestion.answer.answerOptionId = option.id;
  }

  setFreeText(currentQuestion: UInspectQuestionSyncDTO, value: string) {
    this.stepEvent.emit({action: "setAnswer", value, sectionId: this.section.id, questionId: currentQuestion.id, synced: false});
  }

  sortedOptions(options: UInspectQuestionOptionDTO[]) {

    return options
      .filter(x => x.statusId === StatusEnum.Active)
      .sort((a, b) => a.sequence - b.sequence);
  }

  retakePhoto() {
    this.replaceMediaId = this.currentMedia.id;
    this.takePhoto = true;
  }

  uploadPhoto() {

  }

  onFileSelected(event) {

    const file: File = event.target.files[0];
    let fileName = "";

    if (file) {

      const reader = new FileReader();
      reader.readAsDataURL(file);

      reader.onload = () => {
        const sysImage = reader.result as string;
        this.currentPhoto = sysImage;
        this.uploadImg(sysImage);
        this.takePhoto = false;
      };
      fileName = file.name;
    }
  }
}
