:root {

  --font1: "Prompt";
  --font2: "Prompt";
  --defaultFont: var(--font1);
  --widgetBorderRadius: 8px;
  --imageOutlineColour: var(--widgetBorderColour);

  /* Palette */
  --colour1: #000;
  --colour2: #FF497B;
  --colour3: #fff;
  --inputBorderColour: #b3cad6;
  --primaryColour: #000;

  --textLabelColour: #667A88;
  --navbarBgColour: #fff;
  --textColour: var(--colour1);
  --navbarTextColour: var(--textColour);
  --bgColour: #f0f1f2;
  --headerHeight: 63px;
  --attentionBoxBackgroundColour: #dee2e6;
  --dropdownBackgroundColour: #fff;
  --dropdownItemTextColour: var(--colour1);
  --dropdownHoverItemBackgroundColour: var(--colour1);
  --dropdownHoverItemTextColour: #fff;
  --topbarIconColour: #8f49ff;
  --errorColour: #c00;
  --vrmBackgroundColour: #fff6c3;
  --sideNavBackgroundColour: #fff;
  --sideNavItemBackgroundColour: #8f49ff;
  --sideNavHoverTextColour: var(--colour1);
  --sideNavHoverBackgroundColour: #eee;
  --sideNavTextColour: var(--colour1);
  --linkColour: var(--colour1);
  --primaryButtonColour: #8F49FF;
  --secondaryButtonColour: #000;
  --switchColour: #8f49ff;


  --successColour: forestgreen;
  --buyNowColour: #314d5f;
  --timedSaleColour: #0e2839;
  --managedSaleColour: #1e3b4e;
  --underwriteSaleColour: #476375;

}

.device {
  --headerHeight: 96px;
  --headerPaddingTop: 30px;
}


