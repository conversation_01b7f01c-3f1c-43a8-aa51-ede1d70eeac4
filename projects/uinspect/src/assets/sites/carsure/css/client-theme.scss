@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-Thin.ttf) format("truetype");
  font-weight: 100;
  font-display: swap;
}

@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-ExtraLight.ttf) format("truetype");
  font-weight: 200;
  font-display: swap;
}

@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-Light.ttf) format("truetype");
  font-weight: 300;
  font-display: swap;
}

@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-Regular.ttf) format("truetype");
  font-weight: 400;
  font-display: swap;
}

@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-Medium.ttf) format("truetype");
  font-weight: 500;
  font-display: swap;
}

@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-SemiBold.ttf) format("truetype");
  font-weight: 600;
  font-display: swap;
}

@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-Bold.ttf) format("truetype");
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-ExtraBold.ttf) format("truetype");
  font-weight: 800;
  font-display: swap;
}

@font-face {
  font-family: "Prompt";
  src: url(/assets/sites/carsure/fonts/Prompt-Black.ttf) format("truetype");
  font-weight: 900;
  font-display: swap;
}

#site-carsure {

  #admin-side-nav .side-nav .collapsible .card .card-header a h5:hover {

    color: #000 !important;

  }

  .btn-group .btn.active {
    color: #fff !important;

  }

  .page-footer {
    background: #777;
    color: rgba(255,255,255, 0.7);

    a { color: #fff !important; }

    .footer-size {
      padding-top: 16px !important;
    }
  }

  #main-content { background-color: #fff; }

  #homepage {

    .header-line-1, .header-line-2, .header-line-3 {
      color: var(--colour1) !important;
    }

    .homepage-car {
      width: 100%;
      height: 100%;
      background-image: url("/assets/sites/toyotagb/images/toyota.png");
      background-repeat: no-repeat;
      background-size: contain;
    }

  }

  .side-nav {


    .nav-item.selected {
      background-color: var(--sideNavItemBackgroundColour) !important;
      color: #fff;


      a, a:visited {
        color: #fff !important;
      }

      a:hover { color: var(--colour1) !important; }
    }
  }

  .btn-primary {
    color: #fff;

    &.btn-xs, &.btn-sm, &.btn {
      font-weight: 500 !important;
    }
  }

  .btn-secondary {
    color: #fff;
  }

  h1.page-header {

    .title {
      font-weight: 500;
      font-size: 1.3rem;
    }

  }


  #homepage {

    .why-us {
      color: #fff;
    }

    .block-2 {

      background: linear-gradient(180deg, rgba(26, 26, 66, 1) 0%, rgba(75, 75, 106, 1) 100%);

    }

  }

  body {
    font-family: "Prompt", sans-serif;
  }

  .btn-sign-up {

    background-color: #8F49FF !important;
    color: #fff !important;

  }

  .btn-login {

    border: 1px solid #8F49FF !important;
    color: #8F49FF !important;
    background-color: #fff;

  }

  .navbar-brand {

    background-image: url('/assets/sites/carsure/images/logo.svg');
    background-repeat: no-repeat;
    background-size: contain;
    height: 46px;
    width: 200px;
    padding-bottom: 0px;
    padding-top: 0px;
  }

  .navbar {
    border-bottom: 1px solid #b3cad6;
  }

  .email-link .email-count {
    color: #fff;
  }

  .search-box {

    border: 1px solid var(--inputBorderColour);

    input::placeholder {
      color: var(--inputBorderColour) !important;
    }

    border-radius: 17px;

    .search-submit {
      background-color: var(--colour1);
      border-radius: 0 17px 17px 0;
      color: #fff;
    }
  }

  .undernav {

    a.undernav-item {
      font-weight: 500 !important;
    }
  }

  #dashboard-header-widget {

    background: linear-gradient(180deg, rgba(26, 26, 66, 1) 0%, rgba(75, 75, 106, 1) 100%);

    width: 100vw;
    margin-left: calc(50% - 50vw);

    .welcome-text, .sub-text {
      color: #fff;

      margin-left: auto;
      margin-right: auto;
      padding-left: 10px;
      padding-right: 10px;
    }

    border-radius: 0px !important;
  }

  .stats-inner {

    &.attention-border {

      outline-color: transparent !important;

      background-color: var(--colour1);

      .number {
        color: var(--colour2);
      }

      .title {
        color: var(--colour2);
      }

      .subtitle {
        color: #fff;
      }

      .stats-icon {
        background-color: #fff;

        .error-colour {
          color: var(--colour2);
        }
      }

    }

    .stats-icon {
      background-color: #f0f4f7;
      color: #a0bdcc;
      height: 60px;
      width: 60px;
      border-radius: 50%;

      i {
        line-height: 60px;
      }

      text-align: center;
    }


    .number {
      font-weight: 300;
      font-size: 50px;
    }

    .title {
      text-align: center;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .subtitle {
      text-align: center;
      line-height: 15px;
      color: var(--colour1);
    }
  }
}

@media (min-width: 768px) {
  .welcome-text, .sub-text {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .welcome-text, .sub-text {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .welcome-text, .sub-text {
    max-width: 1140px;
  }
}

