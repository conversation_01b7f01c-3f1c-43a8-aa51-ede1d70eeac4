@import url('https://fonts.googleapis.com/css2?family=Barlow:wght@300;700&display=swap');

#site-edenmotorsUInspect {

  .sitelogo {
    background-image: url("/assets/sites/edenmotorsUInspect/images/edenlogo.png");
    background-size: contain;
    background-repeat: no-repeat;
    height: 50px;
    width: 300px;
  }

  .modal-body .form-control {
    background-color: #fff !important;
  }

  .modal-header { color: #fff !important }

  .btn.btn-secondary:hover { color: var(--colour3) !important; }

  .appraisal-home {
    background-image: url("/assets/sites/edenmotorsUInspect/images/getImage.png");
  }

  .title-main, .title-main-2 {
    font-family: var(--font3);
    text-transform: uppercase;
  }

  .title-main {
    padding-top: 40px;
    font-size: 42px;
    line-height: 47px;
    letter-spacing: -1px;
  }

  .form-container {
    padding-top: 40px;
  }

  footer {
    color: #eee !important;
  }

  .footer-item:not(:last-child) {
    border-right: 1px solid #666 !important;
  }

  .btn.btn-primary {

    background-color: #ed2324;
    border: 0 !important;

  }

  .btn.btn-primary-outline {

    border: 1px solid #ed2324;
    color: #ed2324;

  }

  .btn-group .btn.active { color: var(--colour3) !important; border: 1px solid var(--colour3) !important; }

  .selected { background-color: #eee !important; }

  .form-control {
    border: 1px solid #ced4da !important;

  }
}

@media (max-width: 800px) {

  .title-main {
    font-size: 40px !important;
    line-height: 40px !important;
    padding-top: 30px;
    letter-spacing: -1px;
  }

  .form-container {
    padding-top: 0px !important;

  }


}
