#site-sturgessUInspect {

  font-family: <PERSON><PERSON>s, sans-serif;

  .signup-link .nav-link, .login-link .nav-link {
    font-weight: 700 !important;
  }

  .btn.btn-primary {
    color: #fff;
  }

  .btn-group .btn.active {
    color: #fff !important
  }

  .badge-primary { background-color: #888; }

  .btn.btn-primary-outline { border: 1px solid var(--linkColour) !important; }

  .sitelogo {
    background-image: url("/assets/sites/sturgessUInspect/images/sturgessInspect.png");
    height: 50px;
    width: 190px;
  }

  .header-block {
    .header-line-1 { color: #333; }
    .header-line-2 { color: #333; }
    .header-line-3 { color: var(--colour4); }
  }

  nav .nav-link .user-name {
    color: #fff !important;
  }

  .modal-body .form-control {
    background-color: #fff !important;
    &:focus {
      border: 1px solid #000 !important;
    }
  }

  .modal-header { background-color: #333; color: #fff; }


  .btn-secondary:hover {

    border: 1px solid var(--colour4);
    background-color: var(--colour4);
    color: var(--colour4);

  }

  .homepage-car {
    background-image: url("/assets/sites/sturgessUInspect/images/car.png");
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
  }

  footer {
    color: #bbb !important;

    &.page-footer a, &.page-footer a:visited {
      color: #fff !important;
    }
  }

  .btn-login {
    border: 2px solid var(--colour4) !important;
    background-color: transparent;
    color: var(--colour4);
  }

  .btn-sign-up {
    background-color: var(--colour4);
    color: #fff;
  }

  .selected { background-color: #eee !important;}
}
