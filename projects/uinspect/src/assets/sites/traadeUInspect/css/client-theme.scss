@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@400;700&display=swap');

#site-traadeUInspect {

  #homepage {

    .container {
      width: 100% !important;
    }

    background-color: #065cac;

    .header-line-1 {
      color: var(--textColour);
    }

    .header-line-2 {
      color: var(--textColour);
    }

    .header-line-3 {
      color: var(--colour1);
    }

    .btn-login {
      background-color: var(--colour2);
      color: #fff;
      border: 0px solid !important;
    }

    .btn-sign-up {
      background-color: var(--colour1);
      color: #fff;
      border: 0px solid !important;
    }

  }

  .sitelogo {
    background-image: url('/assets/sites/traadeUInspect/images/traade-logo.png') !important;
    background-repeat: no-repeat !important;
    background-size: contain !important;
    background-position: center center !important;
    height: 30px !important;
    width: 163px !important;
    margin-right: 0 !important;
    padding-bottom: 0 !important;
  }

  .btn-primary, .btn.btn-primary, .btn-secondary, .btn.btn-secondary {
    color: #fff;
  }

  .btn-outline-primary, .btn-primary-outline, .btn-secondary-outline {
    border: 1px solid var(--primaryButtonColour) !important;
    color: var(--primaryButtonColour) !important;

    &:hover {
      color: var(--colour8) !important;
      background-color: var(--primaryButtonColour) !important;
    }

    &.active {
      color: var(--colour8) !important;
      background-color: var(--primaryButtonColour) !important;
    }
  }

  .btn.btn-primary-outline:hover {
    color: black;
  }

  .selected {
    background-color: #4F5267 !important;
    color: #fff !important;
  }

  .nav-item.selected a {
    color: #fff !important;
  }

  #admin-side-nav .side-nav .collapsible .card .card-header a h5:hover {
    color: var(--adminSideNavHoverColor) !important;
  }

  .btn-group .btn.active {
    color: #fff !important;
  }

  .modal-header {
    color: #fff !important;

  }

  .selected {
    background-color: var(--colour1) !important;
  }
}

@media (min-width: 1200px) {
  .vehicle-table {
    width: 100% !important;
  }
}

