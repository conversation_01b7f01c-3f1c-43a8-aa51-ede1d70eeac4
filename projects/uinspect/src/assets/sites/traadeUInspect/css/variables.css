:root {

  --font1: "Raleway";
  --font2: "Raleway";
  --defaultFont: var(--font1);
  --widgetBorderRadius: 8px;
  --imageOutlineColour: var(--widgetBorderColour);
  --footerHeight: 118px;
  --headerHeight: 56px;

  /* Palette */
  --colour1: #065cac;
  --colour2: #D21A20;
  --colour3: #ffffff;
  --colour4: #222222;
  --colour5: #ffffff;
  --colour6: #ffffff;
  --colour7: #ccc;
  --colour8: #ffffff;

  --navbarBgColour: var(--colour5);
  --textColour: var(--colour4);
  --navbarTextColour: var(--colour1);
  --bgColour: #f0f1f2;
  --placeholderColour: #bbb;
  --actionColour: var(--colour1);

  --primaryButtonColour: var(--colour2);
  --secondaryButtonColour: var(--colour1);
  --attentionBoxBackgroundColour: #eaf3f8;
  --footerBackgroundColour: #fff;
  --textLabelColour: var(--textColour);
  --linkColour: var(--colour1);
  --dropdownBackgroundColour: #fff;
  --dropdownItemTextColour: var(--colour1);
  --dropdownHoverItemBackgroundColour: var(--colour1);
  --dropdownHoverItemTextColour: #fff;
  --sideNavBackgroundColour: #fff;
  --adminSideNav: #fff;

  --buyNowColour: var(--colour1);
  --timedSaleColour: #51ADDF;
  --managedSaleColour: var(--colour2);
  --sideNavTextColour: var(--colour4);
  --sideNavHoverTextColour: var(--colour4);
  --primaryButtonColour: var(--colour1);
  --switchColour: var(--colour2);
  --inputBorderColour: var(--colour7);

}

.device {
  --headerHeight: 96px;
  --headerPaddingTop: 30px;
}


