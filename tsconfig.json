{"compileOnSave": true, "compilerOptions": {"baseUrl": "./", "paths": {"crypto": ["../../node_modules/crypto-js"]}, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "module": "es2020", "moduleResolution": "node", "importHelpers": true, "target": "ES2022", "lib": ["es2020", "dom"], "useDefineForClassFields": false}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true}}